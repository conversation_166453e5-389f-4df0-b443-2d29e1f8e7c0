﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddSeatReservationAndChuyenXeStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "TrangThaiChuyenXe",
                table: "ChuyenXes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "SeatReservations",
                columns: table => new
                {
                    ReservationId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChuyenXeId = table.Column<int>(type: "int", nullable: false),
                    ChoNgoiId = table.Column<int>(type: "int", nullable: false),
                    SessionId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UserEmail = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ReservedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeatReservations", x => x.ReservationId);
                    table.ForeignKey(
                        name: "FK_SeatReservations_ChoNgois_ChoNgoiId",
                        column: x => x.ChoNgoiId,
                        principalTable: "ChoNgois",
                        principalColumn: "ChoNgoiId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SeatReservations_ChuyenXes_ChuyenXeId",
                        column: x => x.ChuyenXeId,
                        principalTable: "ChuyenXes",
                        principalColumn: "ChuyenXeId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 1,
                column: "MatKhau",
                value: "e86f78a8a3caf0b60d8e74e5942aa6d86dc150cd3c03338aef25b7d2d7e3acc7");

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 2,
                column: "MatKhau",
                value: "3e7c19576488862816f13b512cacf3e4ba97dd97243ea0bd6a2ad1642d86ba72");

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 3,
                column: "MatKhau",
                value: "3e7c19576488862816f13b512cacf3e4ba97dd97243ea0bd6a2ad1642d86ba72");

            migrationBuilder.CreateIndex(
                name: "IX_SeatReservations_ChoNgoiId",
                table: "SeatReservations",
                column: "ChoNgoiId");

            migrationBuilder.CreateIndex(
                name: "IX_SeatReservations_ChuyenXeId_ChoNgoiId_IsActive",
                table: "SeatReservations",
                columns: new[] { "ChuyenXeId", "ChoNgoiId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SeatReservations_ExpiresAt",
                table: "SeatReservations",
                column: "ExpiresAt");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SeatReservations");

            migrationBuilder.DropColumn(
                name: "TrangThaiChuyenXe",
                table: "ChuyenXes");

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 1,
                column: "MatKhau",
                value: "Admin@123");

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 2,
                column: "MatKhau",
                value: "User@123");

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 3,
                column: "MatKhau",
                value: "User@123");
        }
    }
}
