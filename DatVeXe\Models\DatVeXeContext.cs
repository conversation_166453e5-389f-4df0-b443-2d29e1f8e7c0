using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace DatVeXe.Models
{
    public class DatVeXeContext : DbContext
    {
        public DatVeXeContext(DbContextOptions<DatVeXeContext> options) : base(options) { }

        public DbSet<Xe> Xes { get; set; }
        public DbSet<ChuyenXe> ChuyenXes { get; set; }
        public DbSet<Ve> Ves { get; set; }
        public DbSet<NguoiDung> NguoiDungs { get; set; }
        public DbSet<TuyenDuong> TuyenDuongs { get; set; }
        public DbSet<ChoNgoi> ChoNgois { get; set; }
        public DbSet<SeatReservation> SeatReservations { get; set; }
        public DbSet<ThanhToan> ThanhToans { get; set; }
        public DbSet<DanhBaHanhKhach> DanhBaHanhKhachs { get; set; }
        public DbSet<DanhGiaChuyenDi> DanhGiaChuyenDis { get; set; }
        public DbSet<KhuyenMai> KhuyenMais { get; set; }
        public DbSet<TaiXe> TaiXes { get; set; }
        public DbSet<LichSuKhuyenMai> LichSuKhuyenMais { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Cấu hình relationships
            modelBuilder.Entity<Ve>()
                .HasOne(v => v.ChuyenXe)
                .WithMany(c => c.Ves)
                .HasForeignKey(v => v.ChuyenXeId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Ve>()
                .HasOne(v => v.NguoiDung)
                .WithMany(n => n.Ves)
                .HasForeignKey(v => v.NguoiDungId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Ve>()
                .HasOne(v => v.ChoNgoi)
                .WithMany(c => c.Ves)
                .HasForeignKey(v => v.ChoNgoiId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ChuyenXe>()
                .HasOne(c => c.TuyenDuong)
                .WithMany(t => t.ChuyenXes)
                .HasForeignKey(c => c.TuyenDuongId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ChuyenXe>()
                .HasOne(c => c.TaiXe)
                .WithMany(t => t.ChuyenXes)
                .HasForeignKey(c => c.TaiXeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ChoNgoi>()
                .HasOne(c => c.Xe)
                .WithMany(x => x.ChoNgois)
                .HasForeignKey(c => c.XeId)
                .OnDelete(DeleteBehavior.NoAction);

            // Cấu hình precision cho decimal
            modelBuilder.Entity<ChuyenXe>()
                .Property(c => c.Gia)
                .HasPrecision(18, 2);

            modelBuilder.Entity<Ve>()
                .Property(v => v.GiaVe)
                .HasPrecision(18, 2);

            modelBuilder.Entity<ThanhToan>()
                .Property(t => t.SoTien)
                .HasPrecision(18, 2);

            // Cấu hình precision cho KhuyenMai
            modelBuilder.Entity<KhuyenMai>()
                .Property(k => k.GiaTri)
                .HasPrecision(18, 2);

            modelBuilder.Entity<KhuyenMai>()
                .Property(k => k.GiaTriToiDa)
                .HasPrecision(18, 2);

            modelBuilder.Entity<KhuyenMai>()
                .Property(k => k.GiaTriDonHangToiThieu)
                .HasPrecision(18, 2);

            // Cấu hình unique constraints
            modelBuilder.Entity<ChoNgoi>()
                .HasIndex(c => new { c.XeId, c.SoGhe })
                .IsUnique();

            modelBuilder.Entity<TuyenDuong>()
                .HasIndex(t => new { t.DiemDi, t.DiemDen })
                .IsUnique();

            // Cấu hình SeatReservation
            modelBuilder.Entity<SeatReservation>()
                .HasOne(s => s.ChuyenXe)
                .WithMany()
                .HasForeignKey(s => s.ChuyenXeId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình ThanhToan
            modelBuilder.Entity<ThanhToan>()
                .HasOne(t => t.Ve)
                .WithMany()
                .HasForeignKey(t => t.VeId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình indexes cho ThanhToan
            modelBuilder.Entity<ThanhToan>()
                .HasIndex(t => t.MaGiaoDich)
                .IsUnique();

            modelBuilder.Entity<ThanhToan>()
                .HasIndex(t => new { t.VeId, t.TrangThai });

            // Cấu hình indexes cho SeatReservation
            modelBuilder.Entity<SeatReservation>()
                .HasIndex(r => new { r.ChuyenXeId, r.ChoNgoiId, r.IsActive });

            modelBuilder.Entity<SeatReservation>()
                .HasIndex(r => r.ExpiresAt);

            modelBuilder.Entity<SeatReservation>()
                .HasOne(s => s.ChoNgoi)
                .WithMany()
                .HasForeignKey(s => s.ChoNgoiId)
                .OnDelete(DeleteBehavior.Cascade);

            // Index cho SeatReservation để tìm kiếm nhanh
            modelBuilder.Entity<SeatReservation>()
                .HasIndex(s => new { s.ChuyenXeId, s.ChoNgoiId, s.IsActive });

            modelBuilder.Entity<SeatReservation>()
                .HasIndex(s => s.ExpiresAt);

            // Cấu hình DanhBaHanhKhach
            modelBuilder.Entity<DanhBaHanhKhach>()
                .HasOne(d => d.NguoiDung)
                .WithMany()
                .HasForeignKey(d => d.NguoiDungId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DanhBaHanhKhach>()
                .HasIndex(d => new { d.NguoiDungId, d.SoDienThoai });

            // Cấu hình DanhGiaChuyenDi
            modelBuilder.Entity<DanhGiaChuyenDi>()
                .HasOne(d => d.Ve)
                .WithMany()
                .HasForeignKey(d => d.VeId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<DanhGiaChuyenDi>()
                .HasOne(d => d.NguoiDung)
                .WithMany()
                .HasForeignKey(d => d.NguoiDungId)
                .OnDelete(DeleteBehavior.Cascade);

            // Cấu hình KhuyenMai
            modelBuilder.Entity<KhuyenMai>()
                .HasIndex(k => k.MaKhuyenMai)
                .IsUnique();

            modelBuilder.Entity<KhuyenMai>()
                .HasIndex(k => new { k.NgayBatDau, k.NgayKetThuc });

            // Cấu hình TaiXe
            modelBuilder.Entity<TaiXe>()
                .HasIndex(t => t.SoBangLai)
                .IsUnique();

            modelBuilder.Entity<TaiXe>()
                .HasIndex(t => t.CMND)
                .IsUnique();

            modelBuilder.Entity<TaiXe>()
                .HasIndex(t => t.SoDienThoai);

            // Cấu hình LichSuKhuyenMai
            modelBuilder.Entity<LichSuKhuyenMai>()
                .HasOne(l => l.KhuyenMai)
                .WithMany()
                .HasForeignKey(l => l.KhuyenMaiId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<LichSuKhuyenMai>()
                .HasOne(l => l.NguoiDung)
                .WithMany()
                .HasForeignKey(l => l.NguoiDungId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<LichSuKhuyenMai>()
                .HasOne(l => l.Ve)
                .WithMany()
                .HasForeignKey(l => l.VeId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<LichSuKhuyenMai>()
                .Property(l => l.GiaTriGiam)
                .HasPrecision(18, 2);

            modelBuilder.Entity<LichSuKhuyenMai>()
                .HasIndex(l => new { l.KhuyenMaiId, l.ThoiGianSuDung });

            modelBuilder.Entity<LichSuKhuyenMai>()
                .HasIndex(l => l.ThoiGianSuDung);

            // Seed dữ liệu mẫu
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed NguoiDung với mật khẩu đã hash
            modelBuilder.Entity<NguoiDung>().HasData(
                new NguoiDung
                {
                    NguoiDungId = 1,
                    HoTen = "Administrator",
                    Email = "<EMAIL>",
                    MatKhau = HashPassword("Admin@123"), // Hash password
                    SoDienThoai = "0901234567",
                    DiaChi = "123 Nguyễn Huệ, Q1, TP.HCM",
                    NgaySinh = new DateTime(1990, 1, 1),
                    GioiTinh = "Nam",
                    LaAdmin = true,
                    TrangThaiHoatDong = true,
                    NgayDangKy = new DateTime(2024, 1, 1),
                    LanDangNhapCuoi = new DateTime(2024, 12, 1)
                },
                new NguoiDung
                {
                    NguoiDungId = 2,
                    HoTen = "Nguyễn Văn An",
                    Email = "<EMAIL>",
                    MatKhau = HashPassword("User@123"),
                    SoDienThoai = "0987654321",
                    DiaChi = "456 Lê Lợi, Q1, TP.HCM",
                    NgaySinh = new DateTime(1995, 5, 15),
                    GioiTinh = "Nam",
                    LaAdmin = false,
                    TrangThaiHoatDong = true,
                    NgayDangKy = new DateTime(2024, 11, 1)
                },
                new NguoiDung
                {
                    NguoiDungId = 3,
                    HoTen = "Trần Thị Bình",
                    Email = "<EMAIL>",
                    MatKhau = HashPassword("User@123"),
                    SoDienThoai = "0912345678",
                    DiaChi = "789 Trần Hưng Đạo, Q5, TP.HCM",
                    NgaySinh = new DateTime(1992, 8, 20),
                    GioiTinh = "Nữ",
                    LaAdmin = false,
                    TrangThaiHoatDong = true,
                    NgayDangKy = new DateTime(2024, 11, 15)
                }
            );

            // Seed TuyenDuong
            modelBuilder.Entity<TuyenDuong>().HasData(
                new TuyenDuong
                {
                    TuyenDuongId = 1,
                    TenTuyen = "TP.HCM - Nha Trang",
                    DiemDi = "TP.HCM",
                    DiemDen = "Nha Trang",
                    KhoangCach = 450,
                    ThoiGianDuKien = new TimeSpan(8, 30, 0),
                    MoTa = "Tuyến đường du lịch nổi tiếng, phong cảnh đẹp",
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 10, 1)
                },
                new TuyenDuong
                {
                    TuyenDuongId = 2,
                    TenTuyen = "TP.HCM - Đà Lạt",
                    DiemDi = "TP.HCM",
                    DiemDen = "Đà Lạt",
                    KhoangCach = 300,
                    ThoiGianDuKien = new TimeSpan(6, 0, 0),
                    MoTa = "Tuyến đường lên thành phố ngàn hoa",
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 10, 1)
                },
                new TuyenDuong
                {
                    TuyenDuongId = 3,
                    TenTuyen = "TP.HCM - Vũng Tàu",
                    DiemDi = "TP.HCM",
                    DiemDen = "Vũng Tàu",
                    KhoangCach = 125,
                    ThoiGianDuKien = new TimeSpan(2, 30, 0),
                    MoTa = "Tuyến đường ngắn đến biển Vũng Tàu",
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 10, 1)
                },
                new TuyenDuong
                {
                    TuyenDuongId = 4,
                    TenTuyen = "TP.HCM - Cần Thơ",
                    DiemDi = "TP.HCM",
                    DiemDen = "Cần Thơ",
                    KhoangCach = 170,
                    ThoiGianDuKien = new TimeSpan(3, 30, 0),
                    MoTa = "Tuyến đường đến miền Tây sông nước",
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 10, 1)
                },
                new TuyenDuong
                {
                    TuyenDuongId = 5,
                    TenTuyen = "Hà Nội - Hạ Long",
                    DiemDi = "Hà Nội",
                    DiemDen = "Hạ Long",
                    KhoangCach = 165,
                    ThoiGianDuKien = new TimeSpan(3, 0, 0),
                    MoTa = "Tuyến đường đến vịnh Hạ Long",
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 10, 1)
                }
            );

            // Seed Xe
            modelBuilder.Entity<Xe>().HasData(
                new Xe { XeId = 1, BienSo = "51A-12345", LoaiXe = "Giường nằm 40 chỗ", SoGhe = 40, NhaXe = "Phương Trang", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 2, BienSo = "51B-67890", LoaiXe = "Limousine 20 chỗ", SoGhe = 20, NhaXe = "Mai Linh", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 3, BienSo = "51C-11111", LoaiXe = "Ghế ngồi 45 chỗ", SoGhe = 45, NhaXe = "Thành Bưởi", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 4, BienSo = "51D-22222", LoaiXe = "Giường nằm 36 chỗ", SoGhe = 36, NhaXe = "Phương Trang", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 5, BienSo = "51E-33333", LoaiXe = "Limousine 16 chỗ", SoGhe = 16, NhaXe = "Hoàng Long", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 6, BienSo = "30A-44444", LoaiXe = "Ghế ngồi 50 chỗ", SoGhe = 50, NhaXe = "Mai Linh", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 7, BienSo = "30B-55555", LoaiXe = "Giường nằm 32 chỗ", SoGhe = 32, NhaXe = "Thành Bưởi", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) },
                new Xe { XeId = 8, BienSo = "30C-66666", LoaiXe = "Limousine 24 chỗ", SoGhe = 24, NhaXe = "Hoàng Long", TrangThaiHoatDong = true, NgayTao = new DateTime(2024, 1, 1) }
            );

            // Seed TaiXe
            modelBuilder.Entity<TaiXe>().HasData(
                new TaiXe
                {
                    TaiXeId = 1,
                    HoTen = "Nguyễn Văn Tài",
                    SoDienThoai = "0901111111",
                    DiaChi = "123 Lê Lợi, Q1, TP.HCM",
                    NgaySinh = new DateTime(1985, 3, 15),
                    GioiTinh = "Nam",
                    CMND = "025123456789",
                    SoBangLai = "BL001234567",
                    LoaiBangLai = LoaiBangLai.D,
                    NgayCapBangLai = new DateTime(2010, 5, 20),
                    NgayHetHanBangLai = new DateTime(2030, 5, 20),
                    KinhNghiem = 14,
                    TrangThai = TrangThaiTaiXe.HoatDong,
                    NgayVaoLam = new DateTime(2020, 1, 15),
                    NgayTao = new DateTime(2024, 1, 1)
                },
                new TaiXe
                {
                    TaiXeId = 2,
                    HoTen = "Trần Minh Đức",
                    SoDienThoai = "0902222222",
                    DiaChi = "456 Nguyễn Huệ, Q1, TP.HCM",
                    NgaySinh = new DateTime(1988, 7, 22),
                    GioiTinh = "Nam",
                    CMND = "025987654321",
                    SoBangLai = "BL002345678",
                    LoaiBangLai = LoaiBangLai.D,
                    NgayCapBangLai = new DateTime(2012, 8, 10),
                    NgayHetHanBangLai = new DateTime(2032, 8, 10),
                    KinhNghiem = 12,
                    TrangThai = TrangThaiTaiXe.HoatDong,
                    NgayVaoLam = new DateTime(2021, 3, 10),
                    NgayTao = new DateTime(2024, 1, 1)
                },
                new TaiXe
                {
                    TaiXeId = 3,
                    HoTen = "Lê Hoàng Nam",
                    SoDienThoai = "0903333333",
                    DiaChi = "789 Trần Hưng Đạo, Q5, TP.HCM",
                    NgaySinh = new DateTime(1990, 12, 5),
                    GioiTinh = "Nam",
                    CMND = "025456789123",
                    SoBangLai = "BL003456789",
                    LoaiBangLai = LoaiBangLai.D,
                    NgayCapBangLai = new DateTime(2015, 2, 15),
                    NgayHetHanBangLai = new DateTime(2035, 2, 15),
                    KinhNghiem = 9,
                    TrangThai = TrangThaiTaiXe.HoatDong,
                    NgayVaoLam = new DateTime(2022, 6, 1),
                    NgayTao = new DateTime(2024, 1, 1)
                }
            );

            // Seed KhuyenMai
            modelBuilder.Entity<KhuyenMai>().HasData(
                new KhuyenMai
                {
                    KhuyenMaiId = 1,
                    TenKhuyenMai = "Giảm giá mùa hè",
                    MaKhuyenMai = "SUMMER2024",
                    MoTa = "Giảm 15% cho tất cả chuyến xe trong mùa hè",
                    LoaiKhuyenMai = LoaiKhuyenMai.GiamPhanTram,
                    GiaTri = 15,
                    GiaTriToiDa = 100000,
                    GiaTriDonHangToiThieu = 200000,
                    NgayBatDau = new DateTime(2024, 6, 1),
                    NgayKetThuc = new DateTime(2024, 8, 31),
                    SoLuongToiDa = 1000,
                    SoLuongDaSuDung = 45,
                    SoLanSuDungToiDa = 1,
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 5, 15)
                },
                new KhuyenMai
                {
                    KhuyenMaiId = 2,
                    TenKhuyenMai = "Khuyến mãi sinh viên",
                    MaKhuyenMai = "STUDENT50",
                    MoTa = "Giảm 50,000 VNĐ cho sinh viên",
                    LoaiKhuyenMai = LoaiKhuyenMai.GiamSoTien,
                    GiaTri = 50000,
                    GiaTriDonHangToiThieu = 150000,
                    NgayBatDau = new DateTime(2024, 1, 1),
                    NgayKetThuc = new DateTime(2024, 12, 31),
                    SoLuongToiDa = 500,
                    SoLuongDaSuDung = 123,
                    SoLanSuDungToiDa = 2,
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 1, 1)
                },
                new KhuyenMai
                {
                    KhuyenMaiId = 3,
                    TenKhuyenMai = "Khuyến mãi cuối tuần",
                    MaKhuyenMai = "WEEKEND20",
                    MoTa = "Giảm 20% cho các chuyến xe cuối tuần",
                    LoaiKhuyenMai = LoaiKhuyenMai.GiamPhanTram,
                    GiaTri = 20,
                    GiaTriToiDa = 80000,
                    GiaTriDonHangToiThieu = 100000,
                    NgayBatDau = new DateTime(2024, 12, 1),
                    NgayKetThuc = new DateTime(2024, 12, 31),
                    SoLuongToiDa = 200,
                    SoLuongDaSuDung = 67,
                    SoLanSuDungToiDa = 1,
                    TrangThaiHoatDong = true,
                    NgayTao = new DateTime(2024, 11, 25)
                },
                new KhuyenMai
                {
                    KhuyenMaiId = 4,
                    TenKhuyenMai = "Khuyến mãi Tết 2025",
                    MaKhuyenMai = "TET2025",
                    MoTa = "Giảm 25% dịp Tết Nguyên Đán",
                    LoaiKhuyenMai = LoaiKhuyenMai.GiamPhanTram,
                    GiaTri = 25,
                    GiaTriToiDa = 150000,
                    GiaTriDonHangToiThieu = 300000,
                    NgayBatDau = new DateTime(2025, 1, 20),
                    NgayKetThuc = new DateTime(2025, 2, 10),
                    SoLuongToiDa = 2000,
                    SoLuongDaSuDung = 0,
                    SoLanSuDungToiDa = 1,
                    TrangThaiHoatDong = false, // Chưa kích hoạt
                    NgayTao = new DateTime(2024, 12, 1)
                },
                new KhuyenMai
                {
                    KhuyenMaiId = 5,
                    TenKhuyenMai = "Khuyến mãi khách hàng VIP",
                    MaKhuyenMai = "VIP100",
                    MoTa = "Giảm 100,000 VNĐ cho khách hàng VIP",
                    LoaiKhuyenMai = LoaiKhuyenMai.GiamSoTien,
                    GiaTri = 100000,
                    GiaTriDonHangToiThieu = 500000,
                    NgayBatDau = new DateTime(2024, 11, 1),
                    NgayKetThuc = new DateTime(2024, 11, 30),
                    SoLuongToiDa = 50,
                    SoLuongDaSuDung = 50,
                    SoLanSuDungToiDa = 1,
                    TrangThaiHoatDong = false, // Đã hết hạn
                    NgayTao = new DateTime(2024, 10, 15)
                }
            );
        }

        // Helper method để hash password
        private static string HashPassword(string password)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(password));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
    }
}