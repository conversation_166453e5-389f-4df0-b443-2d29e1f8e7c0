@model DatVeXe.Models.KhuyenMai

@{
    ViewData["Title"] = "Tạo khuyến mãi mới";
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="text-dark mb-1">
                    <i class="fas fa-plus-circle text-primary me-2"></i>
                    Tạo khuyến mãi mới
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a asp-area="Admin" asp-controller="Admin" asp-action="Index" class="text-decoration-none">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" class="text-decoration-none">Quản lý khu<PERSON>ến mãi</a>
                        </li>
                        <li class="breadcrumb-item active">Tạo mới</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Quay lại danh sách
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Alerts -->
@if (ViewBag.TestMessage != null)
{
    <div class="alert alert-info alert-dismissible fade show">
        <i class="fas fa-info-circle me-2"></i>
        @ViewBag.TestMessage
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (ViewBag.Error != null)
{
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        @ViewBag.Error
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<!-- Form -->
<form asp-action="Create" method="post" class="needs-validation" novalidate>
    @Html.AntiForgeryToken()
    <div class="card">
        <div class="card-header">
            <h5>Thông tin khuyến mãi</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="TenKhuyenMai" class="form-label" style="color: black;">Tên khuyến mãi *</label>
                        <input asp-for="TenKhuyenMai" class="form-control" placeholder="Nhập tên khuyến mãi" />
                        <span asp-validation-for="TenKhuyenMai" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="MaKhuyenMai" class="form-label" style="color: black;">Mã khuyến mãi *</label>
                        <input asp-for="MaKhuyenMai" class="form-control" placeholder="Nhập mã khuyến mãi" />
                        <span asp-validation-for="MaKhuyenMai" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label asp-for="MoTa" class="form-label" style="color: black;">Mô tả</label>
                <textarea asp-for="MoTa" class="form-control" rows="3" placeholder="Mô tả khuyến mãi"></textarea>
                <span asp-validation-for="MoTa" class="text-danger"></span>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label asp-for="LoaiKhuyenMai" class="form-label" style="color: black;">Loại khuyến mãi *</label>
                        <select asp-for="LoaiKhuyenMai" class="form-select">
                            <option value="">-- Chọn loại --</option>
                            @if (ViewBag.LoaiKhuyenMaiList != null)
                            {
                                foreach (var item in (Array)ViewBag.LoaiKhuyenMaiList)
                                {
                                    var enumValue = (DatVeXe.Models.LoaiKhuyenMai)item;
                                    <option value="@((int)enumValue)">
                                        @(enumValue == DatVeXe.Models.LoaiKhuyenMai.GiamPhanTram ? "Giảm theo phần trăm" :
                                          enumValue == DatVeXe.Models.LoaiKhuyenMai.GiamSoTien ? "Giảm theo số tiền" : "Miễn phí")
                                    </option>
                                }
                            }
                        </select>
                        <span asp-validation-for="LoaiKhuyenMai" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label asp-for="GiaTri" class="form-label" style="color: black;">Giá trị *</label>
                        <input asp-for="GiaTri" type="number" step="0.01" min="0" class="form-control" placeholder="0" />
                        <span asp-validation-for="GiaTri" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label asp-for="GiaTriToiDa" class="form-label" style="color: black;">Giá trị tối đa (VNĐ)</label>
                        <input asp-for="GiaTriToiDa" type="number" min="0" class="form-control" placeholder="0" />
                        <span asp-validation-for="GiaTriToiDa" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="NgayBatDau" class="form-label" style="color: black;">Ngày bắt đầu *</label>
                        <input asp-for="NgayBatDau" type="datetime-local" class="form-control" />
                        <span asp-validation-for="NgayBatDau" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="NgayKetThuc" class="form-label" style="color: black;">Ngày kết thúc *</label>
                        <input asp-for="NgayKetThuc" type="datetime-local" class="form-control" />
                        <span asp-validation-for="NgayKetThuc" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="SoLuongToiDa" class="form-label" style="color: black;">Số lượng tối đa</label>
                        <input asp-for="SoLuongToiDa" type="number" min="1" class="form-control" placeholder="Không giới hạn" />
                        <span asp-validation-for="SoLuongToiDa" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label asp-for="SoLanSuDungToiDa" class="form-label" style="color: black;">Số lần sử dụng/khách hàng</label>
                        <input asp-for="SoLanSuDungToiDa" type="number" min="1" class="form-control" placeholder="1" />
                        <span asp-validation-for="SoLanSuDungToiDa" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <div class="form-check">
                    <input asp-for="TrangThaiHoatDong" class="form-check-input" type="checkbox" />
                    <label asp-for="TrangThaiHoatDong" class="form-check-label" style="color: black;">
                        Kích hoạt khuyến mãi
                    </label>
                </div>
            </div>

            <div class="text-end">
                <a asp-action="Index" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Tạo khuyến mãi
                </button>
            </div>
        </div>
    </div>
</form>

@section Styles {
    <style>
        .form-label.fw-bold {
            font-weight: 600 !important;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-header {
            border-bottom: 1px solid rgba(255,255,255,.125);
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .form-control, .form-select {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .form-control:focus, .form-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.15s ease-in-out;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
        }
        .input-group-text {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
        }
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 0.5rem;
        }
        .breadcrumb a {
            color: #6CABDD !important;
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: #ffffff !important;
        }
        .breadcrumb .active {
            color: #f8f9fa !important;
            font-weight: 600;
        }
        .alert-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
        }
        .alert-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
            color: white;
        }
        .form-text {
            color: #6c757d !important;
        }
        /* Animation for form elements */
        .form-control, .form-select, .btn {
            transition: all 0.3s ease;
        }
        .card {
            animation: fadeInUp 0.5s ease-out;
        }
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
