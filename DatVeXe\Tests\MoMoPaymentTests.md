# MoMo Payment Integration Tests

## Test Cases

### 1. <PERSON><PERSON> toán thành công
**URL**: `/Checkout/PaymentCallBack?orderId=123_456789&resultCode=0&amount=50000&signature=...`
**Expected**: Redirect đến PaymentSuccess

### 2. <PERSON><PERSON> toán thành công (2-step)
**URL**: `/Checkout/PaymentCallBack?orderId=123_456789&resultCode=9000&amount=50000&signature=...`
**Expected**: Redirect đến PaymentSuccess

### 3. Người dùng hủy thanh toán
**URL**: `/Checkout/PaymentCallBack?orderId=123_456789&resultCode=1006&amount=50000`
**Expected**: Redirect đến PaymentFailure với message "Thanh toán bị hủy"

### 4. Giao dịch bị hủy
**URL**: `/Checkout/PaymentCallBack?orderId=123_456789&resultCode=1003&amount=50000`
**Expected**: Redirect đến PaymentFailure với message "Thanh toán bị hủy"

### 5. Lỗi không đủ tiền
**URL**: `/Checkout/PaymentCallBack?orderId=123_456789&resultCode=1001&amount=50000&signature=...`
**Expected**: Redirect đến PaymentFailure với message chi tiết

### 6. Missing orderId
**URL**: `/Checkout/PaymentCallBack?resultCode=0&amount=50000`
**Expected**: Redirect đến PaymentFailure với error "MISSING_ORDER_ID"

## Manual Testing Steps

1. **Setup**: Tạo một booking với MoMo payment
2. **Test Success**: Modify URL để test các result code thành công
3. **Test Failure**: Modify URL để test các result code thất bại
4. **Test IPN**: Sử dụng Postman để test IPN endpoint
5. **Check Logs**: Verify logs có đầy đủ thông tin debug

## Expected Behavior

- **Result Code 0, 9000**: Thanh toán thành công
- **Result Code 1003, 1006**: Người dùng hủy → Thất bại
- **Other codes**: Thất bại với message chi tiết từ GetMoMoErrorMessage()
- **Invalid signature**: Thất bại với "INVALID_SIGNATURE"
- **Missing orderId**: Thất bại với "MISSING_ORDER_ID"

## Database Updates

Khi thanh toán thành công:
- `ThanhToan.TrangThai` = `ThanhCong`
- `ThanhToan.ThoiGianThanhToan` = Current time
- `Ve.VeTrangThai` = `DaThanhToan`

Khi thanh toán thất bại:
- Không cập nhật database
- Giữ nguyên trạng thái `DangXuLy`
