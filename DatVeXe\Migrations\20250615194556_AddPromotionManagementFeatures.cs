﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddPromotionManagementFeatures : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LichSuKhuyenMais_NguoiDungs_NguoiDungId",
                table: "LichSuKhuyenMais");

            migrationBuilder.DropForeignKey(
                name: "FK_LichSuKhuyenMais_Ves_VeId",
                table: "LichSuKhuyenMais");

            migrationBuilder.DropIndex(
                name: "IX_LichSuKhuyenMais_KhuyenMaiId",
                table: "LichSuKhuyenMais");

            migrationBuilder.InsertData(
                table: "KhuyenMais",
                columns: new[] { "KhuyenMaiId", "<PERSON><PERSON><PERSON><PERSON>", "GiaTriDonHangToi<PERSON>hieu", "GiaTriToiD<PERSON>", "LoaiK<PERSON>yenM<PERSON>", "Ma<PERSON><PERSON>yenM<PERSON>", "MoTa", "NgayBatDau", "NgayCapNhat", "NgayKetThuc", "NgayTao", "SoLanSuDungToiDa", "SoLuongDaSuDung", "SoLuongToiDa", "TenKhuyenMai", "TrangThaiHoatDong" },
                values: new object[,]
                {
                    { 1, 15m, 200000m, 100000m, 1, "SUMMER2024", "Giảm 15% cho tất cả chuyến xe trong mùa hè", new DateTime(2024, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2024, 8, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), 1, 45, 1000, "Giảm giá mùa hè", true },
                    { 2, 50000m, 150000m, null, 2, "STUDENT50", "Giảm 50,000 VNĐ cho sinh viên", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 2, 123, 500, "Khuyến mãi sinh viên", true },
                    { 3, 20m, 100000m, 80000m, 1, "WEEKEND20", "Giảm 20% cho các chuyến xe cuối tuần", new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 11, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), 1, 67, 200, "Khuyến mãi cuối tuần", true },
                    { 4, 25m, 300000m, 150000m, 1, "TET2025", "Giảm 25% dịp Tết Nguyên Đán", new DateTime(2025, 1, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2025, 2, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 1, 0, 2000, "Khuyến mãi Tết 2025", false },
                    { 5, 100000m, 500000m, null, 2, "VIP100", "Giảm 100,000 VNĐ cho khách hàng VIP", new DateTime(2024, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2024, 11, 30, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 10, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), 1, 50, 50, "Khuyến mãi khách hàng VIP", false }
                });

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_KhuyenMaiId_ThoiGianSuDung",
                table: "LichSuKhuyenMais",
                columns: new[] { "KhuyenMaiId", "ThoiGianSuDung" });

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_ThoiGianSuDung",
                table: "LichSuKhuyenMais",
                column: "ThoiGianSuDung");

            migrationBuilder.AddForeignKey(
                name: "FK_LichSuKhuyenMais_NguoiDungs_NguoiDungId",
                table: "LichSuKhuyenMais",
                column: "NguoiDungId",
                principalTable: "NguoiDungs",
                principalColumn: "NguoiDungId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_LichSuKhuyenMais_Ves_VeId",
                table: "LichSuKhuyenMais",
                column: "VeId",
                principalTable: "Ves",
                principalColumn: "VeId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LichSuKhuyenMais_NguoiDungs_NguoiDungId",
                table: "LichSuKhuyenMais");

            migrationBuilder.DropForeignKey(
                name: "FK_LichSuKhuyenMais_Ves_VeId",
                table: "LichSuKhuyenMais");

            migrationBuilder.DropIndex(
                name: "IX_LichSuKhuyenMais_KhuyenMaiId_ThoiGianSuDung",
                table: "LichSuKhuyenMais");

            migrationBuilder.DropIndex(
                name: "IX_LichSuKhuyenMais_ThoiGianSuDung",
                table: "LichSuKhuyenMais");

            migrationBuilder.DeleteData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 5);

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_KhuyenMaiId",
                table: "LichSuKhuyenMais",
                column: "KhuyenMaiId");

            migrationBuilder.AddForeignKey(
                name: "FK_LichSuKhuyenMais_NguoiDungs_NguoiDungId",
                table: "LichSuKhuyenMais",
                column: "NguoiDungId",
                principalTable: "NguoiDungs",
                principalColumn: "NguoiDungId");

            migrationBuilder.AddForeignKey(
                name: "FK_LichSuKhuyenMais_Ves_VeId",
                table: "LichSuKhuyenMais",
                column: "VeId",
                principalTable: "Ves",
                principalColumn: "VeId");
        }
    }
}
