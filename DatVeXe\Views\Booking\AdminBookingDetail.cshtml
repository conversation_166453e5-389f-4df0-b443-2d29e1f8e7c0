@model dynamic
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Chi tiết đặt vé";
    var chuyenXe = Model.ChuyenXe;
    var choNgoi = Model.ChoNgoi;
    var thanhToans = Model.ThanhToans as IEnumerable<DatVeXe.Models.ThanhToan>;

    var statusClass = "";
    var statusText = "";
    var statusIcon = "";

    switch ((int)Model.VeTrangThai)
    {
        case 1: // DaDat - Đã đặt
            statusClass = "bg-warning text-dark";
            statusText = "Đã đặt";
            statusIcon = "bi-exclamation-circle";
            break;
        case 2: // DaThanhToan - Đã thanh toán
            statusClass = "bg-success";
            statusText = "Đã thanh toán";
            statusIcon = "bi-check-circle";
            break;
        case 3: // DaHoanThanh - Đã hoàn thành
            statusClass = "bg-primary";
            statusText = "Đã hoàn thành";
            statusIcon = "bi-check-all";
            break;
        case 4: // DaHuy - Đã hủy
            statusClass = "bg-danger";
            statusText = "Đã hủy";
            statusIcon = "bi-x-circle";
            break;
        case 5: // DaSuDung - Đã sử dụng
            statusClass = "bg-info";
            statusText = "Đã sử dụng";
            statusIcon = "bi-person-check";
            break;
        case 6: // DaHoanTien - Đã hoàn tiền
            statusClass = "bg-dark text-white";
            statusText = "Đã hoàn tiền";
            statusIcon = "bi-arrow-return-left";
            break;
        default:
            statusClass = "bg-secondary";
            statusText = "Không xác định";
            statusIcon = "bi-question-circle";
            break;
    }

    // Kiểm tra trạng thái thanh toán
    var daThanhToan = thanhToans != null && thanhToans.Any(t => t.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.ThanhCong);
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-ticket-detailed me-2"></i>Chi tiết đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Chi tiết đặt vé</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("PrintTicket", "Booking", new { id = Model.VeId })" class="btn btn-success" target="_blank">
                <i class="bi bi-printer me-1"></i>In vé
            </a>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Thông tin tổng quan -->
    <div class="row mb-4 fade-in">
        <div class="col-12">
            <div class="card border-0 shadow-lg overview-card">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="status-badge-container me-4">
                                    <span class="badge @statusClass status-badge-large">
                                        <i class="bi @statusIcon me-2"></i>@statusText
                                    </span>
                                </div>
                                <div>
                                    <h3 class="mb-2 fw-bold text-dark customer-name">@Model.TenKhach</h3>
                                    <div class="ticket-meta">
                                        <span class="meta-item">
                                            <i class="bi bi-ticket-detailed me-1 text-primary"></i>
                                            <span class="meta-label">Mã vé:</span>
                                            <strong class="text-dark">@Model.MaVe</strong>
                                        </span>
                                        <span class="meta-divider">•</span>
                                        <span class="meta-item">
                                            <i class="bi bi-calendar3 me-1 text-info"></i>
                                            <span class="meta-label">Ngày đặt:</span>
                                            <strong class="text-dark">@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</strong>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="price-container">
                                <h2 class="mb-1 text-success fw-bold price-amount">@Model.TongTien.ToString("#,##0") VNĐ</h2>
                                <span class="price-label">Tổng giá trị vé</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Thông tin chi tiết vé -->
        <div class="col-lg-8 slide-in-left">
            <div class="card border-0 shadow-lg mb-4 info-card">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-person-circle me-2"></i>Thông tin khách hàng</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-item-modern">
                                <div class="info-icon-container bg-primary">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <div class="info-content">
                                    <label class="info-label-modern">Họ tên</label>
                                    <div class="info-value-modern">@Model.TenKhach</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item-modern">
                                <div class="info-icon-container bg-success">
                                    <i class="bi bi-telephone text-white"></i>
                                </div>
                                <div class="info-content">
                                    <label class="info-label-modern">Số điện thoại</label>
                                    <div class="info-value-modern">
                                        <a href="tel:@Model.SoDienThoai" class="text-decoration-none text-dark">@Model.SoDienThoai</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item-modern">
                                <div class="info-icon-container bg-info">
                                    <i class="bi bi-envelope text-white"></i>
                                </div>
                                <div class="info-content">
                                    <label class="info-label-modern">Email</label>
                                    <div class="info-value-modern">
                                        @if (string.IsNullOrEmpty(Model.Email))
                                        {
                                            <span class="text-muted">Không có</span>
                                        }
                                        else
                                        {
                                            <a href="mailto:@Model.Email" class="text-decoration-none text-dark">@Model.Email</a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item-modern">
                                <div class="info-icon-container bg-warning">
                                    <i class="bi bi-geo-alt text-white"></i>
                                </div>
                                <div class="info-content">
                                    <label class="info-label-modern">Số ghế</label>
                                    <div class="info-value-modern">
                                        <span class="seat-badge">
                                            @(string.IsNullOrEmpty(Model.DanhSachGhe) ? "Chưa chọn" : Model.DanhSachGhe)
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thông tin chuyến xe -->
            <div class="card border-0 shadow-lg mb-4 info-card">
                <div class="card-header bg-gradient-info text-white">
                    <h5 class="mb-0 fw-bold"><i class="bi bi-bus-front me-2"></i>Thông tin chuyến xe</h5>
                </div>
                <div class="card-body p-4">
                    @if (chuyenXe != null)
                    {
                        <div class="row g-4">
                            <div class="col-md-12">
                                <div class="info-item-modern">
                                    <div class="info-icon-container bg-info">
                                        <i class="bi bi-signpost-2 text-white"></i>
                                    </div>
                                    <div class="info-content">
                                        <label class="info-label-modern">Tuyến đường</label>
                                        <div class="info-value-modern">
                                            @(chuyenXe.TuyenDuong != null ? chuyenXe.TuyenDuong.TenTuyen : $"{chuyenXe.DiemDi} - {chuyenXe.DiemDen}")
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item-modern">
                                    <div class="info-icon-container bg-primary">
                                        <i class="bi bi-calendar-event text-white"></i>
                                    </div>
                                    <div class="info-content">
                                        <label class="info-label-modern">Ngày khởi hành</label>
                                        <div class="info-value-modern">@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item-modern">
                                    <div class="info-icon-container bg-success">
                                        <i class="bi bi-clock text-white"></i>
                                    </div>
                                    <div class="info-content">
                                        <label class="info-label-modern">Giờ khởi hành</label>
                                        <div class="info-value-modern">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <div class="info-icon-container bg-warning mx-auto mb-3">
                                <i class="bi bi-exclamation-triangle text-white"></i>
                            </div>
                            <h6 class="text-dark fw-bold">Không có thông tin chuyến xe</h6>
                            <p class="text-muted mb-0">Thông tin chuyến xe chưa được cập nhật</p>
                        </div>
                    }
                </div>
            </div>

            @if (!string.IsNullOrEmpty(Model.GhiChu) || (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && !string.IsNullOrEmpty(Model.LyDoHuy)))
            {
                <!-- Ghi chú và lý do hủy -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-gradient-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-chat-left-text me-2"></i>Ghi chú</h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.GhiChu))
                        {
                            <div class="mb-3">
                                <label class="info-label"><i class="bi bi-sticky me-2"></i>Ghi chú</label>
                                <div class="info-value">@Model.GhiChu</div>
                            </div>
                        }
                        @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && !string.IsNullOrEmpty(Model.LyDoHuy))
                        {
                            <div class="alert alert-danger mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Lý do hủy:</strong> @Model.LyDoHuy
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Các hành động -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Hành động</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dropdown d-grid">
                                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-arrow-repeat me-2"></i>Cập nhật trạng thái
                                </button>
                                <ul class="dropdown-menu w-100">
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'DaDon'); return false;">
                                        <i class="bi bi-check2-square me-2 text-success"></i>Đã đón khách
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'KhongCoMat'); return false;">
                                        <i class="bi bi-x-square me-2 text-warning"></i>Không có mặt
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateTripStatus(@Model.VeId, 'HuyChuyen'); return false;">
                                        <i class="bi bi-arrow-repeat me-2 text-danger"></i>Hủy chuyến
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-primary w-100" id="btnSendNotification" data-id="@Model.VeId" data-name="@Model.TenKhach">
                                <i class="bi bi-envelope me-2"></i>Gửi thông báo
                            </button>
                        </div>
                        @if (!daThanhToan && Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy)
                        {
                            <div class="col-md-6">
                                <button class="btn btn-success w-100" onclick="confirmPayment(@Model.VeId)">
                                    <i class="bi bi-check-circle me-2"></i>Xác nhận thanh toán
                                </button>
                            </div>
                        }
                        @if (Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy && (chuyenXe == null || chuyenXe.NgayKhoiHanh > DateTime.Now))
                        {
                            <div class="col-md-6">
                                <button class="btn btn-danger w-100" onclick="showCancelModal()">
                                    <i class="bi bi-x-circle me-2"></i>Hủy vé
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar bên phải -->
        <div class="col-lg-4 slide-in-right">
            <!-- Mã QR -->
            <div class="card border-0 shadow-lg mb-4">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="mb-0"><i class="bi bi-qr-code me-2"></i>Mã QR vé</h5>
                </div>
                <div class="card-body text-center">
                    <div id="qrcode" class="mb-3"></div>
                    <p class="text-secondary small mb-0 fw-medium">
                        <i class="bi bi-info-circle me-1"></i>
                        Quét mã QR để kiểm tra vé
                    </p>
                </div>
            </div>

            <!-- Lịch sử thanh toán -->
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-credit-card me-2"></i>Lịch sử thanh toán</h5>
                </div>
                <div class="card-body p-0">
                    @if (thanhToans != null && thanhToans.Any())
                    {
                        <div class="payment-history">
                            @foreach (var tt in thanhToans)
                            {
                                <div class="payment-item">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                @switch (tt.PhuongThucThanhToan)
                                                {
                                                    case DatVeXe.Models.PhuongThucThanhToan.TaiQuay:
                                                        <i class="bi bi-shop me-1"></i><span>Tại quầy</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.VNPay:
                                                        <i class="bi bi-credit-card me-1"></i><span>VNPay</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.MoMo:
                                                        <i class="bi bi-phone me-1"></i><span>MoMo</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ZaloPay:
                                                        <i class="bi bi-phone me-1"></i><span>ZaloPay</span>
                                                        break;
                                                    case DatVeXe.Models.PhuongThucThanhToan.ChuyenKhoan:
                                                        <i class="bi bi-bank me-1"></i><span>Chuyển khoản</span>
                                                        break;
                                                    default:
                                                        <i class="bi bi-question-circle me-1"></i><span>Khác</span>
                                                        break;
                                                }
                                            </h6>
                                            <small class="text-secondary fw-medium">@tt.MaGiaoDich</small>
                                        </div>
                                        <div class="text-end">
                                            @if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.ThanhCong)
                                            {
                                                <span class="badge bg-success mb-1">Thành công</span>
                                            }
                                            else if (tt.TrangThaiThanhToan == DatVeXe.Models.TrangThaiThanhToan.DangXuLy)
                                            {
                                                <span class="badge bg-warning text-dark mb-1">Đang xử lý</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger mb-1">Thất bại</span>
                                            }
                                            <div class="fw-bold text-success">@tt.SoTien.ToString("#,##0") VNĐ</div>
                                        </div>
                                    </div>
                                    <small class="text-secondary fw-medium">
                                        <i class="bi bi-calendar3 me-1"></i>@tt.NgayThanhToan.ToString("dd/MM/yyyy HH:mm")
                                    </small>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-credit-card text-secondary" style="font-size: 3rem;"></i>
                            <p class="mt-3 mb-0 text-dark fw-medium">Chưa có lịch sử thanh toán</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-envelope me-2"></i>Gửi thông báo</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">
                <div class="mb-3">
                    <label for="notificationSubject" class="form-label fw-bold" style="color: black;">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Nhập tiêu đề email...">
                </div>
                <div class="mb-3">
                    <label for="notificationMessage" class="form-label fw-bold" style="color: black;">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="6" placeholder="Nhập nội dung thông báo..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Hủy
                </button>
                <button type="button" class="btn btn-primary" id="sendNotification">
                    <i class="bi bi-send me-1"></i>Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal hủy vé -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i>Hủy vé</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>
                    Bạn có chắc chắn muốn hủy vé này không? Hành động này không thể hoàn tác.
                </div>
                <div class="mb-3">
                    <label for="cancelReason" class="form-label fw-bold" style="color: black;">Lý do hủy (tùy chọn)</label>
                    <textarea class="form-control" id="cancelReason" rows="3" placeholder="Nhập lý do hủy vé..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Không hủy
                </button>
                <button type="button" class="btn btn-danger" onclick="cancelBooking()">
                    <i class="bi bi-trash me-1"></i>Xác nhận hủy
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');

            // Tạo QR code
            var qr = new QRious({
                element: document.getElementById('qrcode'),
                value: '@Model.QRCode',
                size: 200,
                backgroundAlpha: 0,
                foreground: '#2c3e50',
                level: 'M'
            });

            // Xử lý modal gửi thông báo
            $('#btnSendNotification').click(function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');

                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để cập nhật thông tin về vé xe của bạn.\n\nMã vé: @Model.MaVe\nTên khách hàng: @Model.TenKhach\nChuyến xe: ' + '@(chuyenXe != null ? (chuyenXe.TuyenDuong != null ? chuyenXe.TuyenDuong.TenTuyen : $"{chuyenXe.DiemDi} - {chuyenXe.DiemDen}") : "")' + '\n\nTrân trọng,\nNhà xe ABC');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });

            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();

                if (!subject.trim()) {
                    showToast('Vui lòng nhập tiêu đề', 'warning');
                    return;
                }

                if (!message.trim()) {
                    showToast('Vui lòng nhập nội dung thông báo', 'warning');
                    return;
                }

                $(this).prop('disabled', true).html('<i class="bi bi-hourglass-split me-1"></i>Đang gửi...');

                $.ajax({
                    url: '@Url.Action("SendTicketNotification", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        subject: subject,
                        message: message
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('Đã gửi thông báo thành công', 'success');
                            $('#notificationModal').modal('hide');
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi gửi thông báo', 'danger');
                    },
                    complete: function() {
                        $('#sendNotification').prop('disabled', false).html('<i class="bi bi-send me-1"></i>Gửi thông báo');
                    }
                });
            });
        });

        function updateTripStatus(veId, status) {
            let statusText = '';
            let confirmText = '';
            switch(status) {
                case 'DaDon':
                    statusText = 'đã đón khách';
                    confirmText = 'Xác nhận khách hàng đã được đón?';
                    break;
                case 'KhongCoMat':
                    statusText = 'không có mặt';
                    confirmText = 'Xác nhận khách hàng không có mặt?';
                    break;
                case 'HuyChuyen':
                    statusText = 'hủy chuyến';
                    confirmText = 'Xác nhận hủy chuyến xe này?';
                    break;
            }

            Swal.fire({
                title: 'Xác nhận',
                text: confirmText,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `veId=${veId}&trangThai=${status}`
                    })
                    .then(r => r.json())
                    .then(res => {
                        if (res.success) {
                            showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                            setTimeout(() => window.location.reload(), 1500);
                        } else {
                            showToast(res.message || 'Có lỗi xảy ra', 'danger');
                        }
                    })
                    .catch(err => {
                        showToast('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
                        console.error(err);
                    });
                }
            });
        }

        function confirmPayment(veId) {
            Swal.fire({
                title: 'Xác nhận thanh toán',
                text: 'Bạn có chắc chắn muốn xác nhận thanh toán cho vé này?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('@Url.Action("AdminConfirmPayment", "Booking")', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `id=${veId}`
                    })
                    .then(r => r.json())
                    .then(res => {
                        if (res.success) {
                            showToast(res.message || 'Xác nhận thanh toán thành công', 'success');
                            setTimeout(() => window.location.reload(), 1500);
                        } else {
                            showToast(res.message || 'Có lỗi xảy ra', 'danger');
                        }
                    })
                    .catch(err => {
                        showToast('Có lỗi xảy ra khi xác nhận thanh toán', 'danger');
                        console.error(err);
                    });
                }
            });
        }

        function showCancelModal() {
            $('#cancelModal').modal('show');
        }

        function cancelBooking() {
            var reason = $('#cancelReason').val();

            fetch('@Url.Action("AdminCancelBooking", "Booking")', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `id=@Model.VeId&lyDo=${encodeURIComponent(reason)}`
            })
            .then(r => r.json())
            .then(res => {
                if (res.success) {
                    showToast(res.message || 'Hủy vé thành công', 'success');
                    $('#cancelModal').modal('hide');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(res.message || 'Có lỗi xảy ra', 'danger');
                }
            })
            .catch(err => {
                showToast('Có lỗi xảy ra khi hủy vé', 'danger');
                console.error(err);
            });
        }

        // Hiển thị thông báo toast
        function showToast(message, type) {
            var iconClass = '';
            switch(type) {
                case 'success': iconClass = 'bi-check-circle'; break;
                case 'danger': iconClass = 'bi-exclamation-triangle'; break;
                case 'warning': iconClass = 'bi-exclamation-circle'; break;
                case 'info': iconClass = 'bi-info-circle'; break;
                default: iconClass = 'bi-info-circle'; break;
            }

            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">'+
                '    <i class="bi ' + iconClass + ' me-2"></i>' + message +
                '  </div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );

            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 4000 });
            bsToast.show();

            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <style>
        /* Modern Overview Card */
        .overview-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e3e6f0;
        }

        .status-badge-large {
            font-size: 1rem;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .customer-name {
            font-size: 1.75rem;
            color: #2c3e50;
        }

        .ticket-meta {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 0.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .meta-label {
            color: #6c757d;
            margin-right: 0.25rem;
        }

        .meta-divider {
            color: #dee2e6;
            margin: 0 0.5rem;
        }

        .price-container {
            text-align: right;
        }

        .price-amount {
            font-size: 2rem;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .price-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Modern Info Cards */
        .info-card {
            border: 1px solid #e3e6f0;
        }

        .info-item-modern {
            display: flex;
            align-items: center;
            padding: 1.25rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-item-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #007bff, #6610f2);
            transition: width 0.3s ease;
        }

        .info-item-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .info-item-modern:hover::before {
            width: 6px;
        }

        .info-icon-container {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.25rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .info-content {
            flex: 1;
        }

        .info-label-modern {
            font-size: 0.85rem;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
            display: block;
        }

        .info-value-modern {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.3;
        }

        .seat-badge {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.95rem;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        /* Gradient backgrounds */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .bg-gradient-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        /* Enhanced Card styles */
        .card {
            border-radius: 16px;
            border: 1px solid #e3e6f0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            background: #ffffff;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.12);
            border-color: #007bff;
        }

        .card-header {
            border-bottom: none;
            padding: 1.5rem 2rem;
            border-radius: 16px 16px 0 0 !important;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            opacity: 0.9;
        }

        .card-header h5 {
            position: relative;
            z-index: 1;
            margin: 0;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .card-body {
            padding: 2rem;
            position: relative;
        }

        .shadow-lg {
            box-shadow: 0 10px 30px rgba(0,0,0,0.12) !important;
        }

        /* Info items */
        .info-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            display: block;
        }

        .info-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Payment history */
        .payment-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .payment-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }

        .payment-item:hover {
            background-color: #f8f9fa;
        }

        .payment-item:last-child {
            border-bottom: none;
        }

        /* QR Code styling */
        #qrcode canvas {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Enhanced Button styles */
        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #6610f2);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            box-shadow: 0 4px 15px rgba(220,53,69,0.3);
        }

        .btn-outline-primary {
            border: 2px solid #007bff;
            color: #007bff;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #007bff, #6610f2);
            border-color: #007bff;
            color: white;
        }

        /* Enhanced Badge styles */
        .badge {
            font-size: 0.9rem;
            padding: 0.6em 1.2em;
            border-radius: 25px;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Modal improvements */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-bottom: none;
            border-radius: 15px 15px 0 0;
        }

        .modal-footer {
            border-top: none;
            border-radius: 0 0 15px 15px;
        }

        /* Text contrast improvements */
        .text-secondary {
            color: #495057 !important;
        }

        .text-secondary.fw-medium {
            color: #343a40 !important;
        }

        .badge {
            font-weight: 600;
        }

        /* Status badge improvements */
        .badge.bg-warning {
            color: #000 !important;
        }

        .badge.bg-secondary {
            background-color: #343a40 !important;
            color: #ffffff !important;
        }

        .badge.bg-dark {
            background-color: #212529 !important;
            color: #ffffff !important;
        }

        /* Enhanced QR Code styling */
        #qrcode {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 16px;
            border: 2px dashed #dee2e6;
        }

        #qrcode canvas {
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        #qrcode:hover canvas {
            transform: scale(1.05);
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-left {
            animation: slideInLeft 0.6s ease-out;
        }

        @@keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        @@keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Enhanced Responsive design */
        @@media (max-width: 992px) {
            .price-container {
                text-align: left;
                margin-top: 1rem;
            }

            .customer-name {
                font-size: 1.5rem;
            }

            .price-amount {
                font-size: 1.75rem;
            }
        }

        @@media (max-width: 768px) {
            .card-body {
                padding: 1.5rem;
            }

            .card-header {
                padding: 1.25rem 1.5rem;
            }

            .info-item-modern {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
            }

            .info-icon-container {
                margin-right: 0;
                margin-bottom: 0.75rem;
            }

            .payment-item {
                padding: 1rem 1.5rem;
            }

            .ticket-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .meta-divider {
                display: none;
            }

            .btn {
                padding: 0.875rem 1.25rem;
                font-size: 0.95rem;
            }

            .overview-card .card-body {
                padding: 1.5rem;
            }

            .status-badge-large {
                font-size: 0.9rem;
                padding: 0.6rem 1.2rem;
            }
        }

        @@media (max-width: 576px) {
            .card-body {
                padding: 1rem;
            }

            .card-header {
                padding: 1rem;
            }

            .customer-name {
                font-size: 1.25rem;
            }

            .price-amount {
                font-size: 1.5rem;
            }

            .info-item-modern {
                padding: 0.75rem;
            }

            .info-icon-container {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
}

