/* ==========================================================================
   PAGE SPECIFIC COLOR FIXES - Khắc phục vấn đề màu sắc cho từng trang cụ thể
   ========================================================================== */

/* 1. SEAT SELECTION PAGE FIXES */
.seat-layout-container {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin: 20px 0 !important;
}

.seat-item {
    border: 2px solid #dee2e6 !important;
    color: #333333 !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.seat-available {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border-color: #28a745 !important;
}

.seat-available:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    transform: scale(1.1) !important;
}

.seat-selected {
    background-color: #007bff !important;
    color: #ffffff !important;
    border-color: #0056b3 !important;
    transform: scale(1.1) !important;
}

.seat-occupied {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border-color: #c82333 !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
}

.seat-reserved {
    background-color: #ffc107 !important;
    color: #000000 !important;
    border-color: #e0a800 !important;
    cursor: not-allowed !important;
}

.seat-disabled {
    background-color: #6c757d !important;
    color: #ffffff !important;
    border-color: #545b62 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* Seat Legend */
.seat-legend {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin: 20px 0 !important;
}

.seat-legend-item {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

.seat-legend-color {
    width: 20px !important;
    height: 20px !important;
    border-radius: 4px !important;
    margin-right: 10px !important;
    border: 1px solid #dee2e6 !important;
}

.seat-legend-text {
    color: #333333 !important;
    font-weight: 500 !important;
}

/* 2. SEARCH RESULTS PAGE FIXES */
.search-results-container {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin: 20px 0 !important;
}

.trip-card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
    transition: all 0.3s ease !important;
}

.trip-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    transform: translateY(-2px) !important;
}

.trip-time {
    color: #007bff !important;
    font-weight: 600 !important;
    font-size: 1.2rem !important;
}

.trip-route {
    color: #333333 !important;
    font-weight: 500 !important;
}

.trip-price {
    color: #28a745 !important;
    font-weight: 700 !important;
    font-size: 1.3rem !important;
}

.trip-seats-available {
    color: #28a745 !important;
    font-weight: 500 !important;
}

.trip-seats-few {
    color: #ffc107 !important;
    font-weight: 500 !important;
}

.trip-seats-full {
    color: #dc3545 !important;
    font-weight: 500 !important;
}

/* 3. BOOKING FORM FIXES */
.booking-form-container {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-radius: 10px !important;
    padding: 30px !important;
    margin: 20px 0 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

.booking-summary {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 20px !important;
}

.booking-summary-title {
    color: #007bff !important;
    font-weight: 600 !important;
    margin-bottom: 15px !important;
}

.booking-summary-item {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 10px !important;
    color: #333333 !important;
}

.booking-summary-total {
    border-top: 2px solid #dee2e6 !important;
    padding-top: 15px !important;
    margin-top: 15px !important;
    font-weight: 700 !important;
    font-size: 1.2rem !important;
    color: #28a745 !important;
}

/* 4. DASHBOARD FIXES */
.dashboard-card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.dashboard-stat-number {
    color: #007bff !important;
    font-weight: 700 !important;
    font-size: 2.5rem !important;
}

.dashboard-stat-label {
    color: #6c757d !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.dashboard-chart-container {
    background-color: #ffffff !important;
    border-radius: 8px !important;
    padding: 20px !important;
}

/* 5. ADMIN PANEL FIXES */
.admin-sidebar {
    background-color: #343a40 !important;
    color: #ffffff !important;
}

.admin-sidebar .nav-link {
    color: #ffffff !important;
    padding: 12px 20px !important;
    border-radius: 0 !important;
    transition: all 0.3s ease !important;
}

.admin-sidebar .nav-link:hover {
    background-color: #495057 !important;
    color: #ffffff !important;
}

.admin-sidebar .nav-link.active {
    background-color: #007bff !important;
    color: #ffffff !important;
}

.admin-content {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    min-height: 100vh !important;
    padding: 20px !important;
}

/* 6. TICKET HISTORY FIXES */
.ticket-history-item {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 15px !important;
}

.ticket-status-confirmed {
    background-color: #28a745 !important;
    color: #ffffff !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.ticket-status-cancelled {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.ticket-status-pending {
    background-color: #ffc107 !important;
    color: #000000 !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

/* 7. FILTER PANEL FIXES */
.filter-panel {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
}

.filter-panel .form-label {
    color: #333333 !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
}

.filter-panel .form-control,
.filter-panel .form-select {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #ced4da !important;
}

.filter-panel .btn-primary {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #ffffff !important;
    font-weight: 500 !important;
}

.filter-panel .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
    font-weight: 500 !important;
}

/* 8. NOTIFICATION FIXES */
.notification-container {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
}

.notification-success {
    background-color: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
}

.notification-error {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
}

.notification-warning {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
}

.notification-info {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border: 1px solid #bee5eb !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
}

/* 9. LOADING SPINNER FIXES */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #333333 !important;
}

.loading-spinner {
    border: 4px solid #f3f3f3 !important;
    border-top: 4px solid #007bff !important;
    border-radius: 50% !important;
    width: 50px !important;
    height: 50px !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 10. RESPONSIVE FIXES */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px !important;
    }
    
    .booking-form-container,
    .search-results-container,
    .seat-layout-container {
        padding: 15px !important;
        margin: 10px 0 !important;
    }
    
    .trip-card {
        padding: 15px !important;
    }
    
    .dashboard-card {
        padding: 15px !important;
        margin-bottom: 15px !important;
    }
}
