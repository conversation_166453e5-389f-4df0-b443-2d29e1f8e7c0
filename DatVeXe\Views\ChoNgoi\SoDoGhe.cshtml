@model DatVeXe.Models.SoDoGheViewModel
@{
    ViewData["Title"] = "Sơ đồ ghế xe";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-grid-3x3-gap me-2"></i>Sơ đồ ghế xe @Model.BienSo
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>Loại xe:</strong> @Model.LoaiXe</p>
                            <p><strong>Số ghế:</strong> @Model.SoGhe</p>
                        </div>
                        <div class="col-md-6">
                            <div class="seat-legend">
                                <span class="badge bg-success me-2">Còn trống</span>
                                <span class="badge bg-danger me-2"><PERSON><PERSON> đặt</span>
                                <span class="badge bg-warning me-2"><PERSON><PERSON> chọn</span>
                                <span class="badge bg-secondary">Kh<PERSON><PERSON> kh<PERSON> dụng</span>
                            </div>
                        </div>
                    </div>

                    <!-- Sơ đồ ghế đơn giản -->
                    <div class="seat-map">
                        @for (int hang = 1; hang <= Model.SoHang; hang++)
                        {
                            <div class="seat-row mb-2">
                                <span class="row-label">@hang</span>
                                @for (int cot = 1; cot <= Model.SoCot; cot++)
                                {
                                    var ghe = Model.DanhSachGhe.FirstOrDefault(g => g.Hang == hang && g.Cot == cot);
                                    if (ghe != null)
                                    {
                                        var cssClass = "btn btn-sm me-1 seat-btn";
                                        if (!ghe.TrangThaiHoatDong)
                                        {
                                            cssClass += " btn-secondary";
                                        }
                                        else if (ghe.DaDat)
                                        {
                                            cssClass += " btn-danger";
                                        }
                                        else
                                        {
                                            cssClass += " btn-success";
                                        }

                                        <button type="button" class="@cssClass" 
                                                data-seat-id="@ghe.ChoNgoiId"
                                                data-seat-number="@ghe.SoGhe"
                                                @(ghe.DaDat || !ghe.TrangThaiHoatDong ? "disabled" : "")
                                                title="@ghe.SoGhe @(ghe.DaDat ? $"- Đã đặt ({ghe.TenKhachDat})" : "")">
                                            @ghe.SoGhe
                                        </button>
                                    }
                                    else
                                    {
                                        <span class="seat-empty me-1"></span>
                                    }
                                    
                                    @if (cot == 2 && Model.SoCot > 2)
                                    {
                                        <span class="aisle me-2"></span>
                                    }
                                }
                            </div>
                        }
                    </div>

                    <div class="mt-3">
                        <p><strong>Ghế đã chọn:</strong> <span id="selectedSeat">Chưa chọn</span></p>
                        <button class="btn btn-primary" id="bookBtn" disabled>Đặt vé</button>
                        <a href="/ChuyenXe/Search" class="btn btn-secondary">Quay lại</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.seat-map {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.seat-row {
    display: flex;
    justify-content: center;
    align-items: center;
}

.row-label {
    width: 30px;
    font-weight: bold;
    margin-right: 10px;
}

.seat-btn {
    width: 40px;
    height: 40px;
    font-size: 12px;
    font-weight: bold;
}

.seat-empty {
    display: inline-block;
    width: 40px;
    height: 40px;
}

.aisle {
    display: inline-block;
    width: 20px;
}

.seat-legend {
    text-align: right;
}
</style>

<script>
$(document).ready(function() {
    let selectedSeatId = null;
    let selectedSeatNumber = null;

    $('.btn-success.seat-btn').click(function() {
        // Bỏ chọn ghế cũ
        $('.btn-warning').removeClass('btn-warning').addClass('btn-success');
        
        // Chọn ghế mới
        $(this).removeClass('btn-success').addClass('btn-warning');
        
        selectedSeatId = $(this).data('seat-id');
        selectedSeatNumber = $(this).data('seat-number');
        
        $('#selectedSeat').text(selectedSeatNumber);
        $('#bookBtn').prop('disabled', false);
    });

    $('#bookBtn').click(function() {
        if (selectedSeatId) {
            // Chuyển đến trang đặt vé với ghế đã chọn
            window.location.href = `/ChoNgoi/ChonGhe/@Model.XeId?seatId=${selectedSeatId}`;
        }
    });
});
</script>
