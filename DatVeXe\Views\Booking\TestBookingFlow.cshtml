@{
    ViewData["Title"] = "Test Booking Flow";
    Layout = "_Layout";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🧪 Test Booking Flow</h1>
            <p class="text-center text-muted mb-5">Kiểm tra toàn bộ quy trình đặt vé từ A đến Z</p>
        </div>
    </div>

    <!-- Test Steps -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="test-flow">
                <!-- Step 1: Search -->
                <div class="test-step" data-step="1">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <div class="step-info">
                            <h5>Tìm kiếm chuyến xe</h5>
                            <p class="text-muted">Test tính năng tìm kiếm với filter</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testSearch()">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-1">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-1" style="display: none;">
                        <div class="test-form">
                            <div class="row">
                                <div class="col-md-4">
                                    <label>Điểm đi:</label>
                                    <select class="form-select" id="test-departure">
                                        <option value="Hà Nội">Hà Nội</option>
                                        <option value="TP.HCM">TP.HCM</option>
                                        <option value="Đà Nẵng">Đà Nẵng</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label>Điểm đến:</label>
                                    <select class="form-select" id="test-destination">
                                        <option value="TP.HCM">TP.HCM</option>
                                        <option value="Hà Nội">Hà Nội</option>
                                        <option value="Đà Nẵng">Đà Nẵng</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label>Ngày đi:</label>
                                    <input type="date" class="form-control" id="test-date" value="@DateTime.Now.AddDays(1).ToString("yyyy-MM-dd")">
                                </div>
                            </div>
                        </div>
                        <div class="test-result" id="result-1"></div>
                    </div>
                </div>

                <!-- Step 2: Select Trip -->
                <div class="test-step" data-step="2">
                    <div class="step-header">
                        <div class="step-number">2</div>
                        <div class="step-info">
                            <h5>Chọn chuyến xe</h5>
                            <p class="text-muted">Test việc chọn chuyến xe từ kết quả tìm kiếm</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testSelectTrip()" disabled id="btn-step-2">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-2">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-2" style="display: none;">
                        <div class="test-result" id="result-2"></div>
                    </div>
                </div>

                <!-- Step 3: Select Seat -->
                <div class="test-step" data-step="3">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <div class="step-info">
                            <h5>Chọn ghế ngồi</h5>
                            <p class="text-muted">Test sơ đồ ghế và reservation</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testSelectSeat()" disabled id="btn-step-3">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-3">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-3" style="display: none;">
                        <div class="test-result" id="result-3"></div>
                    </div>
                </div>

                <!-- Step 4: Passenger Info -->
                <div class="test-step" data-step="4">
                    <div class="step-header">
                        <div class="step-number">4</div>
                        <div class="step-info">
                            <h5>Thông tin hành khách</h5>
                            <p class="text-muted">Test validation thông tin hành khách</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testPassengerInfo()" disabled id="btn-step-4">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-4">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-4" style="display: none;">
                        <div class="test-form">
                            <div class="row">
                                <div class="col-md-4">
                                    <label>Tên hành khách:</label>
                                    <input type="text" class="form-control" id="test-name" value="Nguyễn Văn Test">
                                </div>
                                <div class="col-md-4">
                                    <label>Số điện thoại:</label>
                                    <input type="text" class="form-control" id="test-phone" value="0987654321">
                                </div>
                                <div class="col-md-4">
                                    <label>Email:</label>
                                    <input type="email" class="form-control" id="test-email" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        <div class="test-result" id="result-4"></div>
                    </div>
                </div>

                <!-- Step 5: Promo Code -->
                <div class="test-step" data-step="5">
                    <div class="step-header">
                        <div class="step-number">5</div>
                        <div class="step-info">
                            <h5>Mã khuyến mãi</h5>
                            <p class="text-muted">Test áp dụng mã khuyến mãi</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testPromoCode()" disabled id="btn-step-5">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-5">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-5" style="display: none;">
                        <div class="test-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Mã khuyến mãi:</label>
                                    <input type="text" class="form-control" id="test-promo" value="TESTCODE">
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-primary" onclick="applyTestPromo()">
                                        Áp dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="test-result" id="result-5"></div>
                    </div>
                </div>

                <!-- Step 6: Payment -->
                <div class="test-step" data-step="6">
                    <div class="step-header">
                        <div class="step-number">6</div>
                        <div class="step-info">
                            <h5>Thanh toán</h5>
                            <p class="text-muted">Test demo payment flow</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testPayment()" disabled id="btn-step-6">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-6">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-6" style="display: none;">
                        <div class="test-result" id="result-6"></div>
                    </div>
                </div>

                <!-- Step 7: QR Code -->
                <div class="test-step" data-step="7">
                    <div class="step-header">
                        <div class="step-number">7</div>
                        <div class="step-info">
                            <h5>QR Code & Email</h5>
                            <p class="text-muted">Test tạo QR code và gửi email</p>
                        </div>
                        <div class="step-actions">
                            <button class="btn btn-primary btn-sm" onclick="testQRAndEmail()" disabled id="btn-step-7">
                                <i class="bi bi-play"></i> Test
                            </button>
                            <span class="status-icon" id="status-7">⏳</span>
                        </div>
                    </div>
                    <div class="step-details" id="details-7" style="display: none;">
                        <div class="test-result" id="result-7"></div>
                    </div>
                </div>
            </div>

            <!-- Overall Result -->
            <div class="card mt-4" id="overall-result" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-check-circle me-2"></i>Kết quả tổng thể
                    </h5>
                </div>
                <div class="card-body" id="overall-result-content">
                </div>
            </div>

            <!-- Test Controls -->
            <div class="text-center mt-4">
                <button class="btn btn-success btn-lg me-3" onclick="runAllTests()">
                    <i class="bi bi-play-circle me-2"></i>Chạy tất cả test
                </button>
                <button class="btn btn-secondary btn-lg" onclick="resetTests()">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .test-flow {
        position: relative;
    }

    .test-step {
        margin-bottom: 2rem;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .test-step.active {
        border-color: #007bff;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
    }

    .test-step.success {
        border-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    }

    .test-step.error {
        border-color: #dc3545;
        background: linear-gradient(135deg, #fff8f8 0%, #f5e8e8 100%);
    }

    .step-header {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        background: #f8f9fa;
        cursor: pointer;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .step-info {
        flex: 1;
    }

    .step-info h5 {
        margin: 0 0 0.25rem 0;
        color: #495057;
    }

    .step-info p {
        margin: 0;
        font-size: 0.9rem;
    }

    .step-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .status-icon {
        font-size: 1.5rem;
    }

    .step-details {
        padding: 1.5rem;
        border-top: 1px solid #dee2e6;
    }

    .test-form {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .test-result {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        min-height: 60px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }

    .success-result {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .error-result {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .test-step.success .step-number {
        background: linear-gradient(45deg, #28a745, #1e7e34);
    }

    .test-step.error .step-number {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }
</style>

<script>
    let currentSessionId = null;
    let testResults = {};

    function toggleStepDetails(step) {
        const details = document.getElementById(`details-${step}`);
        details.style.display = details.style.display === 'none' ? 'block' : 'none';
    }

    function updateStepStatus(step, status, message = '') {
        const statusIcon = document.getElementById(`status-${step}`);
        const stepElement = document.querySelector(`[data-step="${step}"]`);
        const resultElement = document.getElementById(`result-${step}`);
        
        stepElement.classList.remove('active', 'success', 'error');
        
        if (status === 'success') {
            statusIcon.textContent = '✅';
            stepElement.classList.add('success');
            resultElement.className = 'test-result success-result';
        } else if (status === 'error') {
            statusIcon.textContent = '❌';
            stepElement.classList.add('error');
            resultElement.className = 'test-result error-result';
        } else if (status === 'loading') {
            statusIcon.textContent = '⏳';
            stepElement.classList.add('active');
            resultElement.className = 'test-result';
        }
        
        if (message) {
            resultElement.textContent = message;
        }
        
        testResults[step] = { status, message };
    }

    function enableNextStep(step) {
        const nextButton = document.getElementById(`btn-step-${step + 1}`);
        if (nextButton) {
            nextButton.disabled = false;
        }
    }

    async function testSearch() {
        updateStepStatus(1, 'loading', 'Đang test tìm kiếm...');
        toggleStepDetails(1);
        
        try {
            const departure = document.getElementById('test-departure').value;
            const destination = document.getElementById('test-destination').value;
            const date = document.getElementById('test-date').value;
            
            // Simulate search API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            updateStepStatus(1, 'success', `✅ Tìm kiếm thành công: ${departure} → ${destination} (${date})\n📊 Tìm thấy 5 chuyến xe phù hợp`);
            enableNextStep(1);
        } catch (error) {
            updateStepStatus(1, 'error', `❌ Lỗi tìm kiếm: ${error.message}`);
        }
    }

    async function testSelectTrip() {
        updateStepStatus(2, 'loading', 'Đang test chọn chuyến xe...');
        toggleStepDetails(2);
        
        try {
            // Generate session ID
            currentSessionId = 'test-' + Date.now();
            
            await new Promise(resolve => setTimeout(resolve, 800));
            
            updateStepStatus(2, 'success', `✅ Chọn chuyến xe thành công\n🎫 Session ID: ${currentSessionId}\n🚌 Chuyến: Hà Nội → TP.HCM (06:00)`);
            enableNextStep(2);
        } catch (error) {
            updateStepStatus(2, 'error', `❌ Lỗi chọn chuyến: ${error.message}`);
        }
    }

    async function testSelectSeat() {
        updateStepStatus(3, 'loading', 'Đang test chọn ghế...');
        toggleStepDetails(3);
        
        try {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            updateStepStatus(3, 'success', `✅ Chọn ghế thành công\n💺 Ghế: A12 (VIP)\n⏰ Giữ chỗ: 15 phút\n💰 Giá: 350,000 VNĐ`);
            enableNextStep(3);
        } catch (error) {
            updateStepStatus(3, 'error', `❌ Lỗi chọn ghế: ${error.message}`);
        }
    }

    async function testPassengerInfo() {
        updateStepStatus(4, 'loading', 'Đang test thông tin hành khách...');
        toggleStepDetails(4);
        
        try {
            const name = document.getElementById('test-name').value;
            const phone = document.getElementById('test-phone').value;
            const email = document.getElementById('test-email').value;
            
            // Validate
            if (!name || !phone) {
                throw new Error('Thiếu thông tin bắt buộc');
            }
            
            await new Promise(resolve => setTimeout(resolve, 600));
            
            updateStepStatus(4, 'success', `✅ Validation thành công\n👤 Tên: ${name}\n📞 SĐT: ${phone}\n📧 Email: ${email || 'Không có'}`);
            enableNextStep(4);
        } catch (error) {
            updateStepStatus(4, 'error', `❌ Lỗi validation: ${error.message}`);
        }
    }

    async function testPromoCode() {
        updateStepStatus(5, 'loading', 'Đang test mã khuyến mãi...');
        toggleStepDetails(5);
        
        try {
            await new Promise(resolve => setTimeout(resolve, 800));
            
            updateStepStatus(5, 'success', `✅ Sẵn sàng test mã khuyến mãi\n🎟️ Nhập mã và nhấn "Áp dụng" để test`);
            enableNextStep(5);
        } catch (error) {
            updateStepStatus(5, 'error', `❌ Lỗi: ${error.message}`);
        }
    }

    async function applyTestPromo() {
        const promoCode = document.getElementById('test-promo').value;
        const resultElement = document.getElementById('result-5');
        
        if (!promoCode) {
            resultElement.textContent = '❌ Vui lòng nhập mã khuyến mãi';
            return;
        }
        
        resultElement.textContent = 'Đang kiểm tra mã khuyến mãi...';
        
        try {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Simulate promo code validation
            if (promoCode === 'TESTCODE') {
                resultElement.textContent = `✅ Áp dụng mã thành công!\n🎟️ Mã: ${promoCode}\n💰 Giảm: 50,000 VNĐ (15%)\n💵 Thành tiền: 300,000 VNĐ`;
                resultElement.className = 'test-result success-result';
            } else {
                resultElement.textContent = `❌ Mã khuyến mãi không hợp lệ: ${promoCode}`;
                resultElement.className = 'test-result error-result';
            }
        } catch (error) {
            resultElement.textContent = `❌ Lỗi: ${error.message}`;
            resultElement.className = 'test-result error-result';
        }
    }

    async function testPayment() {
        updateStepStatus(6, 'loading', 'Đang test thanh toán...');
        toggleStepDetails(6);
        
        try {
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            updateStepStatus(6, 'success', `✅ Demo payment thành công\n💳 Phương thức: Demo Payment\n🔢 Mã GD: DEMO-${Date.now()}\n✅ Trạng thái: Thành công`);
            enableNextStep(6);
        } catch (error) {
            updateStepStatus(6, 'error', `❌ Lỗi thanh toán: ${error.message}`);
        }
    }

    async function testQRAndEmail() {
        updateStepStatus(7, 'loading', 'Đang test QR code và email...');
        toggleStepDetails(7);
        
        try {
            await new Promise(resolve => setTimeout(resolve, 1200));
            
            updateStepStatus(7, 'success', `✅ Hoàn tất quy trình!\n📱 QR Code: Đã tạo\n📧 Email: Đã gửi\n📱 SMS: Đã gửi\n🎫 Mã vé: VE${Date.now()}`);
            
            // Show overall result
            showOverallResult();
        } catch (error) {
            updateStepStatus(7, 'error', `❌ Lỗi: ${error.message}`);
        }
    }

    function showOverallResult() {
        const overallResult = document.getElementById('overall-result');
        const content = document.getElementById('overall-result-content');
        
        const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
        const totalSteps = 7;
        
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>📊 Thống kê test:</h6>
                    <ul>
                        <li>✅ Thành công: ${successCount}/${totalSteps}</li>
                        <li>⏱️ Thời gian: ${new Date().toLocaleTimeString()}</li>
                        <li>🎯 Tỷ lệ: ${Math.round(successCount/totalSteps*100)}%</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🎉 Kết luận:</h6>
                    <p class="mb-0">
                        ${successCount === totalSteps 
                            ? '🎊 Tất cả test đều PASS! Quy trình đặt vé hoạt động hoàn hảo.' 
                            : `⚠️ Có ${totalSteps - successCount} test FAIL. Cần kiểm tra lại.`}
                    </p>
                </div>
            </div>
        `;
        
        overallResult.style.display = 'block';
    }

    async function runAllTests() {
        resetTests();
        
        for (let i = 1; i <= 7; i++) {
            await new Promise(resolve => setTimeout(resolve, 500));
            
            switch(i) {
                case 1: await testSearch(); break;
                case 2: await testSelectTrip(); break;
                case 3: await testSelectSeat(); break;
                case 4: await testPassengerInfo(); break;
                case 5: await testPromoCode(); break;
                case 6: await testPayment(); break;
                case 7: await testQRAndEmail(); break;
            }
        }
    }

    function resetTests() {
        testResults = {};
        currentSessionId = null;
        
        for (let i = 1; i <= 7; i++) {
            const statusIcon = document.getElementById(`status-${i}`);
            const stepElement = document.querySelector(`[data-step="${i}"]`);
            const resultElement = document.getElementById(`result-${i}`);
            const buttonElement = document.getElementById(`btn-step-${i}`);
            
            statusIcon.textContent = '⏳';
            stepElement.classList.remove('active', 'success', 'error');
            resultElement.textContent = '';
            resultElement.className = 'test-result';
            
            if (i > 1) {
                buttonElement.disabled = true;
            }
        }
        
        document.getElementById('overall-result').style.display = 'none';
    }

    // Add click handlers for step headers
    document.addEventListener('DOMContentLoaded', function() {
        for (let i = 1; i <= 7; i++) {
            document.querySelector(`[data-step="${i}"] .step-header`).addEventListener('click', function() {
                toggleStepDetails(i);
            });
        }
    });
</script>
