@model DatVeXe.Models.ChuyenXeSearchViewModel
@{
    ViewData["Title"] = "Danh sách chuyến xe";
}

<div class="container py-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
        <h2 class="fw-bold text-primary mb-0 text-uppercase">T<PERSON><PERSON> kiếm chuyến xe</h2>
        <div class="d-flex gap-2">
            <a asp-controller="Home" asp-action="Index" class="btn btn-outline-secondary d-flex align-items-center gap-2 rounded-pill px-4">
                <i class="fas fa-home"></i> Trang chủ
            </a>
            <a asp-controller="Booking" asp-action="Search" class="btn btn-primary d-flex align-items-center gap-2 rounded-pill px-4">
                <i class="fas fa-search"></i> T<PERSON><PERSON> kiếm nâng cao
            </a>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Form tìm kiếm -->
    <div class="card shadow-sm mb-4">
        <div class="card-header search-form">
            <h6 class="card-title mb-0">
                <i class="fas fa-search text-primary"></i> Tìm kiếm chuyến xe
            </h6>
        </div>
        <div class="card-body search-form">
            <form asp-action="Index" method="get" id="searchForm">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label asp-for="DiemDi" class="form-label"></label>
                        <input asp-for="DiemDi" class="form-control" placeholder="Nhập điểm đi" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="DiemDen" class="form-label"></label>
                        <input asp-for="DiemDen" class="form-control" placeholder="Nhập điểm đến" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="TuNgay" class="form-label"></label>
                        <input asp-for="TuNgay" class="form-control" type="date" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="DenNgay" class="form-label"></label>
                        <input asp-for="DenNgay" class="form-control" type="date" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="GiaTu" class="form-label"></label>
                        <input asp-for="GiaTu" class="form-control" placeholder="Giá từ" type="number" min="0" step="1000" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="GiaDen" class="form-label"></label>
                        <input asp-for="GiaDen" class="form-control" placeholder="Giá đến" type="number" min="0" step="1000" />
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-2">
                        <label asp-for="LoaiXe" class="form-label"></label>
                        <select asp-for="LoaiXe" class="form-select">
                            <option value="">Tất cả loại xe</option>
                            <option value="Giường nằm">Giường nằm</option>
                            <option value="Limousine">Limousine</option>
                            <option value="Ghế ngồi">Ghế ngồi</option>
                            <option value="VIP">VIP</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="TrangThai" class="form-label"></label>
                        <select asp-for="TrangThai" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="chua_khoi_hanh">Chưa khởi hành</option>
                            <option value="da_khoi_hanh">Đã khởi hành</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="SortBy" class="form-label">Sắp xếp theo</label>
                        <select asp-for="SortBy" class="form-select">
                            <option value="NgayKhoiHanh">Ngày khởi hành</option>
                            <option value="Gia">Giá vé</option>
                            <option value="DiemDi">Điểm đi</option>
                            <option value="DiemDen">Điểm đến</option>
                            <option value="LoaiXe">Loại xe</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="SortOrder" class="form-label">Thứ tự</label>
                        <select asp-for="SortOrder" class="form-select">
                            <option value="asc">Tăng dần</option>
                            <option value="desc">Giảm dần</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check mt-4">
                            <input name="CoGheTrong" class="form-check-input" type="checkbox" value="true" @(Model.CoGheTrong == true ? "checked" : "") />
                            <label for="CoGheTrong" class="form-check-label">Có ghế trống</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Tìm kiếm
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i> Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Hiển thị thống kê kết quả -->
    @if (Model.KetQua != null)
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Tìm thấy <strong>@Model.KetQua.Count()</strong> chuyến xe
            @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
            {
                <span>phù hợp với điều kiện tìm kiếm</span>
            }
        </div>
    }

    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="border-0">Tuyến đường</th>
                            <th class="border-0">Ngày & Giờ khởi hành</th>
                            <th class="border-0">Giá vé</th>
                            <th class="border-0">Loại xe</th>
                            <th class="border-0">Số ghế trống</th>
                            <th class="border-0">Trạng thái</th>
                            <th class="border-0" style="width: 150px;">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.KetQua == null || !Model.KetQua.Any())
                        {
                            <tr>
                                <td colspan="7" class="text-center py-5 text-muted">
                                    <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                                    @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
                                    {
                                        <span>Không tìm thấy chuyến xe nào phù hợp với điều kiện tìm kiếm.</span>
                                        <br><small>Hãy thử điều chỉnh bộ lọc hoặc chọn ngày khác.</small>
                                    }
                                    else
                                    {
                                        <span>Vui lòng sử dụng bộ lọc để tìm chuyến xe phù hợp.</span>
                                    }
                                </td>
                            </tr>
                        }
                        else
                        {
                            foreach (var item in Model.KetQua)
                            {
                                var soGheTrong = item.SoGheTrong; // Sử dụng computed property
                                var daDi = item.NgayKhoiHanh <= DateTime.Now;

                                <tr>
                                    <td>
                                        <strong class="text-primary">@item.DiemDiDisplay</strong>
                                        <i class="fas fa-arrow-right text-muted mx-2"></i>
                                        <strong class="text-success">@item.DiemDenDisplay</strong>
                                        @if (item.TuyenDuong != null)
                                        {
                                            <br><small class="text-muted">@item.TuyenDuong.KhoangCach km - @item.TuyenDuong.ThoiGianDuKien.ToString(@"hh\:mm")</small>
                                        }
                                    </td>
                                    <td>
                                        <strong>@item.NgayKhoiHanh.ToString("dd/MM/yyyy")</strong>
                                        <br><span class="text-primary">@item.NgayKhoiHanh.ToString("HH:mm")</span>
                                    </td>
                                    <td>
                                        <strong class="text-success fs-5">@item.Gia.ToString("N0") VNĐ</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">@(item.Xe?.LoaiXe ?? "N/A")</span>
                                        <br><small class="text-muted">@(item.Xe?.BienSo ?? "N/A") - @(item.Xe?.SoGhe ?? 0) chỗ</small>
                                    </td>
                                    <td>
                                        @if (soGheTrong > 0)
                                        {
                                            <span class="badge bg-success fs-6">@soGheTrong chỗ trống</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger fs-6">Hết chỗ</span>
                                        }
                                    </td>
                                    <td>
                                        @if (daDi)
                                        {
                                            <span class="badge bg-secondary">Đã khởi hành</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-primary">Sẵn sàng</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical d-grid gap-1">
                                            <a asp-action="Details" asp-route-id="@item.ChuyenXeId"
                                               class="btn btn-outline-primary btn-sm" title="Xem chi tiết">
                                               <i class="fas fa-info-circle"></i> Chi tiết
                                            </a>
                                            @if (!daDi && soGheTrong > 0)
                                            {
                                                <a asp-controller="ChoNgoi" asp-action="ChonGhe" asp-route-id="@item.ChuyenXeId"
                                                   class="btn btn-success btn-sm" title="Đặt vé">
                                                    <i class="fas fa-ticket-alt"></i> Đặt vé
                                                </a>
                                            }
                                            else if (!daDi)
                                            {
                                                <button class="btn btn-secondary btn-sm" disabled title="Hết chỗ">
                                                    <i class="fas fa-ban"></i> Hết chỗ
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .table th {
            font-weight: 600;
            color: #2c3e50;
        }
        .table td {
            color: #2c3e50;
        }
        .badge {
            font-weight: 500;
        }
        .search-form {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .form-label {
            color: #2c3e50 !important;
            font-weight: 600;
        }
        .form-control, .form-select {
            color: #2c3e50 !important;
        }
        .form-check-label {
            color: #2c3e50 !important;
            font-weight: 500;
        }
        .text-muted {
            color: #6c757d !important;
        }
        .card-title {
            color: #2c3e50 !important;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Validation cho ngày
            $('#TuNgay, #DenNgay').change(function() {
                var tuNgay = $('#TuNgay').val();
                var denNgay = $('#DenNgay').val();

                if (tuNgay && denNgay && new Date(tuNgay) > new Date(denNgay)) {
                    alert('Từ ngày không được lớn hơn đến ngày');
                    $(this).val('');
                }
            });

            // Highlight search results
            @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
            {
                <text>
                $('.alert-info').addClass('border-primary');
                </text>
            }

            // Auto-focus vào form tìm kiếm khi trang load
            $('#DiemDi').focus();
        });
    </script>
}
