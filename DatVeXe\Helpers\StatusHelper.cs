using DatVeXe.Models;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace DatVeXe.Helpers
{
    /// <summary>
    /// Helper class để xử lý trạng thái vé một cách thống nhất
    /// </summary>
    public static class StatusHelper
    {
        /// <summary>
        /// Lấy text hiển thị cho trạng thái vé
        /// </summary>
        /// <param name="trangThai">Trạng thái vé</param>
        /// <returns>Text hiển thị</returns>
        public static string GetStatusText(TrangThaiVe trangThai)
        {
            return trangThai switch
            {
                TrangThaiVe.DaDat => "Đã đặt",
                TrangThaiVe.DaThanhToan => "Đã thanh toán",
                TrangThaiVe.DaHoanThanh => "Đã hoàn thành",
                TrangThaiVe.DaHuy => "Đã hủy",
                TrangThaiVe.DaSuDung => "Đã sử dụng",
                TrangThaiVe.DaHoanTien => "Đã hoàn tiền",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Lấy class CSS cho badge trạng thái
        /// </summary>
        /// <param name="trangThai">Trạng thái vé</param>
        /// <returns>Class CSS</returns>
        public static string GetStatusBadgeClass(TrangThaiVe trangThai)
        {
            return trangThai switch
            {
                TrangThaiVe.DaDat => "warning",
                TrangThaiVe.DaThanhToan => "info",
                TrangThaiVe.DaHoanThanh => "success",
                TrangThaiVe.DaHuy => "danger",
                TrangThaiVe.DaSuDung => "primary",
                TrangThaiVe.DaHoanTien => "secondary",
                _ => "light"
            };
        }

        /// <summary>
        /// Lấy icon cho trạng thái
        /// </summary>
        /// <param name="trangThai">Trạng thái vé</param>
        /// <returns>Class icon FontAwesome</returns>
        public static string GetStatusIcon(TrangThaiVe trangThai)
        {
            return trangThai switch
            {
                TrangThaiVe.DaDat => "fas fa-clock",
                TrangThaiVe.DaThanhToan => "fas fa-credit-card",
                TrangThaiVe.DaHoanThanh => "fas fa-check-circle",
                TrangThaiVe.DaHuy => "fas fa-times-circle",
                TrangThaiVe.DaSuDung => "fas fa-bus",
                TrangThaiVe.DaHoanTien => "fas fa-money-bill-wave",
                _ => "fas fa-question-circle"
            };
        }

        /// <summary>
        /// Kiểm tra xem có thể chuyển đổi trạng thái hay không
        /// </summary>
        /// <param name="currentStatus">Trạng thái hiện tại</param>
        /// <param name="newStatus">Trạng thái mới</param>
        /// <returns>Tuple chứa kết quả và thông báo lỗi</returns>
        public static (bool IsValid, string ErrorMessage) ValidateStatusChange(TrangThaiVe currentStatus, TrangThaiVe newStatus)
        {
            // Định nghĩa các chuyển đổi hợp lệ
            var validTransitions = new Dictionary<TrangThaiVe, List<TrangThaiVe>>
            {
                [TrangThaiVe.DaDat] = new List<TrangThaiVe> {
                    TrangThaiVe.DaThanhToan,
                    TrangThaiVe.DaHuy,
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHoanThanh
                },
                [TrangThaiVe.DaThanhToan] = new List<TrangThaiVe> {
                    TrangThaiVe.DaDat,
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHoanThanh,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaSuDung] = new List<TrangThaiVe> {
                    TrangThaiVe.DaThanhToan,
                    TrangThaiVe.DaHoanThanh,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaHuy] = new List<TrangThaiVe> {
                    TrangThaiVe.DaHoanTien,
                    TrangThaiVe.DaDat,
                    TrangThaiVe.DaThanhToan
                },
                [TrangThaiVe.DaHoanThanh] = new List<TrangThaiVe> {
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaHoanTien] = new List<TrangThaiVe> {
                    TrangThaiVe.DaHuy,
                    TrangThaiVe.DaDat
                }
            };

            if (currentStatus == newStatus)
            {
                return (false, "Trạng thái mới phải khác trạng thái hiện tại");
            }

            if (!validTransitions.ContainsKey(currentStatus))
            {
                return (false, "Trạng thái hiện tại không hợp lệ");
            }

            if (!validTransitions[currentStatus].Contains(newStatus))
            {
                return (false, $"Không thể chuyển từ trạng thái '{GetStatusText(currentStatus)}' sang '{GetStatusText(newStatus)}'");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Lấy tất cả trạng thái có thể chuyển đổi từ trạng thái hiện tại
        /// </summary>
        /// <param name="currentStatus">Trạng thái hiện tại</param>
        /// <returns>Danh sách trạng thái có thể chuyển đổi</returns>
        public static List<TrangThaiVe> GetValidTransitions(TrangThaiVe currentStatus)
        {
            var validTransitions = new Dictionary<TrangThaiVe, List<TrangThaiVe>>
            {
                [TrangThaiVe.DaDat] = new List<TrangThaiVe> {
                    TrangThaiVe.DaThanhToan,
                    TrangThaiVe.DaHuy,
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHoanThanh
                },
                [TrangThaiVe.DaThanhToan] = new List<TrangThaiVe> {
                    TrangThaiVe.DaDat,
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHoanThanh,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaSuDung] = new List<TrangThaiVe> {
                    TrangThaiVe.DaThanhToan,
                    TrangThaiVe.DaHoanThanh,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaHuy] = new List<TrangThaiVe> {
                    TrangThaiVe.DaHoanTien,
                    TrangThaiVe.DaDat,
                    TrangThaiVe.DaThanhToan
                },
                [TrangThaiVe.DaHoanThanh] = new List<TrangThaiVe> {
                    TrangThaiVe.DaSuDung,
                    TrangThaiVe.DaHuy
                },
                [TrangThaiVe.DaHoanTien] = new List<TrangThaiVe> {
                    TrangThaiVe.DaHuy,
                    TrangThaiVe.DaDat
                }
            };

            return validTransitions.ContainsKey(currentStatus) 
                ? validTransitions[currentStatus] 
                : new List<TrangThaiVe>();
        }

        /// <summary>
        /// Lấy text hiển thị từ Display attribute của enum
        /// </summary>
        /// <param name="enumValue">Giá trị enum</param>
        /// <returns>Text hiển thị</returns>
        public static string GetEnumDisplayName(this Enum enumValue)
        {
            return enumValue.GetType()
                .GetMember(enumValue.ToString())
                .First()
                .GetCustomAttribute<DisplayAttribute>()?
                .GetName() ?? enumValue.ToString();
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có phải là trạng thái cuối hay không
        /// </summary>
        /// <param name="trangThai">Trạng thái cần kiểm tra</param>
        /// <returns>True nếu là trạng thái cuối</returns>
        public static bool IsFinalStatus(TrangThaiVe trangThai)
        {
            return trangThai == TrangThaiVe.DaHoanThanh || 
                   trangThai == TrangThaiVe.DaHoanTien;
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có thể hủy hay không
        /// </summary>
        /// <param name="trangThai">Trạng thái cần kiểm tra</param>
        /// <returns>True nếu có thể hủy</returns>
        public static bool CanCancel(TrangThaiVe trangThai)
        {
            return trangThai == TrangThaiVe.DaDat || 
                   trangThai == TrangThaiVe.DaThanhToan ||
                   trangThai == TrangThaiVe.DaSuDung;
        }

        /// <summary>
        /// Kiểm tra xem trạng thái có thể hoàn tiền hay không
        /// </summary>
        /// <param name="trangThai">Trạng thái cần kiểm tra</param>
        /// <returns>True nếu có thể hoàn tiền</returns>
        public static bool CanRefund(TrangThaiVe trangThai)
        {
            return trangThai == TrangThaiVe.DaHuy;
        }
    }
}
