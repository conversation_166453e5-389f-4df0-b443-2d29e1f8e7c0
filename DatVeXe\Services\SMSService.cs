namespace DatVeXe.Services
{
    public class SMSService : ISMSService
    {
        private readonly ILogger<SMSService> _logger;

        public SMSService(ILogger<SMSService> logger)
        {
            _logger = logger;
        }

        public async Task<bool> SendSMSAsync(string phoneNumber, string message)
        {
            try
            {
                // Demo mode - Log SMS content instead of sending
                _logger.LogInformation($"[DEMO MODE] SMS would be sent to: {phoneNumber}");
                _logger.LogInformation($"[DEMO MODE] Message: {message}");

                // Simulate SMS sending delay
                await Task.Delay(100);

                _logger.LogInformation($"[DEMO MODE] SMS sent successfully to {phoneNumber}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send SMS to {phoneNumber}");
                return false;
            }
        }

        public async Task<bool> SendTicketConfirmationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            string tripInfo, string seatInfo, decimal price, DateTime departureTime)
        {
            var message = GenerateTicketConfirmationSMS(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);
            return await SendSMSAsync(phoneNumber, message);
        }

        public async Task<bool> SendTicketCancellationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            string tripInfo, string reason)
        {
            var message = GenerateTicketCancellationSMS(customerName, ticketCode, tripInfo, reason);
            return await SendSMSAsync(phoneNumber, message);
        }

        public async Task<bool> SendPaymentConfirmationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            decimal amount, string paymentMethod)
        {
            var message = GeneratePaymentConfirmationSMS(customerName, ticketCode, amount, paymentMethod);
            return await SendSMSAsync(phoneNumber, message);
        }

        private string GenerateTicketConfirmationSMS(string customerName, string ticketCode, string tripInfo, 
            string seatInfo, decimal price, DateTime departureTime)
        {
            return $"[DatVeXe] Xin chao {customerName}! " +
                   $"Ve cua ban da duoc dat thanh cong. " +
                   $"Ma ve: {ticketCode}. " +
                   $"Tuyen: {tripInfo}. " +
                   $"Ghe: {seatInfo}. " +
                   $"Gia: {price:N0}d. " +
                   $"Khoi hanh: {departureTime:dd/MM HH:mm}. " +
                   $"Cam on ban!";
        }

        private string GenerateTicketCancellationSMS(string customerName, string ticketCode, string tripInfo, string reason)
        {
            return $"[DatVeXe] Xin chao {customerName}! " +
                   $"Ve {ticketCode} ({tripInfo}) da duoc huy. " +
                   $"Ly do: {reason}. " +
                   $"Lien he 1900-xxxx de ho tro. " +
                   $"Cam on ban!";
        }

        private string GeneratePaymentConfirmationSMS(string customerName, string ticketCode, decimal amount, string paymentMethod)
        {
            return $"[DatVeXe] Xin chao {customerName}! " +
                   $"Thanh toan ve {ticketCode} thanh cong. " +
                   $"So tien: {amount:N0}d. " +
                   $"Phuong thuc: {paymentMethod}. " +
                   $"Cam on ban!";
        }
    }
}
