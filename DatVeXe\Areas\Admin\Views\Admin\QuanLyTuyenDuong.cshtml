@model IEnumerable<DatVeXe.Models.TuyenDuong>

@{
    ViewData["Title"] = "Quản lý tuyến đường";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-route" style="color: #f39c12;"></i>
        Quản lý tuyến đường
    </h3>
    <button class="btn" style="background-color: #f39c12; border-color: #f39c12; color: white;">
        <i class="fas fa-plus"></i>
        Thêm tuyến đường
    </button>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>ID</th>
                        <th>Tên tuyến</th>
                        <th><PERSON><PERSON><PERSON><PERSON> đi</th>
                        <th><PERSON><PERSON><PERSON><PERSON> đến</th>
                        <th>K<PERSON>ảng cách (km)</th>
                        <th>Th<PERSON><PERSON> gian (giờ)</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var tuyenDuong in Model)
                    {
                        <tr>
                            <td>@tuyenDuong.TuyenDuongId</td>
                            <td>@tuyenDuong.TenTuyen</td>
                            <td>@tuyenDuong.DiemDi</td>
                            <td>@tuyenDuong.DiemDen</td>
                            <td>@tuyenDuong.KhoangCach</td>
                            <td>@tuyenDuong.ThoiGianDuKien</td>
                            <td>
                                <span class="badge" style="background-color: #27ae60; color: white;">Hoạt động</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #3498db; color: #3498db;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #f39c12; color: #f39c12;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #e74c3c; color: #e74c3c;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-3">
    <small class="text-muted">
        Tổng cộng: @Model.Count() tuyến đường
    </small>
</div>
