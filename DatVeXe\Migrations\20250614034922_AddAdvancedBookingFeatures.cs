﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddAdvancedBookingFeatures : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Ves_KhuyenMais_KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropIndex(
                name: "IX_Ves_KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropColumn(
                name: "MauXe",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NamSanXuat",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropColumn(
                name: "SoTienGiam",
                table: "Ves");

            migrationBuilder.DropColumn(
                name: "ApDungChoKhachHangMoi",
                table: "<PERSON>huyenMais");

            migrationBuilder.DropColumn(
                name: "Phan<PERSON>ram<PERSON><PERSON>",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "SoTienGiamToiDa",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "ChatLuongXe",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "DungGio",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "GiaCa",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "ThaiDoTaiXe",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "TrangThai",
                table: "ChoNgois");

            migrationBuilder.RenameColumn(
                name: "HangXe",
                table: "Xes",
                newName: "NhaXe");

            migrationBuilder.RenameColumn(
                name: "GhiChu",
                table: "Xes",
                newName: "MoTa");

            migrationBuilder.RenameColumn(
                name: "SoLuongDaSuDung",
                table: "KhuyenMais",
                newName: "LoaiKhuyenMai");

            migrationBuilder.RenameColumn(
                name: "TrangThaiHienThi",
                table: "DanhGiaChuyenDis",
                newName: "CoKhuyenNghi");

            migrationBuilder.RenameColumn(
                name: "NhanXet",
                table: "DanhGiaChuyenDis",
                newName: "NoiDungDanhGia");

            migrationBuilder.RenameColumn(
                name: "NgayDanhGia",
                table: "DanhGiaChuyenDis",
                newName: "ThoiGianDanhGia");

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayTao",
                table: "Xes",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "DaSuDung",
                table: "KhuyenMais",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "GiaTri",
                table: "KhuyenMais",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "GiaTriToiDa",
                table: "KhuyenMais",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DanhGiaDichVu",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DanhGiaTaiXe",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DanhGiaXe",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "DanhBaHanhKhachs",
                columns: table => new
                {
                    DanhBaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NguoiDungId = table.Column<int>(type: "int", nullable: false),
                    TenHanhKhach = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SoDienThoai = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DiaChi = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    NgaySinh = table.Column<DateTime>(type: "datetime2", nullable: true),
                    GioiTinh = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    CCCD = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    NgayTao = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LanSuDungCuoi = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SoLanSuDung = table.Column<int>(type: "int", nullable: false),
                    GhiChu = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DanhBaHanhKhachs", x => x.DanhBaId);
                    table.ForeignKey(
                        name: "FK_DanhBaHanhKhachs_NguoiDungs_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "NguoiDungs",
                        principalColumn: "NguoiDungId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 1,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Phương Trang" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 2,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Mai Linh" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 3,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Thành Bưởi" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 4,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Phương Trang" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 5,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Hoàng Long" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 6,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Mai Linh" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 7,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Thành Bưởi" });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 8,
                columns: new[] { "NgayTao", "NhaXe" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Hoàng Long" });

            migrationBuilder.CreateIndex(
                name: "IX_KhuyenMais_MaKhuyenMai",
                table: "KhuyenMais",
                column: "MaKhuyenMai",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_KhuyenMais_NgayBatDau_NgayKetThuc",
                table: "KhuyenMais",
                columns: new[] { "NgayBatDau", "NgayKetThuc" });

            migrationBuilder.CreateIndex(
                name: "IX_DanhBaHanhKhachs_NguoiDungId_SoDienThoai",
                table: "DanhBaHanhKhachs",
                columns: new[] { "NguoiDungId", "SoDienThoai" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DanhBaHanhKhachs");

            migrationBuilder.DropIndex(
                name: "IX_KhuyenMais_MaKhuyenMai",
                table: "KhuyenMais");

            migrationBuilder.DropIndex(
                name: "IX_KhuyenMais_NgayBatDau_NgayKetThuc",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "NgayTao",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "DaSuDung",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "GiaTri",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "GiaTriToiDa",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "DanhGiaDichVu",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "DanhGiaTaiXe",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "DanhGiaXe",
                table: "DanhGiaChuyenDis");

            migrationBuilder.RenameColumn(
                name: "NhaXe",
                table: "Xes",
                newName: "HangXe");

            migrationBuilder.RenameColumn(
                name: "MoTa",
                table: "Xes",
                newName: "GhiChu");

            migrationBuilder.RenameColumn(
                name: "LoaiKhuyenMai",
                table: "KhuyenMais",
                newName: "SoLuongDaSuDung");

            migrationBuilder.RenameColumn(
                name: "ThoiGianDanhGia",
                table: "DanhGiaChuyenDis",
                newName: "NgayDanhGia");

            migrationBuilder.RenameColumn(
                name: "NoiDungDanhGia",
                table: "DanhGiaChuyenDis",
                newName: "NhanXet");

            migrationBuilder.RenameColumn(
                name: "CoKhuyenNghi",
                table: "DanhGiaChuyenDis",
                newName: "TrangThaiHienThi");

            migrationBuilder.AddColumn<string>(
                name: "MauXe",
                table: "Xes",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NamSanXuat",
                table: "Xes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "KhuyenMaiId",
                table: "Ves",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SoTienGiam",
                table: "Ves",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<bool>(
                name: "ApDungChoKhachHangMoi",
                table: "KhuyenMais",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "PhanTramGiam",
                table: "KhuyenMais",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SoTienGiamToiDa",
                table: "KhuyenMais",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ChatLuongXe",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DungGio",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "GiaCa",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ThaiDoTaiXe",
                table: "DanhGiaChuyenDis",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TrangThai",
                table: "ChoNgois",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 1,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 2,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 3,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 4,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 5,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 6,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 7,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 8,
                columns: new[] { "HangXe", "MauXe", "NamSanXuat" },
                values: new object[] { null, null, null });

            migrationBuilder.CreateIndex(
                name: "IX_Ves_KhuyenMaiId",
                table: "Ves",
                column: "KhuyenMaiId");

            migrationBuilder.AddForeignKey(
                name: "FK_Ves_KhuyenMais_KhuyenMaiId",
                table: "Ves",
                column: "KhuyenMaiId",
                principalTable: "KhuyenMais",
                principalColumn: "KhuyenMaiId");
        }
    }
}
