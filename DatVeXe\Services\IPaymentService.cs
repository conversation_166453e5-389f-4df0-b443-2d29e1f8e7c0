using DatVeXe.Models;

namespace DatVeXe.Services
{
    public interface IPaymentService
    {
        Task<PaymentResult> CreatePaymentAsync(PaymentRequest request);
        Task<PaymentResult> ProcessPaymentCallbackAsync(string transactionId, Dictionary<string, string> parameters);
        Task<PaymentResult> CheckPaymentStatusAsync(string transactionId);
        Task<bool> RefundPaymentAsync(string transactionId, decimal amount, string reason);
        string GeneratePaymentUrl(PhuongThucThanhToan method, string transactionId, decimal amount, string orderInfo, string returnUrl);
    }

    // Payment result classes
    public class PaymentResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? PaymentUrl { get; set; }
        public string? TransactionId { get; set; }
        public TrangThaiThanhToan Status { get; set; }
        public DateTime? PaymentTime { get; set; }
        public string? ErrorCode { get; set; }
        public Dictionary<string, string>? AdditionalData { get; set; }

        public static PaymentResult CreateSuccess(string message, string? paymentUrl = null, string? transactionId = null)
        {
            return new PaymentResult
            {
                Success = true,
                Message = message,
                PaymentUrl = paymentUrl,
                TransactionId = transactionId,
                Status = TrangThaiThanhToan.ThanhCong
            };
        }

        public static PaymentResult CreateFailed(string message, string? errorCode = null)
        {
            return new PaymentResult
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode,
                Status = TrangThaiThanhToan.ThatBai
            };
        }
    }

    public class PaymentRequest
    {
        public int TicketId { get; set; }
        public decimal Amount { get; set; }
        public PhuongThucThanhToan Method { get; set; }
        public string? OrderInfo { get; set; }
        public string? ReturnUrl { get; set; }
        public string? CancelUrl { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public string? CustomerEmail { get; set; }
    }
}
