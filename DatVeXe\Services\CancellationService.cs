using DatVeXe.Models;
using DatVeXe.Middleware;
using Microsoft.EntityFrameworkCore;

namespace DatVeXe.Services
{
    public interface ITicketCancellationService
    {
        Task<TicketCancellationResult> CanCancelTicketAsync(int ticketId, int? userId = null);
        Task<TicketCancellationResult> CancelTicketAsync(int ticketId, string reason, int? userId = null, bool isAdminAction = false);
        Task<TicketRefundResult> CalculateRefundAsync(int ticketId);
        Task<TicketRefundResult> ProcessRefundAsync(int ticketId, string reason);
    }

    public class TicketCancellationService : ITicketCancellationService
    {
        private readonly DatVeXeContext _context;
        private readonly IEmailService _emailService;
        private readonly ISMSService _smsService;
        private readonly ILogger<TicketCancellationService> _logger;

        public TicketCancellationService(
            DatVeXeContext context,
            IEmailService emailService,
            ISMSService smsService,
            ILogger<TicketCancellationService> logger)
        {
            _context = context;
            _emailService = emailService;
            _smsService = smsService;
            _logger = logger;
        }

        public async Task<TicketCancellationResult> CanCancelTicketAsync(int ticketId, int? userId = null)
        {
            try
            {
                var ticket = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .Include(v => v.ThanhToans)
                    .FirstOrDefaultAsync(v => v.VeId == ticketId);

                if (ticket == null)
                {
                    return TicketCancellationResult.Failed("Không tìm thấy vé");
                }

                // Kiểm tra quyền sở hữu (nếu không phải admin)
                if (userId.HasValue && ticket.NguoiDungId != userId.Value)
                {
                    return TicketCancellationResult.Failed("Bạn không có quyền hủy vé này");
                }

                // Kiểm tra trạng thái vé
                if (ticket.VeTrangThai == TrangThaiVe.DaHuy)
                {
                    return TicketCancellationResult.Failed("Vé đã bị hủy trước đó");
                }

                if (ticket.VeTrangThai == TrangThaiVe.DaSuDung)
                {
                    return TicketCancellationResult.Failed("Vé đã được sử dụng, không thể hủy");
                }

                // Kiểm tra thời gian hủy
                if (ticket.ChuyenXe == null)
                {
                    return TicketCancellationResult.Failed("Không tìm thấy thông tin chuyến xe");
                }

                var now = DateTime.Now;
                var departureTime = ticket.ChuyenXe.NgayKhoiHanh;
                var hoursUntilDeparture = (departureTime - now).TotalHours;

                // Quy định hủy vé
                if (hoursUntilDeparture < 2)
                {
                    return TicketCancellationResult.Failed("Không thể hủy vé trong vòng 2 giờ trước giờ khởi hành");
                }

                if (departureTime <= now)
                {
                    return TicketCancellationResult.Failed("Chuyến xe đã khởi hành, không thể hủy vé");
                }

                // Tính toán phí hủy và hoàn tiền
                var refundResult = await CalculateRefundAsync(ticketId);

                return TicketCancellationResult.CreateSuccess("Có thể hủy vé", new
                {
                    CanCancel = true,
                    RefundAmount = refundResult.RefundAmount,
                    CancellationFee = refundResult.CancellationFee,
                    RefundPercentage = refundResult.RefundPercentage,
                    HoursUntilDeparture = hoursUntilDeparture
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking cancellation eligibility for ticket {ticketId}");
                return TicketCancellationResult.Failed("Có lỗi xảy ra khi kiểm tra điều kiện hủy vé");
            }
        }

        public async Task<TicketCancellationResult> CancelTicketAsync(int ticketId, string reason, int? userId = null, bool isAdminAction = false)
        {
            try
            {
                // Kiểm tra điều kiện hủy
                if (!isAdminAction)
                {
                    var canCancelResult = await CanCancelTicketAsync(ticketId, userId);
                    if (!canCancelResult.Success)
                    {
                        return canCancelResult;
                    }
                }

                var ticket = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .Include(v => v.ChoNgoi)
                    .Include(v => v.ThanhToans)
                    .FirstOrDefaultAsync(v => v.VeId == ticketId);

                if (ticket == null)
                {
                    return TicketCancellationResult.Failed("Không tìm thấy vé");
                }

                // Cập nhật trạng thái vé
                ticket.VeTrangThai = TrangThaiVe.DaHuy;
                ticket.NgayHuy = DateTime.Now;
                ticket.LyDoHuy = reason;
                ticket.NgayCapNhat = DateTime.Now;

                // Giải phóng ghế (nếu có)
                if (ticket.ChoNgoi != null)
                {
                    _logger.LogInformation($"Released seat {ticket.ChoNgoi.SoGhe} for cancelled ticket {ticket.MaVe}");
                }

                // Xử lý hoàn tiền (nếu đã thanh toán)
                var hasSuccessfulPayment = ticket.ThanhToans?.Any(t => t.TrangThai == TrangThaiThanhToan.ThanhCong) ?? false;
                TicketRefundResult? refundResult = null;

                if (hasSuccessfulPayment)
                {
                    refundResult = await ProcessRefundAsync(ticketId, reason);
                }

                await _context.SaveChangesAsync();

                // Gửi thông báo hủy vé
                await SendCancellationNotificationsAsync(ticket, reason, refundResult);

                _logger.LogInformation($"Ticket {ticket.MaVe} cancelled successfully. Reason: {reason}");

                return TicketCancellationResult.CreateSuccess("Hủy vé thành công", new
                {
                    TicketCode = ticket.MaVe,
                    RefundInfo = refundResult,
                    CancellationTime = ticket.NgayHuy
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error cancelling ticket {ticketId}");
                return TicketCancellationResult.Failed("Có lỗi xảy ra khi hủy vé. Vui lòng thử lại.");
            }
        }

        public async Task<TicketRefundResult> CalculateRefundAsync(int ticketId)
        {
            try
            {
                var ticket = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .Include(v => v.ThanhToans)
                    .FirstOrDefaultAsync(v => v.VeId == ticketId);

                if (ticket == null)
                {
                    return TicketRefundResult.Failed("Không tìm thấy vé");
                }

                var originalAmount = ticket.GiaVe;
                var hasPayment = ticket.ThanhToans?.Any(t => t.TrangThai == TrangThaiThanhToan.ThanhCong) ?? false;

                if (!hasPayment)
                {
                    return TicketRefundResult.CreateSuccess(0, 0, 0, "Vé chưa thanh toán, không có hoàn tiền");
                }

                if (ticket.ChuyenXe == null)
                {
                    return TicketRefundResult.Failed("Không tìm thấy thông tin chuyến xe");
                }

                var now = DateTime.Now;
                var departureTime = ticket.ChuyenXe.NgayKhoiHanh;
                var hoursUntilDeparture = (departureTime - now).TotalHours;

                // Quy định hoàn tiền theo thời gian hủy
                decimal refundPercentage = 0;
                decimal cancellationFeePercentage = 0;

                if (hoursUntilDeparture >= 24)
                {
                    refundPercentage = 90; // Hoàn 90%
                    cancellationFeePercentage = 10;
                }
                else if (hoursUntilDeparture >= 12)
                {
                    refundPercentage = 80; // Hoàn 80%
                    cancellationFeePercentage = 20;
                }
                else if (hoursUntilDeparture >= 6)
                {
                    refundPercentage = 70; // Hoàn 70%
                    cancellationFeePercentage = 30;
                }
                else if (hoursUntilDeparture >= 2)
                {
                    refundPercentage = 50; // Hoàn 50%
                    cancellationFeePercentage = 50;
                }
                else
                {
                    refundPercentage = 0; // Không hoàn tiền
                    cancellationFeePercentage = 100;
                }

                var refundAmount = originalAmount * refundPercentage / 100;
                var cancellationFee = originalAmount * cancellationFeePercentage / 100;

                return TicketRefundResult.CreateSuccess(refundAmount, cancellationFee, refundPercentage, 
                    $"Hoàn {refundPercentage}% giá trị vé");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calculating refund for ticket {ticketId}");
                return TicketRefundResult.Failed("Có lỗi xảy ra khi tính toán hoàn tiền");
            }
        }

        public async Task<TicketRefundResult> ProcessRefundAsync(int ticketId, string reason)
        {
            try
            {
                var refundCalculation = await CalculateRefundAsync(ticketId);
                if (!refundCalculation.Success || refundCalculation.RefundAmount <= 0)
                {
                    return refundCalculation;
                }

                var ticket = await _context.Ves
                    .Include(v => v.ThanhToans)
                    .FirstOrDefaultAsync(v => v.VeId == ticketId);

                if (ticket == null)
                {
                    return TicketRefundResult.Failed("Không tìm thấy vé");
                }

                // Tạo bản ghi hoàn tiền
                var refundTransaction = new ThanhToan
                {
                    VeId = ticketId,
                    SoTien = -refundCalculation.RefundAmount, // Số âm để đánh dấu hoàn tiền
                    PhuongThucThanhToan = PhuongThucThanhToan.HoanTien,
                    TrangThai = TrangThaiThanhToan.DangXuLy,
                    NgayThanhToan = DateTime.Now,
                    ThoiGianTao = DateTime.Now,
                    MaGiaoDich = $"REFUND-{DateTime.Now:yyyyMMddHHmmss}-{ticketId}",
                    GhiChu = $"Hoàn tiền hủy vé. Lý do: {reason}"
                };

                _context.ThanhToans.Add(refundTransaction);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Refund processed for ticket {ticket.MaVe}: {refundCalculation.RefundAmount:N0} VNĐ");

                return TicketRefundResult.CreateSuccess(
                    refundCalculation.RefundAmount,
                    refundCalculation.CancellationFee,
                    refundCalculation.RefundPercentage,
                    $"Hoàn tiền {refundCalculation.RefundAmount:N0} VNĐ sẽ được xử lý trong 3-7 ngày làm việc"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing refund for ticket {ticketId}");
                return TicketRefundResult.Failed("Có lỗi xảy ra khi xử lý hoàn tiền");
            }
        }

        private async Task SendCancellationNotificationsAsync(Ve ticket, string reason, TicketRefundResult? refundResult)
        {
            try
            {
                var tripInfo = ticket.ChuyenXe != null 
                    ? $"{ticket.ChuyenXe.DiemDiDisplay} - {ticket.ChuyenXe.DiemDenDisplay}"
                    : "N/A";

                // Gửi email
                if (!string.IsNullOrEmpty(ticket.Email))
                {
                    await _emailService.SendTicketCancellationAsync(
                        ticket.Email, ticket.TenKhach, ticket.MaVe, tripInfo, reason);
                }

                // Gửi SMS
                if (!string.IsNullOrEmpty(ticket.SoDienThoai))
                {
                    var smsMessage = $"Vé {ticket.MaVe} đã được hủy. ";
                    if (refundResult?.Success == true && refundResult.RefundAmount > 0)
                    {
                        smsMessage += $"Hoàn tiền: {refundResult.RefundAmount:N0} VNĐ trong 3-7 ngày.";
                    }
                    
                    await _smsService.SendSMSAsync(ticket.SoDienThoai, smsMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending cancellation notifications for ticket {ticket.MaVe}");
            }
        }
    }

    // Result classes
    public class TicketCancellationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }

        public static TicketCancellationResult CreateSuccess(string message, object? data = null)
        {
            return new TicketCancellationResult { Success = true, Message = message, Data = data };
        }

        public static TicketCancellationResult Failed(string message)
        {
            return new TicketCancellationResult { Success = false, Message = message };
        }
    }

    public class TicketRefundResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public decimal RefundAmount { get; set; }
        public decimal CancellationFee { get; set; }
        public decimal RefundPercentage { get; set; }

        public static TicketRefundResult CreateSuccess(decimal refundAmount, decimal cancellationFee, decimal refundPercentage, string message)
        {
            return new TicketRefundResult 
            { 
                Success = true, 
                Message = message, 
                RefundAmount = refundAmount,
                CancellationFee = cancellationFee,
                RefundPercentage = refundPercentage
            };
        }

        public static TicketRefundResult Failed(string message)
        {
            return new TicketRefundResult { Success = false, Message = message };
        }
    }
}
