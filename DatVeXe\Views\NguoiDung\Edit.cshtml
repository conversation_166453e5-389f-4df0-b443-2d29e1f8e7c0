@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Chỉnh sửa người dùng";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-edit"></i> @ViewData["Title"]
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" id="editUserForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <input type="hidden" asp-for="NguoiDungId" />
                        
                        <div class="mb-3">
                            <label asp-for="HoTen" class="form-label">
                                <i class="fas fa-user"></i> @Html.DisplayNameFor(model => model.HoTen)
                            </label>
                            <input asp-for="HoTen" class="form-control" placeholder="Nhập họ tên đầy đủ" />
                            <span asp-validation-for="HoTen" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <input asp-for="Email" class="form-control" type="email" placeholder="Nhập địa chỉ email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="MatKhau" class="form-label">
                                <i class="fas fa-lock"></i> Mật khẩu mới (để trống nếu không đổi)
                            </label>
                            <div class="input-group">
                                <input asp-for="MatKhau" class="form-control" type="password" placeholder="Nhập mật khẩu mới" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="MatKhau" class="text-danger"></span>
                            <div class="form-text">Để trống nếu không muốn thay đổi mật khẩu. Nếu nhập, mật khẩu phải có ít nhất 6 ký tự</div>
                        </div>

                        <div class="mb-3" id="confirmPasswordGroup" style="display: none;">
                            <label for="XacNhanMatKhau" class="form-label">
                                <i class="fas fa-lock"></i> Xác nhận mật khẩu mới
                            </label>
                            <div class="input-group">
                                <input id="XacNhanMatKhau" name="XacNhanMatKhau" class="form-control" type="password" placeholder="Nhập lại mật khẩu mới" />
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye" id="toggleConfirmIcon"></i>
                                </button>
                            </div>
                            <span id="confirmPasswordError" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="LaAdmin" class="form-check-input" type="checkbox" />
                                <label asp-for="LaAdmin" class="form-check-label">
                                    <i class="fas fa-crown"></i> @Html.DisplayNameFor(model => model.LaAdmin)
                                </label>
                            </div>
                            <div class="form-text">Chỉ tích chọn nếu muốn cấp quyền quản trị cho người dùng này</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Cập nhật
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.NguoiDungId" class="btn btn-info">
                                <i class="fas fa-info-circle"></i> Chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            var form = $('#editUserForm');
            var passwordField = $('#MatKhau');
            var confirmGroup = $('#confirmPasswordGroup');
            var confirmField = $('#XacNhanMatKhau');
            
            // Show/hide confirm password field based on password input
            passwordField.on('input', function() {
                if ($(this).val().length > 0) {
                    confirmGroup.show();
                    confirmField.attr('required', true);
                } else {
                    confirmGroup.hide();
                    confirmField.attr('required', false);
                    confirmField.val('');
                }
            });

            // Toggle password visibility
            $('#togglePassword').click(function() {
                var icon = $('#toggleIcon');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Toggle confirm password visibility
            $('#toggleConfirmPassword').click(function() {
                var icon = $('#toggleConfirmIcon');
                
                if (confirmField.attr('type') === 'password') {
                    confirmField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    confirmField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Custom validation for password confirmation
            $.validator.addMethod('passwordMatch', function(value, element) {
                if (passwordField.val().length === 0) return true; // Skip if no new password
                return value === passwordField.val();
            }, 'Mật khẩu xác nhận không khớp');

            confirmField.rules("add", {
                passwordMatch: true
            });

            // Handle form submission
            form.on('submit', function(e) {
                // Validate password confirmation if password is provided
                if (passwordField.val().length > 0 && confirmField.val() !== passwordField.val()) {
                    e.preventDefault();
                    $('#confirmPasswordError').text('Mật khẩu xác nhận không khớp');
                    confirmField.focus();
                    return false;
                } else {
                    $('#confirmPasswordError').text('');
                }

                if (!form.valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    var firstError = $('.field-validation-error, .text-danger').filter(':visible').first();
                    if (firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 200);
                    }
                    return false;
                }
                
                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang cập nhật...');
                return true;
            });
        });
    </script>
}
