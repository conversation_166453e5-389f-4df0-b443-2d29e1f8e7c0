﻿CREATE TABLE [Nguo<PERSON>Dungs] (
    [NguoiDungId] int NOT NULL IDENTITY,
    [HoTen] nvarchar(100) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [<PERSON><PERSON><PERSON><PERSON>] nvarchar(100) NOT NULL,
    [LaAdmin] bit NOT NULL,
    CONSTRAINT [PK_NguoiDungs] PRIMARY KEY ([NguoiDungId])
);
GO


CREATE TABLE [Xes] (
    [XeId] int NOT NULL IDENTITY,
    [BienSo] nvarchar(50) NOT NULL,
    [LoaiXe] nvarchar(100) NOT NULL,
    [SoGhe] int NOT NULL,
    CONSTRAINT [PK_Xes] PRIMARY KEY ([XeId])
);
GO


CREATE TABLE [ChuyenXes] (
    [ChuyenXeId] int NOT NULL IDENTITY,
    [DiemDi] nvarchar(max) NOT NULL,
    [Diem<PERSON><PERSON>] nvarchar(max) NOT NULL,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>Hanh] datetime2 NOT NULL,
    [XeId] int NOT NULL,
    CONSTRAINT [PK_ChuyenXes] PRIMARY KEY ([ChuyenXeId]),
    CONSTRAINT [FK_ChuyenXes_Xes_XeId] FOREIGN KEY ([XeId]) REFERENCES [Xes] ([XeId]) ON DELETE CASCADE
);
GO


CREATE TABLE [Ves] (
    [VeId] int NOT NULL IDENTITY,
    [ChuyenXeId] int NOT NULL,
    [TenKhach] nvarchar(max) NOT NULL,
    [SoDienThoai] nvarchar(max) NOT NULL,
    [NgayDat] datetime2 NOT NULL,
    CONSTRAINT [PK_Ves] PRIMARY KEY ([VeId]),
    CONSTRAINT [FK_Ves_ChuyenXes_ChuyenXeId] FOREIGN KEY ([ChuyenXeId]) REFERENCES [ChuyenXes] ([ChuyenXeId]) ON DELETE CASCADE
);
GO


IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'NguoiDungId', N'Email', N'HoTen', N'LaAdmin', N'MatKhau') AND [object_id] = OBJECT_ID(N'[NguoiDungs]'))
    SET IDENTITY_INSERT [NguoiDungs] ON;
INSERT INTO [NguoiDungs] ([NguoiDungId], [Email], [HoTen], [LaAdmin], [MatKhau])
VALUES (1, N'<EMAIL>', N'Admin', CAST(1 AS bit), N'admin123'),
(2, N'<EMAIL>', N'Nguyen Van A', CAST(0 AS bit), N'123456');
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'NguoiDungId', N'Email', N'HoTen', N'LaAdmin', N'MatKhau') AND [object_id] = OBJECT_ID(N'[NguoiDungs]'))
    SET IDENTITY_INSERT [NguoiDungs] OFF;
GO


IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'XeId', N'BienSo', N'LoaiXe', N'SoGhe') AND [object_id] = OBJECT_ID(N'[Xes]'))
    SET IDENTITY_INSERT [Xes] ON;
INSERT INTO [Xes] ([XeId], [BienSo], [LoaiXe], [SoGhe])
VALUES (1, N'51A-12345', N'Giường nằm', 40),
(2, N'51B-67890', N'Limousine', 20);
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'XeId', N'BienSo', N'LoaiXe', N'SoGhe') AND [object_id] = OBJECT_ID(N'[Xes]'))
    SET IDENTITY_INSERT [Xes] OFF;
GO


IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'ChuyenXeId', N'DiemDen', N'DiemDi', N'NgayKhoiHanh', N'XeId') AND [object_id] = OBJECT_ID(N'[ChuyenXes]'))
    SET IDENTITY_INSERT [ChuyenXes] ON;
INSERT INTO [ChuyenXes] ([ChuyenXeId], [DiemDen], [DiemDi], [NgayKhoiHanh], [XeId])
VALUES (1, N'Nha Trang', N'TP.HCM', '2025-06-01T08:00:00.0000000', 1),
(2, N'Đà Lạt', N'TP.HCM', '2025-06-02T07:00:00.0000000', 2);
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'ChuyenXeId', N'DiemDen', N'DiemDi', N'NgayKhoiHanh', N'XeId') AND [object_id] = OBJECT_ID(N'[ChuyenXes]'))
    SET IDENTITY_INSERT [ChuyenXes] OFF;
GO


CREATE INDEX [IX_ChuyenXes_XeId] ON [ChuyenXes] ([XeId]);
GO


CREATE INDEX [IX_Ves_ChuyenXeId] ON [Ves] ([ChuyenXeId]);
GO


