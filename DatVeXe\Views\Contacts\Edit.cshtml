@model DanhBaHanhKhach
@{
    ViewData["Title"] = "Chỉnh sửa liên hệ";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-warning text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="bi bi-person-gear me-2"></i>Chỉnh sửa liên hệ
                    </h2>
                    <p class="mb-0 mt-2">Cập nhật thông tin hành khách trong danh bạ</p>
                </div>

                <div class="card-body p-4">
                    <form asp-action="Edit" method="post" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <input type="hidden" asp-for="DanhBaId" />
                        <input type="hidden" asp-for="NguoiDungId" />
                        <input type="hidden" asp-for="NgayTao" />
                        <input type="hidden" asp-for="LanSuDungCuoi" />

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="TenHanhKhach" class="form-label fw-bold text-dark">
                                    <i class="bi bi-person me-2"></i>@Html.DisplayNameFor(model => model.TenHanhKhach) <span class="text-danger">*</span>
                                </label>
                                <input asp-for="TenHanhKhach" class="form-control form-control-lg" placeholder="Nhập tên hành khách" />
                                <span asp-validation-for="TenHanhKhach" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="SoDienThoai" class="form-label fw-bold text-dark">
                                    <i class="bi bi-telephone me-2"></i>@Html.DisplayNameFor(model => model.SoDienThoai) <span class="text-danger">*</span>
                                </label>
                                <input asp-for="SoDienThoai" class="form-control form-control-lg" placeholder="Nhập số điện thoại" />
                                <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label fw-bold text-dark">
                                    <i class="bi bi-envelope me-2"></i>@Html.DisplayNameFor(model => model.Email)
                                </label>
                                <input asp-for="Email" type="email" class="form-control form-control-lg" placeholder="Nhập email (tùy chọn)" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="NgaySinh" class="form-label fw-bold text-dark">
                                    <i class="bi bi-calendar me-2"></i>@Html.DisplayNameFor(model => model.NgaySinh)
                                </label>
                                <input asp-for="NgaySinh" type="date" class="form-control form-control-lg" />
                                <span asp-validation-for="NgaySinh" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="GioiTinh" class="form-label fw-bold text-dark">
                                    <i class="bi bi-gender-ambiguous me-2"></i>@Html.DisplayNameFor(model => model.GioiTinh)
                                </label>
                                <select asp-for="GioiTinh" class="form-select form-select-lg">
                                    <option value="">-- Chọn giới tính --</option>
                                    <option value="Nam">Nam</option>
                                    <option value="Nữ">Nữ</option>
                                    <option value="Khác">Khác</option>
                                </select>
                                <span asp-validation-for="GioiTinh" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="CCCD" class="form-label fw-bold text-dark">
                                    <i class="bi bi-credit-card me-2"></i>@Html.DisplayNameFor(model => model.CCCD)
                                </label>
                                <input asp-for="CCCD" class="form-control form-control-lg" placeholder="Nhập số CCCD/CMND" />
                                <span asp-validation-for="CCCD" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="DiaChi" class="form-label fw-bold text-dark">
                                <i class="bi bi-geo-alt me-2"></i>@Html.DisplayNameFor(model => model.DiaChi)
                            </label>
                            <textarea asp-for="DiaChi" class="form-control" rows="3" placeholder="Nhập địa chỉ (tùy chọn)"></textarea>
                            <span asp-validation-for="DiaChi" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="GhiChu" class="form-label fw-bold text-dark">
                                <i class="bi bi-chat-text me-2"></i>@Html.DisplayNameFor(model => model.GhiChu)
                            </label>
                            <textarea asp-for="GhiChu" class="form-control" rows="3" placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                            <span asp-validation-for="GhiChu" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button type="submit" class="btn btn-warning btn-lg px-5 me-md-2">
                                <i class="bi bi-check-circle me-2"></i>Cập nhật liên hệ
                            </button>
                            <a asp-action="Index" class="btn btn-outline-secondary btn-lg px-5">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .card-header {
        border-radius: 0;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }
    
    .btn {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
        transform: translateY(-2px);
    }
    
    .btn-outline-secondary:hover {
        transform: translateY(-2px);
    }
    
    .form-label {
        margin-bottom: 0.75rem;
    }
    
    .text-danger {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
