@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Chi tiết người dùng";
    var userTickets = ViewBag.UserTickets as List<DatVeXe.Models.Ve> ?? new List<DatVeXe.Models.Ve>();
    var totalTickets = ViewBag.TotalTickets as int? ?? 0;
    var totalSpent = ViewBag.TotalSpent as decimal? ?? 0;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user"></i> @ViewData["Title"] - @Model.HoTen
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-user"></i> Họ tên:</strong></td>
                                    <td><span class="text-primary fw-bold">@Model.HoTen</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-envelope"></i> Email:</strong></td>
                                    <td><span class="text-muted">@Model.Email</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-shield-alt"></i> Quyền:</strong></td>
                                    <td>
                                        @if (Model.LaAdmin)
                                        {
                                            <span class="badge bg-danger fs-6">
                                                <i class="fas fa-crown"></i> Admin
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-info fs-6">
                                                <i class="fas fa-user"></i> Khách hàng
                                            </span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-ticket-alt"></i> Tổng vé đã đặt:</strong></td>
                                    <td><span class="badge bg-primary fs-6">@totalTickets vé</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-money-bill-wave"></i> Tổng chi tiêu:</strong></td>
                                    <td><span class="badge bg-success fs-6">@totalSpent.ToString("N0") VNĐ</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-calendar-alt"></i> Vé gần nhất:</strong></td>
                                    <td>
                                        @if (userTickets.Any())
                                        {
                                            <span class="text-muted">@userTickets.First().NgayDat.ToString("dd/MM/yyyy")</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="d-flex gap-2 mt-3">
                        <a asp-action="Edit" asp-route-id="@Model.NguoiDungId" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        @if (totalTickets == 0)
                        {
                            <a asp-action="Delete" asp-route-id="@Model.NguoiDungId" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Xóa
                            </a>
                        }
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Thống kê
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">@totalTickets</h4>
                            <small class="text-muted">Vé đã đặt</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-success">@totalSpent.ToString("N0")</h4>
                            <small class="text-muted">VNĐ đã chi tiêu</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-info">
                                @(totalTickets > 0 ? (totalSpent / totalTickets).ToString("N0") : "0")
                            </h4>
                            <small class="text-muted">VNĐ trung bình/vé</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (userTickets.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> Lịch sử đặt vé
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Ngày đặt</th>
                                        <th>Tên khách</th>
                                        <th>Số điện thoại</th>
                                        <th>Chuyến xe</th>
                                        <th>Ngày khởi hành</th>
                                        <th>Giá vé</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var ve in userTickets)
                                    {
                                        var daDi = ve.ChuyenXe != null && ve.ChuyenXe.NgayKhoiHanh <= DateTime.Now;
                                        
                                        <tr>
                                            <td>@ve.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@ve.TenKhach</td>
                                            <td>@ve.SoDienThoai</td>
                                            <td>@(ve.ChuyenXe != null ? $"{ve.ChuyenXe.DiemDi} - {ve.ChuyenXe.DiemDen}" : "N/A")</td>
                                            <td>@(ve.ChuyenXe != null ? ve.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm") : "N/A")</td>
                                            <td>@(ve.ChuyenXe != null ? $"{ve.ChuyenXe.Gia.ToString("N0")} VNĐ" : "N/A")</td>
                                            <td>
                                                @if (daDi)
                                                {
                                                    <span class="badge bg-secondary">Đã đi</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">Chưa đi</span>
                                                }
                                            </td>
                                            <td>
                                                <a asp-controller="Ve" asp-action="Details" asp-route-id="@ve.VeId" 
                                                   class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Người dùng này chưa đặt vé nào</h5>
                        <p class="text-muted">Khi có vé được đặt, thông tin sẽ hiển thị ở đây.</p>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
