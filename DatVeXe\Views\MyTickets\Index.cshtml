@model MyTicketsViewModel
@{
    ViewData["Title"] = "Vé của tôi";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                </h2>
                <div>
                    <a asp-controller="Booking" asp-action="Search" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                    <a asp-controller="Contacts" asp-action="Index" class="btn btn-outline-secondary">
                        <i class="bi bi-person-lines-fill me-2"></i>Danh bạ
                    </a>
                </div>
            </div>

            <!-- Thống kê tổng quan -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-ticket-perforated display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.TongVe</h3>
                            <p class="mb-0">Tổng số vé</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-currency-dollar display-4 mb-2"></i>
                            <h3 class="fw-bold">@string.Format("{0:N0}", Model.TongChiTieu)</h3>
                            <p class="mb-0">Tổng chi tiêu (VNĐ)</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.VeSapDi.Count</h3>
                            <p class="mb-0">Vé sắp đi</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-star display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.VeChuaDanhGia</h3>
                            <p class="mb-0">Chưa đánh giá</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bộ lọc -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Trạng thái</label>
                            <select name="trangThaiLoc" class="form-select">
                                <option value="">Tất cả trạng thái</option>
                                <option value="DaDat" selected="@(Model.TrangThaiLoc == "DaDat")">Đã đặt</option>
                                <option value="DaThanhToan" selected="@(Model.TrangThaiLoc == "DaThanhToan")">Đã thanh toán</option>
                                <option value="DaHoanThanh" selected="@(Model.TrangThaiLoc == "DaHoanThanh")">Đã hoàn thành</option>
                                <option value="DaHuy" selected="@(Model.TrangThaiLoc == "DaHuy")">Đã hủy</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Từ ngày</label>
                            <input type="date" name="tuNgay" class="form-control" value="@(Model.TuNgay.HasValue ? Model.TuNgay.Value.ToString("yyyy-MM-dd") : "")" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Đến ngày</label>
                            <input type="date" name="denNgay" class="form-control" value="@(Model.DenNgay.HasValue ? Model.DenNgay.Value.ToString("yyyy-MM-dd") : "")" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Tìm kiếm</label>
                            <div class="input-group">
                                <input type="text" name="searchTerm" class="form-control" placeholder="Mã vé, tên, SĐT..." value="@Model.SearchTerm" />
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tabs -->
            <ul class="nav nav-tabs nav-fill mb-4" id="ticketTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        <i class="bi bi-list-ul me-2"></i>Tất cả (@Model.TongVe)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab">
                        <i class="bi bi-clock me-2"></i>Sắp đi (@Model.VeSapDi.Count)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
                        <i class="bi bi-check-circle me-2"></i>Đã hoàn thành (@Model.VeDaHoanThanh.Count)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab">
                        <i class="bi bi-x-circle me-2"></i>Đã hủy (@Model.VeDaHuy.Count)
                    </button>
                </li>
            </ul>

            <!-- Tab content -->
            <div class="tab-content" id="ticketTabsContent">
                <!-- Tất cả vé -->
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    @await Html.PartialAsync("_TicketList", Model.VeDaDat.Concat(Model.VeDaHoanThanh).Concat(Model.VeDaHuy).OrderByDescending(v => v.NgayDat))
                </div>

                <!-- Vé sắp đi -->
                <div class="tab-pane fade" id="upcoming" role="tabpanel">
                    @await Html.PartialAsync("_TicketList", Model.VeSapDi.OrderBy(v => v.ChuyenXe!.NgayKhoiHanh))
                </div>

                <!-- Vé đã hoàn thành -->
                <div class="tab-pane fade" id="completed" role="tabpanel">
                    @await Html.PartialAsync("_TicketList", Model.VeDaHoanThanh.OrderByDescending(v => v.ChuyenXe!.NgayKhoiHanh))
                </div>

                <!-- Vé đã hủy -->
                <div class="tab-pane fade" id="cancelled" role="tabpanel">
                    @await Html.PartialAsync("_TicketList", Model.VeDaHuy.OrderByDescending(v => v.NgayDat))
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Auto refresh upcoming tickets every 5 minutes
            setInterval(function() {
                if ($('#upcoming-tab').hasClass('active')) {
                    location.reload();
                }
            }, 300000); // 5 minutes
        });
    </script>
}

<style>
    .card {
        border-radius: 15px;
        transition: transform 0.2s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .nav-tabs .nav-link {
        border-radius: 10px 10px 0 0;
        font-weight: 600;
    }
    
    .nav-tabs .nav-link.active {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }
    
    .badge {
        font-size: 0.75em;
        padding: 0.5em 0.75em;
    }
</style>
