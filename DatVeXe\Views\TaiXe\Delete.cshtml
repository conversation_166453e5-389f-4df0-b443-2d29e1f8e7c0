@model DatVeXe.Models.TaiXe

@{
    ViewData["Title"] = "Xóa Tài xế";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Xác nhận xóa Tài xế
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Index" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa tài xế này không? Hành động này không thể hoàn tác.
                    </div>

                    <div class="row">
                        <!-- Thông tin tài xế sẽ bị xóa -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> Thông tin tài xế sẽ bị xóa</h5>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Họ tên:</strong></td>
                                    <td>@Html.DisplayFor(model => model.HoTen)</td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SoDienThoai)</td>
                                </tr>
                                <tr>
                                    <td><strong>CMND/CCCD:</strong></td>
                                    <td>@Html.DisplayFor(model => model.CMND)</td>
                                </tr>
                                <tr>
                                    <td><strong>Số bằng lái:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SoBangLai)</td>
                                </tr>
                                <tr>
                                    <td><strong>Loại bằng lái:</strong></td>
                                    <td>
                                        @switch (Model.LoaiBangLai)
                                        {
                                            case LoaiBangLai.B1:
                                                <span>B1 - Xe ô tô dưới 9 chỗ</span>
                                                break;
                                            case LoaiBangLai.B2:
                                                <span>B2 - Xe ô tô dưới 9 chỗ và xe tải dưới 3.5 tấn</span>
                                                break;
                                            case LoaiBangLai.C:
                                                <span>C - Xe tải và xe khách dưới 30 chỗ</span>
                                                break;
                                            case LoaiBangLai.D:
                                                <span>D - Xe khách trên 30 chỗ</span>
                                                break;
                                            case LoaiBangLai.E:
                                                <span>E - Xe container, xe đầu kéo</span>
                                                break;
                                            case LoaiBangLai.F:
                                                <span>F - Xe chuyên dụng</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td>
                                        @switch (Model.TrangThai)
                                        {
                                            case TrangThaiTaiXe.HoatDong:
                                                <span class="badge badge-success">Hoạt động</span>
                                                break;
                                            case TrangThaiTaiXe.NghiPhep:
                                                <span class="badge badge-info">Nghỉ phép</span>
                                                break;
                                            case TrangThaiTaiXe.TamNghi:
                                                <span class="badge badge-warning">Tạm nghỉ</span>
                                                break;
                                            case TrangThaiTaiXe.DaNghiViec:
                                                <span class="badge badge-danger">Đã nghỉ việc</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày vào làm:</strong></td>
                                    <td>@Model.NgayVaoLam.ToString("dd/MM/yyyy")</td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-info-circle"></i> Lưu ý quan trọng</h5>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-check-circle"></i> Điều kiện để xóa tài xế:</h6>
                                <ul class="mb-0">
                                    <li>Tài xế không được phân công trong bất kỳ chuyến xe nào</li>
                                    <li>Không có dữ liệu liên quan đến đánh giá hoặc phản hồi</li>
                                    <li>Trạng thái tài xế phải là "Tạm ngưng" hoặc "Nghỉ phép"</li>
                                </ul>
                            </div>

                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle"></i> Hậu quả khi xóa:</h6>
                                <ul class="mb-0">
                                    <li>Tất cả thông tin cá nhân của tài xế sẽ bị xóa vĩnh viễn</li>
                                    <li>Không thể khôi phục dữ liệu sau khi xóa</li>
                                    <li>Lịch sử làm việc sẽ bị mất</li>
                                </ul>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.GhiChu))
                            {
                                <div class="alert alert-secondary">
                                    <h6><i class="fas fa-sticky-note"></i> Ghi chú:</h6>
                                    <p class="mb-0">@Model.GhiChu</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <form asp-action="Delete" method="post" class="d-inline">
                        <input type="hidden" asp-for="TaiXeId" />
                        
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Bạn có chắc chắn muốn xóa tài xế @Model.HoTen không? Hành động này không thể hoàn tác!')">
                                    <i class="fas fa-trash"></i> Xác nhận xóa
                                </button>
                                <a asp-action="Index" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Hủy bỏ
                                </a>
                                <a asp-action="Details" asp-route-id="@Model.TaiXeId" class="btn btn-info ml-2">
                                    <i class="fas fa-eye"></i> Xem chi tiết
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i>
                                    Tạo hồ sơ: @Model.NgayTao.ToString("dd/MM/yyyy")
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Thêm hiệu ứng cảnh báo
            $('.btn-danger').hover(
                function() {
                    $(this).addClass('shadow-lg');
                },
                function() {
                    $(this).removeClass('shadow-lg');
                }
            );
        });
    </script>
}
