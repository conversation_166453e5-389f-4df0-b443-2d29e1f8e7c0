@{
    ViewData["Title"] = "Test QR Code";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Test QR Code Scanner</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>QR Code Sample</h5>
                            <div class="text-center mb-3">
                                <img src="@Url.Action("QRCode", new { id = 1 })" 
                                     alt="Sample QR Code" class="img-fluid" style="max-width: 200px;" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>QR Code Scanner</h5>
                            <div class="mb-3">
                                <label for="qrInput" class="form-label">Paste QR Code Data:</label>
                                <textarea class="form-control" id="qrInput" rows="8"
                                          placeholder="Paste the QR code data here..."></textarea>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="parseQRData()">
                                    Parse QR Data
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="generateSampleQR()">
                                    Generate Sample QR
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h5>Parsed Data:</h5>
                        <div id="parsedData" class="alert alert-info" style="display: none;">
                            <pre id="dataContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function parseQRData() {
    const qrData = document.getElementById('qrInput').value.trim();
    if (!qrData) {
        alert('Please enter QR code data');
        return;
    }

    try {
        // Try to parse as JSON first
        if (qrData.trim().startsWith('{')) {
            const parsed = JSON.parse(qrData);
            document.getElementById('dataContent').textContent = JSON.stringify(parsed, null, 2);
        } else {
            // Display as formatted text
            document.getElementById('dataContent').textContent = qrData;
        }
        document.getElementById('parsedData').style.display = 'block';
    } catch (e) {
        // If not JSON, display as plain text
        document.getElementById('dataContent').textContent = qrData;
        document.getElementById('parsedData').style.display = 'block';
    }
}

function generateSampleQR() {
    const sampleQR = `🎫 VÉ XE KHÁCH
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Mã vé: VE001234
👤 Hành khách: Nguyễn Văn A
📞 Điện thoại: 0123456789
🚌 Tuyến: TP.HCM → Hà Nội
📅 Ngày đi: 15/01/2024 08:00
💺 Ghế số: A12
💰 Giá vé: 115,000 VNĐ
📊 Trạng thái: Đã thanh toán
📆 Ngày đặt: 14/01/2024 10:30
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Vui lòng xuất trình vé này khi lên xe`;

    document.getElementById('qrInput').value = sampleQR;
    parseQRData();
}
</script>
