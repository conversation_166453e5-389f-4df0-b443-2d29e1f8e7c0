@model DatVeXe.Models.SeatSelectionViewModel
@{
    ViewData["Title"] = "Chọn ghế ngồi";
}

<div class="container py-4">
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="progress-steps">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Chọn chuyến</div>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <div class="step-label">Chọn ghế</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">Thông tin</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Thanh toán</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Trip Info -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Thông tin chuyến xe
                    </h5>
                </div>
                <div class="card-body">
                    <div class="trip-info">
                        <div class="route mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-geo-alt text-success me-2"></i>
                                <span class="fw-semibold">@Model.ChuyenXe.DiemDiDisplay</span>
                            </div>
                            <div class="text-center my-2">
                                <i class="bi bi-arrow-down text-muted"></i>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-geo-alt-fill text-danger me-2"></i>
                                <span class="fw-semibold">@Model.ChuyenXe.DiemDenDisplay</span>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="trip-details">
                            <div class="detail-item">
                                <i class="bi bi-calendar-event text-info me-2"></i>
                                <span>@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</span>
                            </div>
                            <div class="detail-item">
                                <i class="bi bi-truck text-warning me-2"></i>
                                <span>@Model.Xe.LoaiXe - @Model.Xe.BienSo</span>
                            </div>
                            <div class="detail-item">
                                <i class="bi bi-currency-dollar text-success me-2"></i>
                                <span class="fw-bold">@string.Format("{0:N0}", Model.ChuyenXe.Gia) VNĐ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Seat Info -->
            <div class="card shadow-sm border-0 mt-3" id="selectedSeatCard" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-check-circle me-2"></i>Ghế đã chọn
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="selected-seat-display">
                            <i class="bi bi-square-fill text-success fs-1"></i>
                            <div class="mt-2">
                                <div class="fw-bold fs-5" id="selectedSeatNumber">-</div>
                                <small class="text-muted">Ghế số</small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary mt-3" id="continueBtn" disabled>
                            <i class="bi bi-arrow-right me-2"></i>Tiếp tục
                        </button>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="card shadow-sm border-0 mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Chú thích
                    </h6>
                </div>
                <div class="card-body">
                    <div class="legend">
                        <div class="legend-item">
                            <span class="seat-legend available"></span>
                            <span>Ghế trống</span>
                        </div>
                        <div class="legend-item">
                            <span class="seat-legend selected"></span>
                            <span>Ghế đang chọn</span>
                        </div>
                        <div class="legend-item">
                            <span class="seat-legend occupied"></span>
                            <span>Đã có khách</span>
                        </div>
                        <div class="legend-item">
                            <span class="seat-legend reserved"></span>
                            <span>Đang được giữ</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seat Map -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-grid-3x3-gap me-2"></i>Sơ đồ ghế - @Model.Xe.LoaiXe
                    </h5>
                </div>
                <div class="card-body">
                    <div class="bus-layout">
                        <!-- Driver area -->
                        <div class="driver-area mb-4">
                            <div class="driver-seat">
                                <i class="bi bi-person-fill"></i>
                                <span>Tài xế</span>
                            </div>
                        </div>

                        <!-- Seat grid -->
                        <div class="seat-grid" id="seatGrid">
                            @for (int hang = 1; hang <= Model.SoHang; hang++)
                            {
                                <div class="seat-row">
                                    @for (int cot = 1; cot <= Model.SoCot; cot++)
                                    {
                                        var ghe = Model.DanhSachGhe.FirstOrDefault(g => g.Hang == hang && g.Cot == cot);
                                        if (ghe != null)
                                        {
                                            var cssClass = "seat-btn";
                                            var disabled = "";
                                            var title = $"Ghế {ghe.SoGhe}";
                                            
                                            if (ghe.DaDat)
                                            {
                                                cssClass += " occupied";
                                                disabled = "disabled";
                                                title = $"Ghế {ghe.SoGhe} - Đã có khách: {ghe.TenKhachDat}";
                                            }
                                            else if (ghe.DangGiu)
                                            {
                                                cssClass += " reserved";
                                                disabled = "disabled";
                                                title = $"Ghế {ghe.SoGhe} - Đang được giữ";
                                            }
                                            else
                                            {
                                                cssClass += " available";
                                            }

                                            <button type="button" 
                                                    class="@cssClass" 
                                                    data-seat-id="@ghe.ChoNgoiId"
                                                    data-seat-number="@ghe.SoGhe"
                                                    title="@title"
                                                    @disabled>
                                                @ghe.SoGhe
                                            </button>
                                        }
                                        else
                                        {
                                            <div class="seat-empty"></div>
                                        }
                                        
                                        <!-- Add aisle after specific columns based on bus type -->
                                        @if (Model.LoaiXe == "Limousine" && cot == 1)
                                        {
                                            <div class="aisle"></div>
                                        }
                                        else if ((Model.LoaiXe == "Ghế ngồi" || Model.LoaiXe == "VIP") && cot == 2)
                                        {
                                            <div class="aisle"></div>
                                        }
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            let selectedSeatId = null;
            let selectedSeatNumber = null;
            const sessionId = '@Model.SessionId';

            // Seat selection
            $('.seat-btn.available').click(function() {
                const seatId = $(this).data('seat-id');
                const seatNumber = $(this).data('seat-number');
                
                // Remove previous selection
                $('.seat-btn').removeClass('selected');
                
                // Add selection to current seat
                $(this).addClass('selected');
                
                selectedSeatId = seatId;
                selectedSeatNumber = seatNumber;
                
                // Update UI
                $('#selectedSeatNumber').text(seatNumber);
                $('#selectedSeatCard').show();
                $('#continueBtn').prop('disabled', false);
            });

            // Continue button
            $('#continueBtn').click(function() {
                if (selectedSeatId) {
                    // Show loading
                    $(this).prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>Đang xử lý...');
                    
                    // Send seat selection to server
                    $.post('@Url.Action("SelectSeat")', {
                        sessionId: sessionId,
                        seatId: selectedSeatId
                    })
                    .done(function(response) {
                        if (response.success) {
                            window.location.href = response.nextUrl;
                        } else {
                            alert(response.message);
                            $('#continueBtn').prop('disabled', false).html('<i class="bi bi-arrow-right me-2"></i>Tiếp tục');
                        }
                    })
                    .fail(function() {
                        alert('Có lỗi xảy ra. Vui lòng thử lại.');
                        $('#continueBtn').prop('disabled', false).html('<i class="bi bi-arrow-right me-2"></i>Tiếp tục');
                    });
                }
            });

            // Auto-refresh seat status every 30 seconds
            setInterval(function() {
                // You can implement real-time seat status updates here
            }, 30000);
        });
    </script>
}

<style>
    .progress-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 2rem;
        position: relative;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 100%;
        width: 4rem;
        height: 2px;
        background: #dee2e6;
        z-index: -1;
    }

    .step.completed::after,
    .step.active::after {
        background: #28a745;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .step.completed .step-number {
        background: #28a745;
        color: white;
    }

    .step.active .step-number {
        background: #007bff;
        color: white;
    }

    .step-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .step.completed .step-label,
    .step.active .step-label {
        color: #495057;
        font-weight: 600;
    }

    .trip-info .detail-item {
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
    }

    .bus-layout {
        max-width: 600px;
        margin: 0 auto;
    }

    .driver-area {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .driver-seat {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #495057;
    }

    .seat-row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 0.5rem;
        gap: 0.25rem;
    }

    .seat-btn {
        width: 45px;
        height: 45px;
        border: 2px solid;
        border-radius: 8px;
        background: white;
        font-weight: bold;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .seat-btn.available {
        border-color: #28a745;
        color: #28a745;
    }

    .seat-btn.available:hover {
        background: #28a745;
        color: white;
        transform: scale(1.1);
    }

    .seat-btn.selected {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .seat-btn.occupied {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
        cursor: not-allowed;
    }

    .seat-btn.reserved {
        background: #ffc107;
        border-color: #ffc107;
        color: #212529;
        cursor: not-allowed;
    }

    .seat-empty {
        width: 45px;
        height: 45px;
    }

    .aisle {
        width: 20px;
        height: 45px;
    }

    .legend {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .seat-legend {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 2px solid;
    }

    .seat-legend.available {
        background: white;
        border-color: #28a745;
    }

    .seat-legend.selected {
        background: #007bff;
        border-color: #007bff;
    }

    .seat-legend.occupied {
        background: #dc3545;
        border-color: #dc3545;
    }

    .seat-legend.reserved {
        background: #ffc107;
        border-color: #ffc107;
    }

    .selected-seat-display {
        padding: 1rem;
    }
</style>
