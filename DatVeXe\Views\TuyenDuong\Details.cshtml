@model DatVeXe.Models.TuyenDuongDetailViewModel
@{
    ViewData["Title"] = $"Chi tiết tuyến đường {Model.TenTuyenDuong}";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold text-primary mb-0">
            <i class="bi bi-geo-alt-fill me-2"></i>@Model.TenTuyenDuong
        </h2>
        <div class="d-flex gap-2">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
            <a href="@Url.Action("Create", "ChuyenXe", new { diemDi = Model.DiemDi, diemDen = Model.DiemDen })" class="btn btn-success">
                <i class="bi bi-plus-circle me-1"></i>Thê<PERSON> chuyến xe
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card border-primary text-center">
                <div class="card-body">
                    <i class="bi bi-bus-front text-primary" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.SoChuyenXe</h4>
                    <p class="text-muted">Tổng chuyến xe</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-success text-center">
                <div class="card-body">
                    <i class="bi bi-ticket-perforated text-success" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.TongVeDaBan</h4>
                    <p class="text-muted">Vé đã bán</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-warning text-center">
                <div class="card-body">
                    <i class="bi bi-currency-dollar text-warning" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.DoanhThu.ToString("N0")</h4>
                    <p class="text-muted">Doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-info text-center">
                <div class="card-body">
                    <i class="bi bi-calendar-check text-info" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.ChuyenXeSapToi</h4>
                    <p class="text-muted">Sắp khởi hành</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-secondary text-center">
                <div class="card-body">
                    <i class="bi bi-check-circle text-secondary" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.ChuyenXeDaHoanThanh</h4>
                    <p class="text-muted">Đã hoàn thành</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-dark text-center">
                <div class="card-body">
                    <i class="bi bi-cash text-dark" style="font-size: 2rem;"></i>
                    <h4 class="mt-2">@Model.GiaThapNhat.ToString("N0") - @Model.GiaCaoNhat.ToString("N0")</h4>
                    <p class="text-muted">Khoảng giá (VNĐ)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Trips Table -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="bi bi-list me-2"></i>Danh sách chuyến xe
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Ngày khởi hành</th>
                            <th>Xe</th>
                            <th>Giá vé</th>
                            <th>Vé đã bán</th>
                            <th>Doanh thu</th>
                            <th>Tỷ lệ lấp đầy</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var chuyenXe in Model.ChuyenXes)
                        {
                            var tyLeLapDay = chuyenXe.Xe.SoGhe > 0 ? (double)chuyenXe.Ves.Count / chuyenXe.Xe.SoGhe * 100 : 0;
                            var doanhThu = chuyenXe.Ves.Sum(v => v.GiaVe);
                            
                            <tr>
                                <td>
                                    <div>
                                        <strong>@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</strong>
                                        <br>
                                        <small class="text-muted">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>@chuyenXe.Xe.BienSo</strong>
                                        <br>
                                        <small class="text-muted">@chuyenXe.Xe.LoaiXe (@chuyenXe.Xe.SoGhe chỗ)</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-success fw-bold">@chuyenXe.Gia.ToString("N0") VNĐ</span>
                                </td>
                                <td>
                                    <span class="badge bg-info fs-6">@chuyenXe.Ves.Count/@chuyenXe.Xe.SoGhe</span>
                                </td>
                                <td>
                                    <span class="text-success fw-bold">@doanhThu.ToString("N0") VNĐ</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar @(tyLeLapDay >= 80 ? "bg-success" : tyLeLapDay >= 50 ? "bg-warning" : "bg-danger")" 
                                             role="progressbar" 
                                             style="width: @tyLeLapDay.ToString("F1")%">
                                            @tyLeLapDay.ToString("F1")%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if (chuyenXe.NgayKhoiHanh > DateTime.Now)
                                    {
                                        <span class="badge bg-primary">
                                            <i class="bi bi-clock me-1"></i>Sắp khởi hành
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Đã hoàn thành
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="@Url.Action("Details", "ChuyenXe", new { id = chuyenXe.ChuyenXeId })" 
                                           class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="@Url.Action("Index", "Ve", new { chuyenXeId = chuyenXe.ChuyenXeId })" 
                                           class="btn btn-sm btn-outline-info" title="Quản lý vé">
                                            <i class="bi bi-ticket-perforated"></i>
                                        </a>
                                        @if (chuyenXe.NgayKhoiHanh > DateTime.Now)
                                        {
                                            <a href="@Url.Action("Edit", "ChuyenXe", new { id = chuyenXe.ChuyenXeId })" 
                                               class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>Doanh thu theo thời gian
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-pie-chart me-2"></i>Tỷ lệ lấp đầy
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="occupancyChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueData = @Html.Raw(Json.Serialize(Model.ChuyenXes.Select(c => new { 
    date = c.NgayKhoiHanh.ToString("dd/MM"), 
    revenue = c.Ves.Sum(v => v.GiaVe) 
}).OrderBy(x => x.date)));

new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: revenueData.map(d => d.date),
        datasets: [{
            label: 'Doanh thu (VNĐ)',
            data: revenueData.map(d => d.revenue),
            borderColor: '#6CABDD',
            backgroundColor: 'rgba(108, 171, 221, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' VNĐ';
                    }
                }
            }
        }
    }
});

// Occupancy Chart
const occupancyCtx = document.getElementById('occupancyChart').getContext('2d');
const occupancyData = @Html.Raw(Json.Serialize(Model.ChuyenXes.Select(c => new { 
    trip = c.NgayKhoiHanh.ToString("dd/MM HH:mm"), 
    occupancy = c.Xe.SoGhe > 0 ? (double)c.Ves.Count / c.Xe.SoGhe * 100 : 0 
})));

new Chart(occupancyCtx, {
    type: 'bar',
    data: {
        labels: occupancyData.map(d => d.trip),
        datasets: [{
            label: 'Tỷ lệ lấp đầy (%)',
            data: occupancyData.map(d => d.occupancy),
            backgroundColor: occupancyData.map(d => 
                d.occupancy >= 80 ? '#28a745' : 
                d.occupancy >= 50 ? '#ffc107' : '#dc3545'
            )
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});
</script>

<style>
.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.progress {
    border-radius: 10px;
}

.btn-group .btn {
    margin-right: 2px;
}
</style>
