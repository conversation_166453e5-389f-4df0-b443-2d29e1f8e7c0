@model DatVeXe.Models.Xe
@{
    ViewData["Title"] = "Xóa xe";
}
@if (TempData["ThongBao"] != null)
{
    <div class="alert alert-info">@TempData["ThongBao"]</div>
}
<h2>X<PERSON>a xe</h2>
<h3>Bạn có chắc chắn muốn xóa xe này?</h3>
<div>
    <dl class="row">
        <dt class="col-sm-2">Biển số xe</dt>
        <dd class="col-sm-10">@Model.BienSoXe</dd>
        <dt class="col-sm-2">Loại xe</dt>
        <dd class="col-sm-10">@Model.LoaiXe</dd>
        <dt class="col-sm-2">Số ghế</dt>
        <dd class="col-sm-10">@Model.SoGhe</dd>
        <dt class="col-sm-2"><PERSON><PERSON> <PERSON><PERSON></dt>
        <dd class="col-sm-10">@Model.MoTa</dd>
    </dl>
    <form asp-action="Delete">
        <input type="hidden" asp-for="XeId" />
        <button type="submit" class="btn btn-danger">Xóa</button>
        <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
    </form>
</div>
