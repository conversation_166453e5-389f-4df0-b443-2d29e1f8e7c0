using DatVeXe.Models;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

namespace DatVeXe.Services
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string htmlBody, string? plainTextBody = null)
        {
            try
            {
                // Demo mode - Log email content instead of sending
                _logger.LogInformation($"[DEMO MODE] Email would be sent to: {toEmail}");
                _logger.LogInformation($"[DEMO MODE] Subject: {subject}");
                _logger.LogInformation($"[DEMO MODE] Content: {plainTextBody ?? htmlBody}");

                // Simulate email sending delay
                await Task.Delay(100);

                _logger.LogInformation($"[DEMO MODE] Email sent successfully to {toEmail}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email to {toEmail}");
                return false;
            }
        }

        public async Task<bool> SendTicketConfirmationAsync(string toEmail, string customerName, string ticketCode,
            string tripInfo, string seatInfo, decimal price, DateTime departureTime)
        {
            var subject = $"Xác nhận đặt vé thành công - Mã vé: {ticketCode}";
            var htmlBody = GenerateTicketConfirmationHtml(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);
            var plainTextBody = GenerateTicketConfirmationText(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);

            return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
        }

        public async Task<bool> SendTicketConfirmationWithQRAsync(string toEmail, string customerName, string ticketCode,
            string tripInfo, string seatInfo, decimal price, DateTime departureTime, string qrCodeData)
        {
            var subject = $"Xác nhận đặt vé thành công - Mã vé: {ticketCode}";
            var htmlBody = GenerateTicketConfirmationWithQRHtml(customerName, ticketCode, tripInfo, seatInfo, price, departureTime, qrCodeData);
            var plainTextBody = GenerateTicketConfirmationText(customerName, ticketCode, tripInfo, seatInfo, price, departureTime);

            return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
        }

        public async Task<bool> SendTicketCancellationAsync(string toEmail, string customerName, string ticketCode, 
            string tripInfo, string reason)
        {
            var subject = $"Thông báo hủy vé - Mã vé: {ticketCode}";
            var htmlBody = GenerateTicketCancellationHtml(customerName, ticketCode, tripInfo, reason);
            var plainTextBody = GenerateTicketCancellationText(customerName, ticketCode, tripInfo, reason);

            return await SendEmailAsync(toEmail, subject, htmlBody, plainTextBody);
        }

        private string GenerateTicketConfirmationHtml(string customerName, string ticketCode, string tripInfo,
            string seatInfo, decimal price, DateTime departureTime)
        {
            return GenerateTicketConfirmationWithQRHtml(customerName, ticketCode, tripInfo, seatInfo, price, departureTime, null);
        }

        private string GenerateTicketConfirmationWithQRHtml(string customerName, string ticketCode, string tripInfo,
            string seatInfo, decimal price, DateTime departureTime, string? qrCodeData)
        {
            var qrSection = !string.IsNullOrEmpty(qrCodeData) ? $@"
            <div class='qr-section'>
                <h3 style='color: #667eea; margin-top: 0; text-align: center;'>📱 Mã QR vé của bạn</h3>
                <div class='qr-container'>
                    <div id='qrcode'></div>
                    <p class='qr-instruction'>Quét mã QR này khi lên xe hoặc sử dụng mã vé <strong>{ticketCode}</strong></p>
                </div>
            </div>
            <script src='https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'></script>
            <script>
                QRCode.toCanvas(document.getElementById('qrcode'), '{qrCodeData}', {{
                    width: 200,
                    height: 200,
                    color: {{
                        dark: '#000000',
                        light: '#FFFFFF'
                    }}
                }}, function (error) {{
                    if (error) console.error(error);
                }});
            </script>" : "";

            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Xác nhận đặt vé</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }}
        .content {{ padding: 30px; }}
        .ticket-info {{ background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 5px solid #667eea; }}
        .info-row {{ display: flex; justify-content: space-between; align-items: center; margin: 15px 0; padding: 12px 0; border-bottom: 1px solid #dee2e6; }}
        .info-row:last-child {{ border-bottom: none; }}
        .label {{ font-weight: 600; color: #495057; font-size: 14px; }}
        .value {{ color: #212529; font-weight: 500; text-align: right; }}
        .price {{ font-size: 28px; font-weight: bold; color: #28a745; text-align: center; margin: 25px 0; padding: 20px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-radius: 10px; }}
        .footer {{ text-align: center; margin-top: 40px; color: #6c757d; font-size: 14px; padding: 20px; background-color: #f8f9fa; }}
        .logo {{ font-size: 32px; font-weight: bold; margin-bottom: 15px; }}
        .qr-note {{ background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 20px; border-radius: 10px; margin: 25px 0; text-align: center; border-left: 5px solid #2196f3; }}
        .qr-section {{ background: white; padding: 25px; border-radius: 12px; margin: 25px 0; text-align: center; border: 2px dashed #667eea; }}
        .qr-container {{ display: flex; flex-direction: column; align-items: center; }}
        .qr-instruction {{ margin-top: 15px; color: #495057; font-size: 14px; }}
        .contact-info {{ background: #fff3cd; padding: 20px; border-radius: 10px; margin: 25px 0; border-left: 5px solid #ffc107; }}
        .contact-info ul {{ list-style: none; padding: 0; margin: 10px 0; }}
        .contact-info li {{ margin: 8px 0; font-size: 14px; }}
        .highlight {{ background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #f39c12; }}
        .success-badge {{ display: inline-block; background: #28a745; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>🚌 DatVeXe.com</div>
            <div class='success-badge'>✅ Đặt vé thành công</div>
            <h2 style='margin: 15px 0 0 0; font-size: 24px;'>Xác nhận đặt vé thành công!</h2>
        </div>

        <div class='content'>
            <p style='font-size: 16px; margin-bottom: 20px;'>Xin chào <strong style='color: #667eea;'>{customerName}</strong>,</p>
            <p style='font-size: 16px; margin-bottom: 25px;'>Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi. Vé của bạn đã được đặt thành công và đang chờ thanh toán!</p>

            <div class='ticket-info'>
                <h3 style='color: #667eea; margin-top: 0; font-size: 20px; text-align: center;'>🎫 Thông tin chi tiết vé</h3>
                <div class='info-row'>
                    <span class='label'>📋 Mã vé:</span>
                    <span class='value' style='font-family: monospace; font-size: 16px; font-weight: bold; color: #667eea;'>{ticketCode}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>🛣️ Tuyến đường:</span>
                    <span class='value' style='font-weight: bold;'>{tripInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>💺 Chỗ ngồi:</span>
                    <span class='value' style='font-weight: bold; color: #28a745;'>{seatInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>🕐 Thời gian khởi hành:</span>
                    <span class='value' style='font-weight: bold; color: #dc3545;'>{departureTime:dddd, dd/MM/yyyy HH:mm}</span>
                </div>
                <div class='price'>
                    💰 Tổng tiền: {price:N0} VNĐ
                </div>
            </div>

            {qrSection}

            <div class='highlight'>
                <p style='margin: 0; font-weight: 600;'><strong>⚠️ Lưu ý quan trọng:</strong></p>
                <ul style='margin: 10px 0 0 20px; padding: 0;'>
                    <li>✅ Mang theo email này hoặc mã vé <strong>{ticketCode}</strong> khi lên xe</li>
                    <li>⏰ Có mặt tại bến xe trước giờ khởi hành ít nhất <strong>15 phút</strong></li>
                    <li>🆔 Mang theo giấy tờ tùy thân để đối chiếu thông tin</li>
                    <li>📱 Có thể sử dụng mã QR để check-in nhanh chóng</li>
                </ul>
            </div>

            <div class='contact-info'>
                <p style='margin-top: 0; font-weight: 600;'>📞 Cần hỗ trợ? Liên hệ với chúng tôi:</p>
                <ul>
                    <li>📞 <strong>Hotline 24/7:</strong> 1900-xxxx</li>
                    <li>📧 <strong>Email:</strong> <EMAIL></li>
                    <li>🌐 <strong>Website:</strong> www.datvexe.com</li>
                    <li>💬 <strong>Chat trực tuyến:</strong> Có sẵn trên website</li>
                </ul>
            </div>
        </div>

        <div class='footer'>
            <p style='margin: 0 0 10px 0; font-weight: 600;'>Cảm ơn bạn đã tin tưởng DatVeXe.com!</p>
            <p style='margin: 0; font-style: italic;'>Email này được gửi tự động, vui lòng không trả lời trực tiếp.</p>
            <p style='margin: 10px 0 0 0; font-size: 12px;'>© 2024 DatVeXe.com - Hệ thống đặt vé xe hàng đầu Việt Nam</p>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateTicketConfirmationText(string customerName, string ticketCode, string tripInfo, 
            string seatInfo, decimal price, DateTime departureTime)
        {
            return $@"
XÁC NHẬN ĐẶT VÉ THÀNH CÔNG

Xin chào {customerName},

Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi. Vé của bạn đã được đặt thành công!

THÔNG TIN VÉ:
- Mã vé: {ticketCode}
- Tuyến đường: {tripInfo}
- Chỗ ngồi: {seatInfo}
- Thời gian khởi hành: {departureTime:dd/MM/yyyy HH:mm}
- Tổng tiền: {price:N0} VNĐ

LƯU Ý QUAN TRỌNG:
- Vui lòng mang theo email này hoặc mã vé {ticketCode} khi lên xe
- Có mặt tại bến xe trước giờ khởi hành ít nhất 15 phút

LIÊN HỆ HỖ TRỢ:
- Hotline: 1900-xxxx
- Email: <EMAIL>
- Website: www.datvexe.com

Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!

---
Email này được gửi tự động, vui lòng không trả lời.
";
        }

        private string GenerateTicketCancellationHtml(string customerName, string ticketCode, string tripInfo, string reason)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Thông báo hủy vé</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
        .ticket-info {{ background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .info-row {{ display: flex; justify-content: space-between; margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #eee; }}
        .label {{ font-weight: bold; color: #555; }}
        .value {{ color: #333; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
        .logo {{ font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='logo'>🚌 Hệ thống đặt vé xe</div>
            <h2>Thông báo hủy vé</h2>
        </div>
        
        <div class='content'>
            <p>Xin chào <strong>{customerName}</strong>,</p>
            <p>Chúng tôi xin thông báo vé của bạn đã được hủy.</p>
            
            <div class='ticket-info'>
                <h3 style='color: #e74c3c; margin-top: 0;'>🎫 Thông tin vé đã hủy</h3>
                <div class='info-row'>
                    <span class='label'>Mã vé:</span>
                    <span class='value'><strong>{ticketCode}</strong></span>
                </div>
                <div class='info-row'>
                    <span class='label'>Tuyến đường:</span>
                    <span class='value'>{tripInfo}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Lý do hủy:</span>
                    <span class='value'>{reason}</span>
                </div>
                <div class='info-row'>
                    <span class='label'>Thời gian hủy:</span>
                    <span class='value'>{DateTime.Now:dd/MM/yyyy HH:mm}</span>
                </div>
            </div>
            
            <div class='warning'>
                <p><strong>⚠️ Lưu ý:</strong></p>
                <p>Nếu bạn đã thanh toán, chúng tôi sẽ tiến hành hoàn tiền theo chính sách của công ty.</p>
                <p>Thời gian hoàn tiền: 3-7 ngày làm việc.</p>
            </div>
            
            <p>Nếu bạn có bất kỳ thắc mắc nào, vui lòng liên hệ với chúng tôi qua:</p>
            <ul>
                <li>📞 Hotline: 1900-xxxx</li>
                <li>📧 Email: <EMAIL></li>
                <li>🌐 Website: www.datvexe.com</li>
            </ul>
        </div>
        
        <div class='footer'>
            <p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>
            <p><em>Email này được gửi tự động, vui lòng không trả lời.</em></p>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateTicketCancellationText(string customerName, string ticketCode, string tripInfo, string reason)
        {
            return $@"
THÔNG BÁO HỦY VÉ

Xin chào {customerName},

Chúng tôi xin thông báo vé của bạn đã được hủy.

THÔNG TIN VÉ ĐÃ HỦY:
- Mã vé: {ticketCode}
- Tuyến đường: {tripInfo}
- Lý do hủy: {reason}
- Thời gian hủy: {DateTime.Now:dd/MM/yyyy HH:mm}

LƯU Ý:
- Nếu bạn đã thanh toán, chúng tôi sẽ tiến hành hoàn tiền theo chính sách của công ty
- Thời gian hoàn tiền: 3-7 ngày làm việc

LIÊN HỆ HỖ TRỢ:
- Hotline: 1900-xxxx
- Email: <EMAIL>
- Website: www.datvexe.com

Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!

---
Email này được gửi tự động, vui lòng không trả lời.
";
        }
    }
}
