@model DatVeXe.Models.Ve

@{
    ViewData["Title"] = "Đặt vé thành công";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h3><i class="bi bi-check-circle-fill me-2"></i>Đặt vé thành công!</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success" role="alert">
                        <h5 class="alert-heading">Chúc mừng!</h5>
                        <p>Vé của bạn đã được đặt thành công. Vui lòng lưu lại thông tin vé để sử dụng khi lên xe.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary">Thông tin vé</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mã vé:</strong></td>
                                    <td class="text-primary fw-bold">@Model.MaVe</td>
                                </tr>
                                <tr>
                                    <td><strong>Tên khách hàng:</strong></td>
                                    <td>@Model.TenKhach</td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td>@Model.SoDienThoai</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.Email))
                                {
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>@Model.Email</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Giá vé:</strong></td>
                                    <td class="text-danger fw-bold">@Model.GiaVe.ToString("N0") VNĐ</td>
                                </tr>
                                @if (Model.ChoNgoi != null)
                                {
                                    <tr>
                                        <td><strong>Số ghế:</strong></td>
                                        <td class="text-info fw-bold">@Model.ChoNgoi.SoGhe</td>
                                    </tr>
                                }
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">Thông tin chuyến xe</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Tuyến đường:</strong></td>
                                    <td>@Model.ChuyenXe?.DiemDiDisplay → @Model.ChuyenXe?.DiemDenDisplay</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày khởi hành:</strong></td>
                                    <td class="text-warning fw-bold">@Model.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <td><strong>Biển số xe:</strong></td>
                                    <td>@Model.ChuyenXe?.Xe?.BienSo</td>
                                </tr>
                                <tr>
                                    <td><strong>Loại xe:</strong></td>
                                    <td>@Model.ChuyenXe?.Xe?.LoaiXe</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày đặt:</strong></td>
                                    <td>@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.GhiChu))
                    {
                        <div class="mt-3">
                            <h6 class="text-primary">Ghi chú:</h6>
                            <p class="text-muted">@Model.GhiChu</p>
                        </div>
                    }

                    <div class="alert alert-info mt-4">
                        <h6><i class="bi bi-info-circle me-2"></i>Lưu ý quan trọng:</h6>
                        <ul class="mb-0">
                            <li>Vui lòng có mặt tại bến xe trước 15 phút so với giờ khởi hành</li>
                            <li>Mang theo giấy tờ tùy thân khi lên xe</li>
                            <li>Liên hệ hotline nếu cần hỗ trợ: <strong>1900-xxxx</strong></li>
                            <li>Mã vé: <strong class="text-primary">@Model.MaVe</strong> - Vui lòng lưu lại để tra cứu</li>
                        </ul>
                    </div>

                    <div class="text-center mt-4">
                        <a href="@Url.Action("Search", "ChuyenXe")" class="btn btn-primary me-2">
                            <i class="bi bi-search me-2"></i>Đặt vé khác
                        </a>
                        <a href="@Url.Action("Index", "Home")" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>Về trang chủ
                        </a>
                        @if (Model.NguoiDungId.HasValue)
                        {
                            <a href="@Url.Action("Index", "MyTickets")" class="btn btn-info ms-2">
                                <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    
    .card-header {
        border-radius: 15px 15px 0 0 !important;
        padding: 1.5rem;
    }
    
    .table td {
        padding: 0.5rem 0;
        border: none;
    }
    
    .alert {
        border-radius: 10px;
    }
    
    .btn {
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
    }
</style>
