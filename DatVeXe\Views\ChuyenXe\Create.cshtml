@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Thêm chuyến xe mới";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle"></i> @ViewData["Title"]
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="createForm">
                        <div asp-validation-summary="All" class="alert alert-danger"></div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DiemDi" class="form-label required"></label>
                                    <input asp-for="DiemDi" class="form-control" placeholder="Nhập điểm đi" />
                                    <span asp-validation-for="DiemDi" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DiemDen" class="form-label required"></label>
                                    <input asp-for="DiemDen" class="form-control" placeholder="Nhập điểm đến" />
                                    <span asp-validation-for="DiemDen" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="NgayKhoiHanh" class="form-label required"></label>
                                    <input asp-for="NgayKhoiHanh" class="form-control" type="datetime-local" />
                                    <span asp-validation-for="NgayKhoiHanh" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="XeId" class="form-label required">Chọn xe</label>
                                    <select asp-for="XeId" asp-items="ViewBag.XeList" class="form-select">
                                        <option value="">-- Chọn xe --</option>
                                    </select>
                                    <span asp-validation-for="XeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="TaiXeId" class="form-label">Chọn tài xế</label>
                                    <select asp-for="TaiXeId" asp-items="ViewBag.TaiXeList" class="form-select">
                                        <option value="">-- Chọn tài xế --</option>
                                    </select>
                                    <span asp-validation-for="TaiXeId" class="text-danger"></span>
                                    <small class="form-text text-muted">Có thể để trống và gán tài xế sau</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Lưu chuyến xe
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        .validation-summary-errors {
            margin-bottom: 1rem;
        }
        .validation-summary-errors ul {
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
        }
        .field-validation-error {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }
        .input-validation-error {
            border-color: #dc3545;
        }
        .input-validation-error:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Set min date for NgayKhoiHanh to today
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            var hh = String(today.getHours()).padStart(2, '0');
            var mi = String(today.getMinutes()).padStart(2, '0');
            today = yyyy + '-' + mm + '-' + dd + 'T' + hh + ':' + mi;
            $('#NgayKhoiHanh').attr('min', today);

            // Enable jQuery validation
            var form = $("#createForm");
            form.removeData('validator');
            form.removeData('unobtrusiveValidation');
            $.validator.unobtrusive.parse(form);

            // Add custom validation for XeId
            $.validator.addMethod('requiredXe', function(value) {
                return value && value !== '';
            }, 'Vui lòng chọn xe');

            $("#XeId").rules("add", {
                requiredXe: true
            });

            // Handle form submission
            form.on('submit', function(e) {
                if (!form.valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    var firstError = $('.field-validation-error').first();
                    if (firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 200);
                    }
                    return false;
                }
                return true;
            });

            // Disable submit button on valid form submission
            form.on('submit', function() {
                if (form.valid()) {
                    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');
                }
            });
        });
    </script>
} 