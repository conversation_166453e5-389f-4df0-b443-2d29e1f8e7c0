-- Script backup database DatVeXeDb
-- T<PERSON><PERSON> ngày: 12/12/2024
-- <PERSON><PERSON> tả: Script này tạo backup đầy đủ của database DatVeXeDb

USE master;
GO

-- Tạo backup database
DECLARE @BackupPath NVARCHAR(500);
DECLARE @BackupFileName NVARCHAR(500);
DECLARE @CurrentDateTime NVARCHAR(20);

-- Tạo tên file backup với timestamp
SET @CurrentDateTime = FORMAT(GETDATE(), 'yyyyMMdd_HHmmss');
SET @BackupFileName = 'DatVeXeDb_Backup_' + @CurrentDateTime + '.bak';
SET @BackupPath = 'C:\Backup\' + @BackupFileName;

-- Tạo thư mục backup nếu chưa tồn tại
EXEC xp_cmdshell 'mkdir C:\Backup', NO_OUTPUT;

-- <PERSON>h<PERSON><PERSON> hiện backup
BACKUP DATABASE [DatVeXeDb] 
TO DISK = @BackupPath
WITH 
    FORMAT,
    INIT,
    NAME = 'DatVeXeDb Full Backup',
    DESCRIPTION = 'Full backup of DatVeXeDb database',
    COMPRESSION,
    CHECKSUM,
    STATS = 10;

PRINT 'Backup completed successfully!';
PRINT 'Backup file: ' + @BackupPath;

-- Kiểm tra backup
RESTORE VERIFYONLY FROM DISK = @BackupPath;
PRINT 'Backup verification completed successfully!';
