@model DanhBaH<PERSON>h<PERSON>hach
@{
    ViewData["Title"] = "Thêm liên hệ mới";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="bi bi-person-plus me-2"></i>Thêm liên hệ mới
                    </h2>
                    <p class="mb-0 mt-2">Thêm thông tin hành khách để tiện lợi cho lần đặt vé sau</p>
                </div>

                <div class="card-body p-4">
                    <form asp-action="Create" method="post">
                        <div class="row">
                            <!-- Thông tin cơ bản -->
                            <div class="col-12 mb-4">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-info-circle me-2"></i>Thông tin cơ bản
                                </h5>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="TenHanhKhach" class="form-label fw-semibold">
                                    <i class="bi bi-person me-2"></i>Tên hành khách *
                                </label>
                                <input asp-for="TenHanhKhach" class="form-control form-control-lg" 
                                       placeholder="Nhập tên đầy đủ" />
                                <span asp-validation-for="TenHanhKhach" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="SoDienThoai" class="form-label fw-semibold">
                                    <i class="bi bi-telephone me-2"></i>Số điện thoại *
                                </label>
                                <input asp-for="SoDienThoai" class="form-control form-control-lg" 
                                       placeholder="Nhập số điện thoại" />
                                <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope me-2"></i>Email
                                </label>
                                <input asp-for="Email" type="email" class="form-control form-control-lg" 
                                       placeholder="Nhập địa chỉ email" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="GioiTinh" class="form-label fw-semibold">
                                    <i class="bi bi-gender-ambiguous me-2"></i>Giới tính
                                </label>
                                <select asp-for="GioiTinh" class="form-select form-select-lg">
                                    <option value="">Chọn giới tính</option>
                                    <option value="Nam">Nam</option>
                                    <option value="Nữ">Nữ</option>
                                    <option value="Khác">Khác</option>
                                </select>
                                <span asp-validation-for="GioiTinh" class="text-danger"></span>
                            </div>

                            <!-- Thông tin bổ sung -->
                            <div class="col-12 mb-4 mt-3">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-card-text me-2"></i>Thông tin bổ sung
                                </h5>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="NgaySinh" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-date me-2"></i>Ngày sinh
                                </label>
                                <input asp-for="NgaySinh" type="date" class="form-control form-control-lg" 
                                       max="@DateTime.Now.ToString("yyyy-MM-dd")" />
                                <span asp-validation-for="NgaySinh" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="CCCD" class="form-label fw-semibold">
                                    <i class="bi bi-card-heading me-2"></i>CCCD/CMND
                                </label>
                                <input asp-for="CCCD" class="form-control form-control-lg" 
                                       placeholder="Nhập số CCCD/CMND" />
                                <span asp-validation-for="CCCD" class="text-danger"></span>
                            </div>

                            <div class="col-12 mb-3">
                                <label asp-for="DiaChi" class="form-label fw-semibold">
                                    <i class="bi bi-geo-alt me-2"></i>Địa chỉ
                                </label>
                                <input asp-for="DiaChi" class="form-control form-control-lg" 
                                       placeholder="Nhập địa chỉ" />
                                <span asp-validation-for="DiaChi" class="text-danger"></span>
                            </div>

                            <div class="col-12 mb-3">
                                <label asp-for="GhiChu" class="form-label fw-semibold">
                                    <i class="bi bi-chat-text me-2"></i>Ghi chú
                                </label>
                                <textarea asp-for="GhiChu" class="form-control" rows="3" 
                                          placeholder="Ghi chú thêm về hành khách (tùy chọn)"></textarea>
                                <div class="form-text">Ví dụ: Ưa thích ghế cửa sổ, cần hỗ trợ đặc biệt...</div>
                                <span asp-validation-for="GhiChu" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a asp-action="Index" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại danh sách
                            </a>
                            <div>
                                <button type="reset" class="btn btn-outline-warning btn-lg me-2">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Làm mới
                                </button>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-save me-2"></i>Lưu liên hệ
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Hướng dẫn -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0 text-info">
                        <i class="bi bi-lightbulb me-2"></i>Mẹo sử dụng danh bạ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Lưu thông tin người thân, bạn bè thường đi cùng
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Đặt vé nhanh chóng mà không cần nhập lại thông tin
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Thông tin được bảo mật và chỉ bạn mới thấy được
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-info-circle text-info me-2"></i>
                                    Chỉ cần điền tên và số điện thoại (bắt buộc)
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-info-circle text-info me-2"></i>
                                    Thông tin khác giúp đặt vé chính xác hơn
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-info-circle text-info me-2"></i>
                                    Có thể chỉnh sửa hoặc xóa bất cứ lúc nào
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Auto-focus first input
            $('#TenHanhKhach').focus();

            // Phone number formatting
            $('#SoDienThoai').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }
                $(this).val(value);
            });

            // CCCD formatting
            $('#CCCD').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length > 12) {
                    value = value.substring(0, 12);
                }
                $(this).val(value);
            });

            // Form validation feedback
            $('form').on('submit', function(e) {
                const name = $('#TenHanhKhach').val().trim();
                const phone = $('#SoDienThoai').val().trim();
                
                if (!name || !phone) {
                    e.preventDefault();
                    toastr.error('Vui lòng điền đầy đủ tên và số điện thoại!');
                    return false;
                }

                if (phone.length < 10) {
                    e.preventDefault();
                    toastr.error('Số điện thoại phải có ít nhất 10 chữ số!');
                    return false;
                }

                // Show loading
                $(this).find('button[type="submit"]').prop('disabled', true).html(
                    '<i class="bi bi-hourglass-split me-2"></i>Đang lưu...'
                );
            });

            // Character counter for notes
            $('#GhiChu').on('input', function() {
                const maxLength = 500;
                const currentLength = $(this).val().length;
                const remaining = maxLength - currentLength;
                
                if (!$('.char-counter').length) {
                    $(this).after('<div class="char-counter form-text"></div>');
                }
                
                $('.char-counter').text(`${currentLength}/${maxLength} ký tự`);
                
                if (remaining < 50) {
                    $('.char-counter').addClass('text-warning');
                } else {
                    $('.char-counter').removeClass('text-warning');
                }
            });
        });
    </script>
}

<style>
    .card {
        border-radius: 15px;
    }
    
    .form-control-lg, .form-select-lg {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control-lg:focus, .form-select-lg:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .border-bottom {
        border-bottom: 2px solid #e9ecef !important;
    }
    
    .form-label {
        margin-bottom: 0.75rem;
    }
    
    .form-label i {
        width: 20px;
    }
</style>
