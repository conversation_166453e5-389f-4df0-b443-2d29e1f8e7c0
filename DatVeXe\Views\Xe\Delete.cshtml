@model DatVeXe.Models.Xe

@{
    ViewData["Title"] = "Xóa xe";
    var soChuyenXe = Model.ChuyenXes?.Count ?? 0;
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> @ViewData["Title"]
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa xe này không? Hành động này không thể hoàn tác.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-id-card"></i> Biển số xe:</strong></td>
                                    <td><span class="badge bg-primary fs-6">@Model.BienSo</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-bus"></i> Loại xe:</strong></td>
                                    <td><span class="badge bg-info fs-6">@Model.LoaiXe</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-chair"></i> Số ghế:</strong></td>
                                    <td><span class="badge bg-secondary fs-6">@Model.SoGhe chỗ</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-route"></i> Số chuyến:</strong></td>
                                    <td><span class="badge bg-success fs-6">@soChuyenXe chuyến</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (soChuyenXe > 0)
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-ban"></i>
                            <strong>Không thể xóa!</strong> Xe này đã có @soChuyenXe chuyến xe. Vui lòng xóa tất cả chuyến xe trước khi xóa xe.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a asp-action="Details" asp-route-id="@Model.XeId" class="btn btn-info">
                                <i class="fas fa-info-circle"></i> Xem chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    }
                    else
                    {
                        <form asp-action="Delete" method="post" id="deleteForm">
                            <input type="hidden" asp-for="XeId" />
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-danger" id="deleteBtn">
                                    <i class="fas fa-trash"></i> Xác nhận xóa
                                </button>
                                <a asp-action="Details" asp-route-id="@Model.XeId" class="btn btn-info">
                                    <i class="fas fa-info-circle"></i> Xem chi tiết
                                </a>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Hủy bỏ
                                </a>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#deleteForm').on('submit', function(e) {
                e.preventDefault();
                
                // Show confirmation dialog
                if (confirm('Bạn có chắc chắn muốn xóa xe "' + '@Model.BienSo' + '" không?\n\nHành động này không thể hoàn tác!')) {
                    // Disable button and show loading
                    $('#deleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xóa...');
                    
                    // Submit form
                    this.submit();
                }
            });
        });
    </script>
}
