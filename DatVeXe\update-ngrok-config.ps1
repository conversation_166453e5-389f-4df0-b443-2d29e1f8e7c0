# Script để cập nhật cấu hình ngrok
param(
    [Parameter(Mandatory=$true)]
    [string]$NgrokUrl
)

Write-Host "Updating configuration with ngrok URL: $NgrokUrl"

# Đọc template
$templatePath = "appsettings.Development.template.json"
$configPath = "appsettings.Development.json"

if (Test-Path $templatePath) {
    $content = Get-Content $templatePath -Raw
    $content = $content -replace "REPLACE_WITH_YOUR_NGROK_URL", $NgrokUrl
    $content | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "Configuration updated successfully!"
    Write-Host "File: $configPath"
    Write-Host "Ngrok URL: $NgrokUrl"
} else {
    Write-Host "Template file not found: $templatePath"
}

Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Restart the application (Ctrl+C then 'dotnet run')"
Write-Host "2. Test payment again"
Write-Host "3. VNPay will callback to: $NgrokUrl/Booking/PaymentCallback"
