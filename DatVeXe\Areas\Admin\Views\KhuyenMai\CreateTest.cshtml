@model DatVeXe.Models.KhuyenMai

@{
    ViewData["Title"] = "Test Create Page";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <title>Test Create Khuyến <PERSON>i</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> Test Page Hoạt Động!</h4>
                    <p>Nếu bạn thấy trang này, nghĩa là controller và routing đã hoạt động.</p>
                </div>
                
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-plus-circle me-2"></i>Form Tạo Khuyến Mãi Test</h5>
                    </div>
                    <div class="card-body">
                        <form asp-action="Create" method="post">
                            @Html.AntiForgeryToken()
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="TenKhuyenMai" class="form-label">Tên khuyến mãi *</label>
                                        <input asp-for="TenKhuyenMai" class="form-control" placeholder="Nhập tên khuyến mãi" required />
                                        <span asp-validation-for="TenKhuyenMai" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="MaKhuyenMai" class="form-label">Mã khuyến mãi *</label>
                                        <input asp-for="MaKhuyenMai" class="form-control" placeholder="Nhập mã khuyến mãi" required />
                                        <span asp-validation-for="MaKhuyenMai" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label asp-for="MoTa" class="form-label">Mô tả</label>
                                <textarea asp-for="MoTa" class="form-control" rows="3" placeholder="Mô tả khuyến mãi"></textarea>
                                <span asp-validation-for="MoTa" class="text-danger"></span>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="LoaiKhuyenMai" class="form-label">Loại khuyến mãi *</label>
                                        <select asp-for="LoaiKhuyenMai" class="form-select" required>
                                            <option value="">-- Chọn loại --</option>
                                            @if (ViewBag.LoaiKhuyenMaiList != null)
                                            {
                                                foreach (LoaiKhuyenMai item in ViewBag.LoaiKhuyenMaiList)
                                                {
                                                    <option value="@((int)item)">
                                                        @(item == LoaiKhuyenMai.GiamPhanTram ? "Giảm theo phần trăm" :
                                                          item == LoaiKhuyenMai.GiamSoTien ? "Giảm theo số tiền" : "Miễn phí")
                                                    </option>
                                                }
                                            }
                                        </select>
                                        <span asp-validation-for="LoaiKhuyenMai" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="GiaTri" class="form-label">Giá trị *</label>
                                        <input asp-for="GiaTri" type="number" step="0.01" min="0" class="form-control" placeholder="0" required />
                                        <span asp-validation-for="GiaTri" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="GiaTriToiDa" class="form-label">Giá trị tối đa (VNĐ)</label>
                                        <input asp-for="GiaTriToiDa" type="number" min="0" class="form-control" placeholder="0" />
                                        <span asp-validation-for="GiaTriToiDa" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="NgayBatDau" class="form-label">Ngày bắt đầu *</label>
                                        <input asp-for="NgayBatDau" type="datetime-local" class="form-control" required />
                                        <span asp-validation-for="NgayBatDau" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="NgayKetThuc" class="form-label">Ngày kết thúc *</label>
                                        <input asp-for="NgayKetThuc" type="datetime-local" class="form-control" required />
                                        <span asp-validation-for="NgayKetThuc" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="SoLuongToiDa" class="form-label">Số lượng tối đa</label>
                                        <input asp-for="SoLuongToiDa" type="number" min="1" class="form-control" placeholder="Không giới hạn" />
                                        <span asp-validation-for="SoLuongToiDa" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="SoLanSuDungToiDa" class="form-label">Số lần sử dụng/khách hàng</label>
                                        <input asp-for="SoLanSuDungToiDa" type="number" min="1" class="form-control" placeholder="1" />
                                        <span asp-validation-for="SoLanSuDungToiDa" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input asp-for="TrangThaiHoatDong" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="TrangThaiHoatDong" class="form-check-label">
                                        Kích hoạt khuyến mãi
                                    </label>
                                </div>
                            </div>

                            <div class="text-end">
                                <a href="/Admin/KhuyenMai/Index" class="btn btn-secondary me-2">
                                    <i class="fas fa-arrow-left"></i> Quay lại
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Tạo khuyến mãi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>Debug Info:</h5>
                    <ul>
                        <li>Controller: KhuyenMaiController</li>
                        <li>Action: Create</li>
                        <li>Model: @(Model != null ? "Có" : "Null")</li>
                        <li>ViewBag.LoaiKhuyenMaiList: @(ViewBag.LoaiKhuyenMaiList != null ? "Có" : "Null")</li>
                        <li>ViewBag.TestMessage: @(ViewBag.TestMessage ?? "Null")</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
