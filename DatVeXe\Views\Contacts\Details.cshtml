@model DanhBaH<PERSON><PERSON><PERSON><PERSON><PERSON>
@{
    ViewData["Title"] = "Chi tiết liên hệ";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-info text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="bi bi-person-vcard me-2"></i>Chi tiết liên hệ
                    </h2>
                    <p class="mb-0 mt-2">Thông tin chi tiết hành khách trong danh bạ</p>
                </div>

                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-person me-2"></i>Tê<PERSON> hành khách
                                </label>
                                <div class="info-value">@Model.TenHanhKhach</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-telephone me-2"></i>Số điện thoại
                                </label>
                                <div class="info-value">
                                    <a href="tel:@Model.SoDienThoai" class="text-primary text-decoration-none">
                                        @Model.SoDienThoai
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-envelope me-2"></i>Email
                                </label>
                                <div class="info-value">
                                    @if (!string.IsNullOrEmpty(Model.Email))
                                    {
                                        <a href="mailto:@Model.Email" class="text-primary text-decoration-none">
                                            @Model.Email
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có thông tin</span>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-calendar me-2"></i>Ngày sinh
                                </label>
                                <div class="info-value">
                                    @if (Model.NgaySinh.HasValue)
                                    {
                                        @Model.NgaySinh.Value.ToString("dd/MM/yyyy")
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có thông tin</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-gender-ambiguous me-2"></i>Giới tính
                                </label>
                                <div class="info-value">
                                    @if (!string.IsNullOrEmpty(Model.GioiTinh))
                                    {
                                        <span class="badge bg-secondary">@Model.GioiTinh</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có thông tin</span>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-credit-card me-2"></i>CCCD/CMND
                                </label>
                                <div class="info-value">
                                    @if (!string.IsNullOrEmpty(Model.CCCD))
                                    {
                                        @Model.CCCD
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có thông tin</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.DiaChi))
                    {
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-geo-alt me-2"></i>Địa chỉ
                                    </label>
                                    <div class="info-value">@Model.DiaChi</div>
                                </div>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.GhiChu))
                    {
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-chat-text me-2"></i>Ghi chú
                                    </label>
                                    <div class="info-value">@Model.GhiChu</div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-calendar-plus me-2"></i>Ngày tạo
                                </label>
                                <div class="info-value">
                                    <small class="text-muted">@Model.NgayTao.ToString("dd/MM/yyyy HH:mm")</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-clock-history me-2"></i>Lần sử dụng cuối
                                </label>
                                <div class="info-value">
                                    @if (Model.LanSuDungCuoi.HasValue)
                                    {
                                        <small class="text-muted">@Model.LanSuDungCuoi.Value.ToString("dd/MM/yyyy HH:mm")</small>
                                    }
                                    else
                                    {
                                        <small class="text-muted">Chưa sử dụng</small>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="info-item">
                                <label class="info-label">
                                    <i class="bi bi-graph-up me-2"></i>Thống kê sử dụng
                                </label>
                                <div class="info-value">
                                    <span class="badge bg-success">Đã sử dụng @Model.SoLanSuDung lần</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                        <a asp-action="Edit" asp-route-id="@Model.DanhBaId" class="btn btn-warning btn-lg px-4 me-md-2">
                            <i class="bi bi-pencil me-2"></i>Chỉnh sửa
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.DanhBaId" class="btn btn-danger btn-lg px-4 me-md-2">
                            <i class="bi bi-trash me-2"></i>Xóa
                        </a>
                        <a asp-action="Index" class="btn btn-outline-secondary btn-lg px-4">
                            <i class="bi bi-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .card-header {
        border-radius: 0;
    }
    
    .info-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        height: 100%;
        border-left: 4px solid #17a2b8;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .info-value {
        font-size: 1.1rem;
        color: #212529;
        word-break: break-word;
    }
    
    .btn {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    .badge {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
    
    .text-primary:hover {
        text-decoration: underline !important;
    }
</style>
