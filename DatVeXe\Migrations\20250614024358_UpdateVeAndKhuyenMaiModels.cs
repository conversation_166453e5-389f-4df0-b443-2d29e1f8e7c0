﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class UpdateVeAndKhuyenMaiModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "KhuyenMaiId",
                table: "Ves",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SoTienGiam",
                table: "Ves",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "DanhGiaChuyenDis",
                columns: table => new
                {
                    DanhGiaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    VeId = table.Column<int>(type: "int", nullable: false),
                    NguoiDungId = table.Column<int>(type: "int", nullable: false),
                    DiemDanhGia = table.Column<int>(type: "int", nullable: false),
                    NhanXet = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ChatLuongXe = table.Column<int>(type: "int", nullable: false),
                    ThaiDoTaiXe = table.Column<int>(type: "int", nullable: false),
                    DungGio = table.Column<int>(type: "int", nullable: false),
                    GiaCa = table.Column<int>(type: "int", nullable: false),
                    NgayDanhGia = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TrangThaiHienThi = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DanhGiaChuyenDis", x => x.DanhGiaId);
                    table.ForeignKey(
                        name: "FK_DanhGiaChuyenDis_NguoiDungs_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "NguoiDungs",
                        principalColumn: "NguoiDungId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DanhGiaChuyenDis_Ves_VeId",
                        column: x => x.VeId,
                        principalTable: "Ves",
                        principalColumn: "VeId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KhuyenMais",
                columns: table => new
                {
                    KhuyenMaiId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TenKhuyenMai = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MaKhuyenMai = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    MoTa = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    PhanTramGiam = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    SoTienGiamToiDa = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    GiaTriDonHangToiThieu = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    NgayBatDau = table.Column<DateTime>(type: "datetime2", nullable: false),
                    NgayKetThuc = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SoLuong = table.Column<int>(type: "int", nullable: true),
                    SoLuongDaSuDung = table.Column<int>(type: "int", nullable: false),
                    TrangThaiHoatDong = table.Column<bool>(type: "bit", nullable: false),
                    ApDungChoKhachHangMoi = table.Column<bool>(type: "bit", nullable: false),
                    NgayTao = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KhuyenMais", x => x.KhuyenMaiId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Ves_KhuyenMaiId",
                table: "Ves",
                column: "KhuyenMaiId");

            migrationBuilder.CreateIndex(
                name: "IX_DanhGiaChuyenDis_NguoiDungId",
                table: "DanhGiaChuyenDis",
                column: "NguoiDungId");

            migrationBuilder.CreateIndex(
                name: "IX_DanhGiaChuyenDis_VeId",
                table: "DanhGiaChuyenDis",
                column: "VeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Ves_KhuyenMais_KhuyenMaiId",
                table: "Ves",
                column: "KhuyenMaiId",
                principalTable: "KhuyenMais",
                principalColumn: "KhuyenMaiId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Ves_KhuyenMais_KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropTable(
                name: "DanhGiaChuyenDis");

            migrationBuilder.DropTable(
                name: "KhuyenMais");

            migrationBuilder.DropIndex(
                name: "IX_Ves_KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropColumn(
                name: "KhuyenMaiId",
                table: "Ves");

            migrationBuilder.DropColumn(
                name: "SoTienGiam",
                table: "Ves");
        }
    }
}
