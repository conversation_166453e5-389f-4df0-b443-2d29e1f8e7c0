using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;

namespace DatVeXe.Middleware
{
    public class UserStatusCheckMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public UserStatusCheckMiddleware(RequestDelegate next, IServiceScopeFactory serviceScopeFactory)
        {
            _next = next;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Chỉ kiểm tra nếu user đã đăng nhập
            var userId = context.Session.GetInt32("UserId");
            
            if (userId.HasValue)
            {
                // Bỏ qua kiểm tra cho các request đến trang đăng xuất và các tài nguyên tĩnh
                var path = context.Request.Path.Value?.ToLower();
                if (path != null && (
                    path.Contains("/taikhoan/dangxuat") ||
                    path.Contains("/css/") ||
                    path.Contains("/js/") ||
                    path.Contains("/images/") ||
                    path.Contains("/lib/") ||
                    path.Contains("/favicon.ico")))
                {
                    await _next(context);
                    return;
                }

                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<DatVeXeContext>();
                    
                    try
                    {
                        var user = await dbContext.NguoiDungs.FindAsync(userId.Value);
                        
                        if (user == null || !user.TrangThaiHoatDong || user.TaiKhoanBiKhoa)
                        {
                            // Xóa session và chuyển hướng về trang đăng nhập
                            context.Session.Clear();
                            
                            // Thiết lập thông báo lỗi
                            string message = "Phiên đăng nhập đã hết hạn.";
                            if (user != null)
                            {
                                if (!user.TrangThaiHoatDong)
                                {
                                    message = "Tài khoản của bạn đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên.";
                                }
                                else if (user.TaiKhoanBiKhoa)
                                {
                                    message = "Tài khoản của bạn đã bị khóa.";
                                    if (!string.IsNullOrEmpty(user.LyDoKhoa))
                                    {
                                        message += $" Lý do: {user.LyDoKhoa}";
                                    }
                                    message += " Vui lòng liên hệ quản trị viên.";
                                }
                            }
                            
                            // Kiểm tra nếu là AJAX request
                            if (context.Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                                context.Request.ContentType?.Contains("application/json") == true)
                            {
                                context.Response.StatusCode = 401;
                                context.Response.ContentType = "application/json";
                                await context.Response.WriteAsync($"{{\"success\": false, \"message\": \"{message}\", \"redirect\": \"/TaiKhoan/DangNhap\"}}");
                                return;
                            }
                            else
                            {
                                // Lưu thông báo vào TempData thông qua session tạm thời
                                context.Session.SetString("TempMessage", message);
                                context.Response.Redirect("/TaiKhoan/DangNhap");
                                return;
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // Nếu có lỗi khi kiểm tra database, bỏ qua và tiếp tục
                        // Điều này tránh làm crash ứng dụng nếu có vấn đề với database
                    }
                }
            }

            await _next(context);
        }
    }

    // Extension method để đăng ký middleware
    public static class UserStatusCheckMiddlewareExtensions
    {
        public static IApplicationBuilder UseUserStatusCheck(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserStatusCheckMiddleware>();
        }
    }
}
