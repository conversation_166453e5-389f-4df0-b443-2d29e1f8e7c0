/**
 * Color Contrast Checker and Auto-Fixer
 * Tự động kiểm tra và sửa các vấn đề về contrast màu sắc
 */

class ColorContrastChecker {
    constructor() {
        this.init();
    }

    init() {
        // Ch<PERSON>y kiểm tra khi DOM đã load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.checkAndFixContrast());
        } else {
            this.checkAndFixContrast();
        }

        // Chạy lại kiểm tra khi có thay đổi DOM
        this.observeChanges();
    }

    checkAndFixContrast() {
        console.log('🎨 Checking color contrast...');
        
        // Kiểm tra và sửa các element có vấn đề contrast
        this.fixCommonContrastIssues();
        this.fixTableContrast();
        this.fixFormContrast();
        this.fixButtonContrast();
        this.fixCardContrast();
        this.fixModalContrast();
        this.fixAlertContrast();
        
        console.log('✅ Color contrast check completed');
    }

    fixCommonContrastIssues() {
        // Sửa text có màu quá nhạt trên nền tối
        const lightTextOnDark = document.querySelectorAll('body *');
        lightTextOnDark.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            const backgroundColor = computedStyle.backgroundColor;
            const color = computedStyle.color;
            
            // Nếu nền tối và chữ cũng tối
            if (this.isDarkColor(backgroundColor) && this.isDarkColor(color)) {
                element.style.color = '#ffffff';
            }
            
            // Nếu nền sáng và chữ cũng sáng
            if (this.isLightColor(backgroundColor) && this.isLightColor(color)) {
                element.style.color = '#333333';
            }
        });
    }

    fixTableContrast() {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            // Đảm bảo table có nền trắng và chữ đen
            table.style.backgroundColor = '#ffffff';
            table.style.color = '#333333';
            
            // Sửa header
            const headers = table.querySelectorAll('th');
            headers.forEach(th => {
                th.style.backgroundColor = '#f8f9fa';
                th.style.color = '#333333';
                th.style.borderBottom = '2px solid #dee2e6';
            });
            
            // Sửa cells
            const cells = table.querySelectorAll('td');
            cells.forEach(td => {
                td.style.backgroundColor = '#ffffff';
                td.style.color = '#333333';
                td.style.borderTop = '1px solid #dee2e6';
            });
            
            // Sửa striped rows
            const oddRows = table.querySelectorAll('tbody tr:nth-child(odd)');
            oddRows.forEach(row => {
                row.style.backgroundColor = '#f8f9fa';
            });
        });
    }

    fixFormContrast() {
        // Sửa form controls
        const formControls = document.querySelectorAll('.form-control, .form-select, input, select, textarea');
        formControls.forEach(control => {
            control.style.backgroundColor = '#ffffff';
            control.style.color = '#333333';
            control.style.border = '1px solid #ced4da';
        });
        
        // Sửa labels
        const labels = document.querySelectorAll('label, .form-label');
        labels.forEach(label => {
            const parent = label.closest('.card, .modal, .bg-white');
            if (parent) {
                label.style.color = '#333333';
            } else {
                label.style.color = '#ffffff';
            }
        });
    }

    fixButtonContrast() {
        // Sửa buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            if (button.classList.contains('btn-primary')) {
                button.style.backgroundColor = '#6CABDD';
                button.style.borderColor = '#6CABDD';
                button.style.color = '#ffffff';
            } else if (button.classList.contains('btn-secondary')) {
                button.style.backgroundColor = '#6c757d';
                button.style.borderColor = '#6c757d';
                button.style.color = '#ffffff';
            } else if (button.classList.contains('btn-success')) {
                button.style.backgroundColor = '#198754';
                button.style.borderColor = '#198754';
                button.style.color = '#ffffff';
            } else if (button.classList.contains('btn-danger')) {
                button.style.backgroundColor = '#dc3545';
                button.style.borderColor = '#dc3545';
                button.style.color = '#ffffff';
            } else if (button.classList.contains('btn-warning')) {
                button.style.backgroundColor = '#ffc107';
                button.style.borderColor = '#ffc107';
                button.style.color = '#000000';
            } else if (button.classList.contains('btn-light')) {
                button.style.backgroundColor = '#f8f9fa';
                button.style.borderColor = '#f8f9fa';
                button.style.color = '#333333';
            }
        });
    }

    fixCardContrast() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.backgroundColor = '#ffffff';
            card.style.color = '#333333';
            card.style.border = '1px solid #dee2e6';
            
            // Sửa card header
            const header = card.querySelector('.card-header');
            if (header) {
                header.style.backgroundColor = '#f8f9fa';
                header.style.color = '#333333';
                header.style.borderBottom = '1px solid #dee2e6';
            }
            
            // Sửa card body
            const body = card.querySelector('.card-body');
            if (body) {
                body.style.backgroundColor = '#ffffff';
                body.style.color = '#333333';
            }
            
            // Sửa card footer
            const footer = card.querySelector('.card-footer');
            if (footer) {
                footer.style.backgroundColor = '#f8f9fa';
                footer.style.color = '#333333';
                footer.style.borderTop = '1px solid #dee2e6';
            }
        });
    }

    fixModalContrast() {
        const modals = document.querySelectorAll('.modal-content');
        modals.forEach(modal => {
            modal.style.backgroundColor = '#ffffff';
            modal.style.color = '#333333';
            
            const header = modal.querySelector('.modal-header');
            if (header) {
                header.style.backgroundColor = '#f8f9fa';
                header.style.color = '#333333';
            }
            
            const body = modal.querySelector('.modal-body');
            if (body) {
                body.style.backgroundColor = '#ffffff';
                body.style.color = '#333333';
            }
            
            const footer = modal.querySelector('.modal-footer');
            if (footer) {
                footer.style.backgroundColor = '#f8f9fa';
                footer.style.color = '#333333';
            }
        });
    }

    fixAlertContrast() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-primary')) {
                alert.style.backgroundColor = 'rgba(108, 171, 221, 0.1)';
                alert.style.borderColor = '#6CABDD';
                alert.style.color = '#0a2640';
            } else if (alert.classList.contains('alert-success')) {
                alert.style.backgroundColor = 'rgba(25, 135, 84, 0.1)';
                alert.style.borderColor = '#198754';
                alert.style.color = '#0f5132';
            } else if (alert.classList.contains('alert-danger')) {
                alert.style.backgroundColor = 'rgba(220, 53, 69, 0.1)';
                alert.style.borderColor = '#dc3545';
                alert.style.color = '#721c24';
            } else if (alert.classList.contains('alert-warning')) {
                alert.style.backgroundColor = 'rgba(255, 193, 7, 0.1)';
                alert.style.borderColor = '#ffc107';
                alert.style.color = '#664d03';
            }
        });
    }

    isDarkColor(color) {
        if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)') return false;
        
        const rgb = this.getRGBValues(color);
        if (!rgb) return false;
        
        // Tính luminance
        const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
        return luminance < 0.5;
    }

    isLightColor(color) {
        if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)') return false;
        
        const rgb = this.getRGBValues(color);
        if (!rgb) return false;
        
        // Tính luminance
        const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
        return luminance > 0.7;
    }

    getRGBValues(color) {
        if (!color) return null;
        
        // Xử lý rgb() và rgba()
        const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }
        
        // Xử lý hex colors
        const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
        if (hexMatch) {
            return {
                r: parseInt(hexMatch[1], 16),
                g: parseInt(hexMatch[2], 16),
                b: parseInt(hexMatch[3], 16)
            };
        }
        
        return null;
    }

    observeChanges() {
        // Theo dõi thay đổi DOM để tự động sửa contrast
        const observer = new MutationObserver((mutations) => {
            let shouldCheck = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldCheck = true;
                }
            });
            
            if (shouldCheck) {
                // Debounce để tránh chạy quá nhiều lần
                clearTimeout(this.checkTimeout);
                this.checkTimeout = setTimeout(() => {
                    this.checkAndFixContrast();
                }, 500);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Public method để force check
    forceCheck() {
        this.checkAndFixContrast();
    }
}

// Khởi tạo checker
const colorChecker = new ColorContrastChecker();

// Export để có thể sử dụng từ console
window.colorChecker = colorChecker;
