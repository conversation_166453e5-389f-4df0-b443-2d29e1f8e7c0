@model IEnumerable<DatVeXe.Models.TaiXe>

@{
    ViewData["Title"] = "Quản lý Tài xế";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-tie mr-2"></i>
                        Quản lý Tài xế
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Thêm tài xế mới
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Bộ lọc tìm kiếm -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Tìm kiếm:</label>
                                    <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" 
                                           class="form-control" placeholder="Tên, SĐT, CMND, Số bằng lái...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Trạng thái:</label>
                                    <select name="trangThai" class="form-control">
                                        <option value="">-- Tất cả --</option>
                                        <option value="1" selected="@(ViewData["CurrentTrangThai"]?.ToString() == "1")">Hoạt động</option>
                                        <option value="2" selected="@(ViewData["CurrentTrangThai"]?.ToString() == "2")">Nghỉ phép</option>
                                        <option value="3" selected="@(ViewData["CurrentTrangThai"]?.ToString() == "3")">Tạm nghỉ</option>
                                        <option value="4" selected="@(ViewData["CurrentTrangThai"]?.ToString() == "4")">Đã nghỉ việc</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-search"></i> Tìm kiếm
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Thông báo -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    }

                    <!-- Bảng danh sách tài xế -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Họ tên</th>
                                    <th>Số điện thoại</th>
                                    <th>CMND</th>
                                    <th>Số bằng lái</th>
                                    <th>Loại bằng lái</th>
                                    <th>Kinh nghiệm</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày vào làm</th>
                                    <th width="150">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.HoTen)</strong>
                                            @if (!string.IsNullOrEmpty(item.GioiTinh))
                                            {
                                                <br><small class="text-muted">@item.GioiTinh</small>
                                            }
                                        </td>
                                        <td>@Html.DisplayFor(modelItem => item.SoDienThoai)</td>
                                        <td>@Html.DisplayFor(modelItem => item.CMND)</td>
                                        <td>@Html.DisplayFor(modelItem => item.SoBangLai)</td>
                                        <td>
                                            @switch (item.LoaiBangLai)
                                            {
                                                case LoaiBangLai.B1:
                                                    <span class="badge badge-primary">B1</span>
                                                    break;
                                                case LoaiBangLai.B2:
                                                    <span class="badge badge-primary">B2</span>
                                                    break;
                                                case LoaiBangLai.C:
                                                    <span class="badge badge-warning">C</span>
                                                    break;
                                                case LoaiBangLai.D:
                                                    <span class="badge badge-success">D</span>
                                                    break;
                                                case LoaiBangLai.E:
                                                    <span class="badge badge-danger">E</span>
                                                    break;
                                                case LoaiBangLai.F:
                                                    <span class="badge badge-dark">F</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (item.KinhNghiem.HasValue)
                                            {
                                                <span>@item.KinhNghiem năm</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Chưa có</span>
                                            }
                                        </td>
                                        <td>
                                            @switch (item.TrangThai)
                                            {
                                                case TrangThaiTaiXe.HoatDong:
                                                    <span class="badge badge-success">Hoạt động</span>
                                                    break;
                                                case TrangThaiTaiXe.NghiPhep:
                                                    <span class="badge badge-info">Nghỉ phép</span>
                                                    break;
                                                case TrangThaiTaiXe.TamNghi:
                                                    <span class="badge badge-warning">Tạm nghỉ</span>
                                                    break;
                                                case TrangThaiTaiXe.DaNghiViec:
                                                    <span class="badge badge-danger">Đã nghỉ việc</span>
                                                    break;
                                            }
                                        </td>
                                        <td>@item.NgayVaoLam.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.TaiXeId" 
                                                   class="btn btn-info btn-sm" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.TaiXeId" 
                                                   class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.TaiXeId" 
                                                   class="btn btn-danger btn-sm" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Không tìm thấy tài xế nào</h5>
                            <p class="text-muted">Hãy thử thay đổi điều kiện tìm kiếm hoặc thêm tài xế mới.</p>
                        </div>
                    }

                    <!-- Phân trang -->
                    @if (ViewBag.TotalPages > 1)
                    {
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                @if (ViewBag.CurrentPage > 1)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="?page=@(ViewBag.CurrentPage - 1)&searchString=@ViewData["CurrentFilter"]&trangThai=@ViewData["CurrentTrangThai"]">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                }

                                @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                                {
                                    <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                        <a class="page-link" href="?page=@i&searchString=@ViewData["CurrentFilter"]&trangThai=@ViewData["CurrentTrangThai"]">@i</a>
                                    </li>
                                }

                                @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="?page=@(ViewBag.CurrentPage + 1)&searchString=@ViewData["CurrentFilter"]&trangThai=@ViewData["CurrentTrangThai"]">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </nav>

                        <div class="text-center text-muted">
                            Hiển thị @((ViewBag.CurrentPage - 1) * 10 + 1) - @(Math.Min(ViewBag.CurrentPage * 10, ViewBag.TotalItems)) 
                            trong tổng số @ViewBag.TotalItems tài xế
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
