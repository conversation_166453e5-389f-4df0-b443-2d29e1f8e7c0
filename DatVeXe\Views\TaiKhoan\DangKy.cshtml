@{
    ViewData["Title"] = "Đăng ký";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Đặt Vé Xe</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
            height: 100vh;
            overflow-x: hidden;
            background-color: #f5f5f5;
        }

        .login-page {
            display: flex;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .login-image {
            flex: 1;
            background: url('/images/mancity.webp') center/cover no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .login-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(10, 38, 64, 0.9) 0%, rgba(10, 38, 64, 0.7) 50%, rgba(108, 171, 221, 0.6) 100%);
            z-index: 1;
        }

        .login-image-content {
            position: relative;
            z-index: 2;
            max-width: 80%;
        }

        .login-logo-container {
            width: 120px;
            height: 120px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            border: 3px solid #6cabdd;
        }

        .login-logo-icon {
            font-size: 60px;
            color: #6cabdd;
        }

        .login-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            color: #6cabdd;
        }

        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0 auto;
            line-height: 1.6;
        }

        .login-form-section {
            flex: 1;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 3rem 2rem;
        }

        .login-form-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 2rem;
        }

        .login-tab {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            color: #6c757d;
            cursor: pointer;
            position: relative;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .login-tab.active {
            color: #6cabdd;
            font-weight: 700;
            border-bottom: 3px solid #6cabdd;
        }

        .login-form-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0c2340;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #0c2340;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #6cabdd;
            box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.15);
            outline: none;
        }

        .password-input-container {
            position: relative;
        }

        .input-with-icon {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            z-index: 1;
        }

        .input-with-icon .form-control,
        .password-input-container .form-control {
            padding-left: 40px;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 0.25rem;
            transition: color 0.2s;
        }

        .password-toggle:hover {
            color: #0c2340;
        }

        .login-button {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #6cabdd;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .login-button:hover {
            background-color: #0c2340;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .register-section {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e0e0e0;
        }

        .register-text {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 1rem;
        }

        .register-link {
            display: inline-block;
            padding: 0.75rem 2rem;
            background-color: #0c2340;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .register-link:hover {
            background-color: #6cabdd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .social-login {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 2rem;
        }

        .social-login-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .social-login-text::before,
        .social-login-text::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60px;
            height: 1px;
            background-color: #e0e0e0;
        }

        .social-login-text::before {
            left: -70px;
        }

        .social-login-text::after {
            right: -70px;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            gap: 1.25rem;
        }

        .social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f8f9fa;
            color: #6cabdd;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .social-icon:hover {
            background-color: #6cabdd;
            color: white;
            transform: translateY(-2px);
        }

        .footer {
            margin-top: 3rem;
            text-align: center;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-bottom: 1rem;
        }

        .footer-link {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s;
        }

        .footer-link:hover {
            color: #6cabdd;
        }

        /* Fix cho checkbox và label */
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .form-check-input {
            margin-right: 0.5rem;
            margin-top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid #6cabdd;
            border-radius: 3px;
            background-color: white;
        }

        .form-check-input:checked {
            background-color: #6cabdd;
            border-color: #6cabdd;
        }

        .form-check-input:focus {
            border-color: #6cabdd;
            box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.25);
        }

        .form-check-label {
            color: #0c2340;
            font-weight: 500;
            font-size: 0.95rem;
            cursor: pointer;
            user-select: none;
        }

        .form-check-label:hover {
            color: #6cabdd;
        }

        .form-check-label a {
            color: #6cabdd;
            text-decoration: none;
            font-weight: 600;
        }

        .form-check-label a:hover {
            color: #0c2340;
            text-decoration: underline;
        }

        /* Google Login Button */
        .google-login-section {
            text-align: center;
            margin: 1.5rem 0;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }

        .divider-text {
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .google-login-button {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #fff;
            color: #333;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .google-login-button:hover {
            background-color: #f8f9fa;
            border-color: #6cabdd;
            color: #333;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        @@media (max-width: 768px) {
            .login-page {
                flex-direction: column;
            }

            .login-image {
                min-height: 200px;
            }

            .login-form-container {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-page">
        <div class="login-image">
            <div class="login-image-content">
                <div class="login-logo-container">
                    <i class="bi bi-bus-front login-logo-icon"></i>
                </div>
                <h1 class="login-title">ĐẶT VÉ XE</h1>
                <p class="login-subtitle">Chào mừng bạn đến với hệ thống đặt vé xe khách trực tuyến hàng đầu Việt Nam. Đặt vé dễ dàng, thanh toán nhanh chóng và trải nghiệm hành trình tuyệt vời.</p>
            </div>
        </div>

        <div class="login-form-section">
            <div class="login-form-container">
                <div class="login-tabs">
                    <a href="/TaiKhoan/DangNhap" class="login-tab">Đăng nhập</a>
                    <a href="/TaiKhoan/DangKy" class="login-tab active">Đăng ký</a>
                </div>

                <h2 class="login-form-title">Đăng ký</h2>

                <form asp-action="DangKy" method="post" class="registration-form" id="registrationForm">
                    <div class="form-group">
                        <label for="hoTen" class="form-label">Họ tên</label>
                        <div class="input-with-icon">
                            <i class="bi bi-person input-icon"></i>
                            <input type="text" id="hoTen" name="hoTen" class="form-control" placeholder="Nhập họ tên đầy đủ của bạn" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-with-icon">
                            <i class="bi bi-envelope input-icon"></i>
                            <input type="email" id="email" name="email" class="form-control" placeholder="Nhập địa chỉ email của bạn" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="soDienThoai" class="form-label">Số điện thoại</label>
                        <div class="input-with-icon">
                            <i class="bi bi-telephone input-icon"></i>
                            <input type="tel" id="soDienThoai" name="soDienThoai" class="form-control" placeholder="Nhập số điện thoại (VD: 0123456789)" pattern="^0[0-9]{9}$" required>
                        </div>
                        <small class="form-text text-muted">Số điện thoại phải bắt đầu bằng số 0 và có 10 chữ số</small>
                    </div>

                    <div class="form-group">
                        <label for="matKhau" class="form-label">Mật khẩu</label>
                        <div class="password-input-container">
                            <i class="bi bi-lock input-icon"></i>
                            <input type="password" id="matKhau" name="matKhau" class="form-control" placeholder="Tạo mật khẩu mới (tối thiểu 8 ký tự)" required minlength="8">
                            <button type="button" class="password-toggle" onclick="togglePassword('matKhau')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">Mật khẩu phải có ít nhất 8 ký tự</small>
                    </div>

                    <div class="form-group">
                        <label for="xacNhanMatKhau" class="form-label">Xác nhận mật khẩu</label>
                        <div class="password-input-container">
                            <i class="bi bi-lock-fill input-icon"></i>
                            <input type="password" id="xacNhanMatKhau" name="xacNhanMatKhau" class="form-control" placeholder="Nhập lại mật khẩu của bạn" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('xacNhanMatKhau')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <div id="passwordMatchError" class="invalid-feedback" style="display: none;">
                            Mật khẩu xác nhận không khớp!
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="agreeTerms" name="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            Tôi đồng ý với <a href="#" class="text-primary">điều khoản sử dụng</a> và <a href="#" class="text-primary">chính sách bảo mật</a>
                        </label>
                    </div>

                    <button type="submit" class="login-button">Đăng ký</button>
                </form>

                @if (TempData["ThongBao"] != null)
                {
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle me-2"></i>@TempData["ThongBao"]
                    </div>
                }

                <div class="register-section">
                    <p class="register-text">Đã có tài khoản?</p>
                    <a href="/TaiKhoan/DangNhap" class="register-link">Đăng nhập ngay</a>
                </div>

                <div class="google-login-section">
                    <div class="divider">
                        <span class="divider-text">Hoặc</span>
                    </div>
                    <a href="/TaiKhoan/GoogleLogin" class="google-login-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Đăng ký bằng Google
                    </a>
                </div>

                <div class="footer">
                    <div class="footer-links">
                        <a href="#" class="footer-link">Chính sách bảo mật</a>
                        <a href="#" class="footer-link">Điều khoản sử dụng</a>
                        <a href="#" class="footer-link">Trợ giúp & Hỗ trợ</a>
                        <a href="#" class="footer-link">Liên hệ</a>
                    </div>
                    <p>&copy; 2025 Đặt Vé Xe. Tất cả các quyền được bảo lưu.</p>
                </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const icon = document.querySelector(`#${inputId}`).nextElementSibling.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registrationForm');
            const password = document.getElementById('matKhau');
            const confirmPassword = document.getElementById('xacNhanMatKhau');
            const phoneNumber = document.getElementById('soDienThoai');
            const passwordMatchError = document.getElementById('passwordMatchError');

            // Phone number validation
            phoneNumber.addEventListener('input', function() {
                const phonePattern = /^0[0-9]{9}$/;
                if (!phonePattern.test(phoneNumber.value) && phoneNumber.value.length > 0) {
                    phoneNumber.setCustomValidity('Số điện thoại phải bắt đầu bằng số 0 và có 10 chữ số');
                } else {
                    phoneNumber.setCustomValidity('');
                }
            });

            // Check password match on input
            confirmPassword.addEventListener('input', function() {
                if (password.value !== confirmPassword.value) {
                    passwordMatchError.style.display = 'block';
                    confirmPassword.setCustomValidity('Mật khẩu không khớp');
                } else {
                    passwordMatchError.style.display = 'none';
                    confirmPassword.setCustomValidity('');
                }
            });

            // Check password match on form submit
            form.addEventListener('submit', function(event) {
                if (password.value !== confirmPassword.value) {
                    event.preventDefault();
                    passwordMatchError.style.display = 'block';
                    confirmPassword.focus();
                }

                // Check phone number format
                const phonePattern = /^0[0-9]{9}$/;
                if (!phonePattern.test(phoneNumber.value)) {
                    event.preventDefault();
                    phoneNumber.focus();
                    alert('Số điện thoại không hợp lệ! Phải bắt đầu bằng số 0 và có 10 chữ số.');
                }
            });
        });
    </script>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
</body>
</html>

<style>
    body {
        background-color: #98c5e9;
        background-image: linear-gradient(to bottom right, #98c5e9, #6cabdd);
        font-family: 'Segoe UI', Arial, sans-serif;
        margin: 0;
        padding: 0;
        min-height: 100vh;
    }

    .login-container {
        padding: 2rem 0;
    }

    .login-header {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-top: 2rem;
    }

    .login-logo {
        margin-top: 2rem;
    }

    .login-tabs {
        display: flex;
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 2rem;
    }

    .login-tab {
        flex: 1;
        text-align: center;
        padding: 1rem 0;
        background-color: #f8f9fa;
    }

    .login-tab.active {
        background-color: white;
        border-bottom: 3px solid #6cabdd;
    }

    .login-tab-link {
        color: #333;
        text-decoration: none;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 0.9rem;
        display: block;
    }

    .login-tab.active .login-tab-link {
        color: #6cabdd;
    }

    .login-form-container {
        padding: 0 2rem 2rem;
    }

    .login-form label {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .login-form .form-control {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    .password-input-container {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
    }

    .forgot-password-link {
        color: #6cabdd;
        text-decoration: none;
        font-size: 0.9rem;
    }

    .login-button {
        background-color: #002a5c;
        border: none;
        border-radius: 4px;
        color: white;
        font-weight: bold;
        padding: 0.75rem 0;
        width: 100%;
        text-transform: uppercase;
        font-size: 1rem;
        margin-top: 1rem;
    }

    .login-button:hover {
        background-color: #001f44;
    }

    .login-info-box {
        background-color: #e8f4f8;
        border-radius: 4px;
        padding: 1rem;
        font-size: 0.9rem;
        color: #0c5460;
    }

    .login-link {
        color: #6cabdd;
        text-decoration: none;
        font-weight: 500;
    }

    .register-prompt {
        margin-top: 2rem;
    }

    .register-now-button {
        display: inline-block;
        background-color: #6cabdd;
        color: white;
        text-decoration: none;
        padding: 0.5rem 2rem;
        border-radius: 4px;
        font-weight: bold;
        margin-top: 0.5rem;
    }

    .register-now-button:hover {
        background-color: #5a91ba;
    }

    .login-social {
        border-top: 1px solid #e0e0e0;
        padding-top: 1.5rem;
    }

    .social-links {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #f8f9fa;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .social-link:hover {
        background-color: #6cabdd;
        color: white;
    }

    .login-footer {
        border-top: 1px solid #e0e0e0;
        padding-top: 1.5rem;
        text-align: center;
    }

    .footer-links {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin-bottom: 1rem;
    }

    .footer-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.8rem;
    }

    .copyright {
        color: #6c757d;
        font-size: 0.8rem;
    }
</style>

<script>
    function togglePassword(inputId) {
        const passwordInput = document.getElementById(inputId);
        const icon = document.querySelector(`#${inputId}`).nextElementSibling.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }
    }
</script>
