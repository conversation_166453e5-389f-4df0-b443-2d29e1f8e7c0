@model DanhBaHanhKhach
@{
    ViewData["Title"] = "Xóa liên hệ";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-danger text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>Xác nhận xóa liên hệ
                    </h2>
                    <p class="mb-0 mt-2">Bạn có chắc chắn muốn xóa liên hệ này khỏi danh bạ?</p>
                </div>

                <div class="card-body p-4">
                    <div class="alert alert-warning" role="alert">
                        <h5 class="alert-heading">
                            <i class="bi bi-exclamation-triangle me-2"></i>Cảnh báo!
                        </h5>
                        <p class="mb-0">Hành động này không thể hoàn tác. Tất cả thông tin liên hệ sẽ bị xóa vĩnh viễn.</p>
                    </div>

                    <div class="contact-info-preview">
                        <h5 class="text-danger mb-3">
                            <i class="bi bi-person-x me-2"></i>Thông tin liên hệ sẽ bị xóa:
                        </h5>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-person me-2"></i>Tên hành khách
                                    </label>
                                    <div class="info-value">@Model.TenHanhKhach</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-telephone me-2"></i>Số điện thoại
                                    </label>
                                    <div class="info-value">@Model.SoDienThoai</div>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Email))
                        {
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="info-item">
                                        <label class="info-label">
                                            <i class="bi bi-envelope me-2"></i>Email
                                        </label>
                                        <div class="info-value">@Model.Email</div>
                                    </div>
                                </div>

                                @if (Model.NgaySinh.HasValue)
                                {
                                    <div class="col-md-6 mb-3">
                                        <div class="info-item">
                                            <label class="info-label">
                                                <i class="bi bi-calendar me-2"></i>Ngày sinh
                                            </label>
                                            <div class="info-value">@Model.NgaySinh.Value.ToString("dd/MM/yyyy")</div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-calendar-plus me-2"></i>Ngày tạo
                                    </label>
                                    <div class="info-value">
                                        <small>@Model.NgayTao.ToString("dd/MM/yyyy HH:mm")</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="info-item">
                                    <label class="info-label">
                                        <i class="bi bi-graph-up me-2"></i>Số lần sử dụng
                                    </label>
                                    <div class="info-value">
                                        <span class="badge bg-info">@Model.SoLanSuDung lần</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form asp-action="Delete" method="post" class="mt-4">
                        <input type="hidden" asp-for="DanhBaId" />
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button type="submit" class="btn btn-danger btn-lg px-5 me-md-2">
                                <i class="bi bi-trash me-2"></i>Xác nhận xóa
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.DanhBaId" class="btn btn-secondary btn-lg px-5 me-md-2">
                                <i class="bi bi-eye me-2"></i>Xem chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-outline-secondary btn-lg px-5">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .card-header {
        border-radius: 0;
    }
    
    .contact-info-preview {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        border: 2px dashed #dc3545;
    }
    
    .info-item {
        background: white;
        border-radius: 8px;
        padding: 0.75rem;
        height: 100%;
        border-left: 3px solid #dc3545;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
        display: block;
    }
    
    .info-value {
        font-size: 1rem;
        color: #212529;
        word-break: break-word;
    }
    
    .btn {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
    
    .alert {
        border-radius: 10px;
    }
    
    .badge {
        font-size: 0.85rem;
        padding: 0.4rem 0.6rem;
    }
</style>
