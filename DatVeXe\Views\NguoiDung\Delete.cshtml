@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Xóa người dùng";
    var hasBookings = ViewBag.HasBookings as bool? ?? false;
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> @ViewData["Title"]
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa người dùng này không? Hành động này không thể hoàn tác.
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-user"></i> Họ tên:</strong></td>
                                    <td><span class="text-primary fw-bold">@Model.HoTen</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-envelope"></i> Email:</strong></td>
                                    <td><span class="text-muted">@Model.Email</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-shield-alt"></i> Quyền:</strong></td>
                                    <td>
                                        @if (Model.LaAdmin)
                                        {
                                            <span class="badge bg-danger fs-6">
                                                <i class="fas fa-crown"></i> Admin
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-info fs-6">
                                                <i class="fas fa-user"></i> Khách hàng
                                            </span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (hasBookings)
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-ban"></i>
                            <strong>Không thể xóa!</strong> Người dùng này đã có vé đặt trong hệ thống. Vui lòng xóa tất cả vé trước khi xóa người dùng.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a asp-action="Details" asp-route-id="@Model.NguoiDungId" class="btn btn-info">
                                <i class="fas fa-info-circle"></i> Xem chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    }
                    else
                    {
                        @if (Model.LaAdmin)
                        {
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-crown"></i>
                                <strong>Lưu ý:</strong> Bạn đang xóa một tài khoản quản trị viên. Hãy chắc chắn rằng điều này là cần thiết.
                            </div>
                        }

                        <form asp-action="Delete" method="post" id="deleteForm">
                            <input type="hidden" asp-for="NguoiDungId" />
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-danger" id="deleteBtn">
                                    <i class="fas fa-trash"></i> Xác nhận xóa
                                </button>
                                <a asp-action="Details" asp-route-id="@Model.NguoiDungId" class="btn btn-info">
                                    <i class="fas fa-info-circle"></i> Xem chi tiết
                                </a>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Hủy bỏ
                                </a>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#deleteForm').on('submit', function(e) {
                e.preventDefault();
                
                var userName = '@Model.HoTen';
                var isAdmin = @Model.LaAdmin.ToString().ToLower();
                
                var confirmMessage = 'Bạn có chắc chắn muốn xóa người dùng "' + userName + '" không?\n\nHành động này không thể hoàn tác!';
                
                if (isAdmin) {
                    confirmMessage += '\n\nCảnh báo: Đây là tài khoản quản trị viên!';
                }
                
                // Show confirmation dialog
                if (confirm(confirmMessage)) {
                    // Disable button and show loading
                    $('#deleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xóa...');
                    
                    // Submit form
                    this.submit();
                }
            });
        });
    </script>
}
