@model ReviewTripViewModel
@{
    ViewData["Title"] = "Đánh giá chuyến đi";
}

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-warning text-dark text-center py-4">
                    <h2 class="mb-0">
                        <i class="bi bi-star-fill me-2"></i>Đ<PERSON>h giá chuyến đi
                    </h2>
                    <p class="mb-0 mt-2">Chia sẻ trải nghiệm của bạn để giúp chúng tôi cải thiện dịch vụ</p>
                </div>

                <div class="card-body p-4">
                    <!-- Thông tin chuyến đi -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h5 class="card-title text-primary">
                                <i class="bi bi-info-circle me-2"></i>Thông tin chuyến đi
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <strong>Mã vé:</strong> @Model.Ve.MaVe
                                    </div>
                                    <div class="mb-2">
                                        <strong>Tuyến:</strong> @Model.Ve.ChuyenXe?.DiemDi → @Model.Ve.ChuyenXe?.DiemDen
                                    </div>
                                    <div class="mb-2">
                                        <strong>Thời gian:</strong> @Model.Ve.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <strong>Nhà xe:</strong> @Model.Ve.ChuyenXe?.Xe?.NhaXe
                                    </div>
                                    <div class="mb-2">
                                        <strong>Loại xe:</strong> @Model.Ve.ChuyenXe?.Xe?.LoaiXe
                                    </div>
                                    <div class="mb-2">
                                        <strong>Ghế số:</strong> @Model.Ve.ChoNgoi?.SoGhe
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form đánh giá -->
                    <form asp-action="Create" method="post">
                        <input asp-for="VeId" type="hidden" />
                        
                        <!-- Đánh giá tổng thể -->
                        <div class="mb-4">
                            <label asp-for="DiemDanhGia" class="form-label fw-bold">
                                <i class="bi bi-star me-2"></i>Đánh giá tổng thể *
                            </label>
                            <div class="rating-container mb-2">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <input type="radio" asp-for="DiemDanhGia" value="@i" id="rating@(i)" class="d-none" />
                                    <label for="rating@(i)" class="rating-star" data-rating="@i">
                                        <i class="bi bi-star-fill"></i>
                                    </label>
                                }
                            </div>
                            <div class="rating-text text-muted"></div>
                            <span asp-validation-for="DiemDanhGia" class="text-danger"></span>
                        </div>

                        <!-- Đánh giá chi tiết -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label asp-for="DanhGiaTaiXe" class="form-label fw-semibold">
                                    <i class="bi bi-person-check me-2"></i>Tài xế
                                </label>
                                <div class="rating-container-detail" data-field="DanhGiaTaiXe">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <input type="radio" asp-for="DanhGiaTaiXe" value="@i" id="driver@(i)" class="d-none" />
                                        <label for="driver@(i)" class="rating-star-small" data-rating="@i">
                                            <i class="bi bi-star-fill"></i>
                                        </label>
                                    }
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="DanhGiaXe" class="form-label fw-semibold">
                                    <i class="bi bi-bus-front me-2"></i>Xe
                                </label>
                                <div class="rating-container-detail" data-field="DanhGiaXe">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <input type="radio" asp-for="DanhGiaXe" value="@i" id="vehicle@(i)" class="d-none" />
                                        <label for="vehicle@(i)" class="rating-star-small" data-rating="@i">
                                            <i class="bi bi-star-fill"></i>
                                        </label>
                                    }
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="DanhGiaDichVu" class="form-label fw-semibold">
                                    <i class="bi bi-headset me-2"></i>Dịch vụ
                                </label>
                                <div class="rating-container-detail" data-field="DanhGiaDichVu">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <input type="radio" asp-for="DanhGiaDichVu" value="@i" id="service@(i)" class="d-none" />
                                        <label for="service@(i)" class="rating-star-small" data-rating="@i">
                                            <i class="bi bi-star-fill"></i>
                                        </label>
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- Nội dung đánh giá -->
                        <div class="mb-4">
                            <label asp-for="NoiDungDanhGia" class="form-label fw-bold">
                                <i class="bi bi-chat-text me-2"></i>Chia sẻ trải nghiệm của bạn
                            </label>
                            <textarea asp-for="NoiDungDanhGia" class="form-control" rows="5" 
                                      placeholder="Hãy chia sẻ chi tiết về trải nghiệm của bạn: chất lượng xe, thái độ tài xế, dịch vụ, điều gì bạn thích và không thích..."></textarea>
                            <div class="form-text">Tối đa 1000 ký tự</div>
                            <span asp-validation-for="NoiDungDanhGia" class="text-danger"></span>
                        </div>

                        <!-- Khuyến nghị -->
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input asp-for="CoKhuyenNghi" class="form-check-input" type="checkbox" checked />
                                <label asp-for="CoKhuyenNghi" class="form-check-label fw-semibold">
                                    <i class="bi bi-hand-thumbs-up me-2"></i>Tôi sẽ khuyến nghị chuyến đi này cho người khác
                                </label>
                            </div>
                        </div>

                        <!-- Buttons -->
                        <div class="d-flex justify-content-between">
                            <a asp-controller="MyTickets" asp-action="Details" asp-route-id="@Model.VeId" 
                               class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg px-4">
                                <i class="bi bi-send me-2"></i>Gửi đánh giá
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            const ratingTexts = {
                1: "Rất không hài lòng",
                2: "Không hài lòng", 
                3: "Bình thường",
                4: "Hài lòng",
                5: "Rất hài lòng"
            };

            // Handle main rating
            $('.rating-container .rating-star').hover(function() {
                const rating = $(this).data('rating');
                highlightStars('.rating-container', rating);
                $('.rating-text').text(ratingTexts[rating]);
            }, function() {
                const selectedRating = $('input[name="DiemDanhGia"]:checked').val();
                if (selectedRating) {
                    highlightStars('.rating-container', selectedRating);
                    $('.rating-text').text(ratingTexts[selectedRating]);
                } else {
                    resetStars('.rating-container');
                    $('.rating-text').text('');
                }
            });

            $('.rating-container .rating-star').click(function() {
                const rating = $(this).data('rating');
                $(`#rating${rating}`).prop('checked', true);
                highlightStars('.rating-container', rating);
                $('.rating-text').text(ratingTexts[rating]);
            });

            // Handle detail ratings
            $('.rating-container-detail').each(function() {
                const container = this;
                const field = $(container).data('field');
                
                $(container).find('.rating-star-small').hover(function() {
                    const rating = $(this).data('rating');
                    highlightStars(container, rating);
                }, function() {
                    const selectedRating = $(`input[name="${field}"]:checked`).val();
                    if (selectedRating) {
                        highlightStars(container, selectedRating);
                    } else {
                        resetStars(container);
                    }
                });

                $(container).find('.rating-star-small').click(function() {
                    const rating = $(this).data('rating');
                    const inputId = $(this).attr('for');
                    $(`#${inputId}`).prop('checked', true);
                    highlightStars(container, rating);
                });
            });

            function highlightStars(container, rating) {
                $(container).find('.rating-star, .rating-star-small').each(function(index) {
                    if (index < rating) {
                        $(this).addClass('active');
                    } else {
                        $(this).removeClass('active');
                    }
                });
            }

            function resetStars(container) {
                $(container).find('.rating-star, .rating-star-small').removeClass('active');
            }

            // Initialize with existing values
            const initialRating = $('input[name="DiemDanhGia"]:checked').val();
            if (initialRating) {
                highlightStars('.rating-container', initialRating);
                $('.rating-text').text(ratingTexts[initialRating]);
            }

            // Initialize detail ratings
            ['DanhGiaTaiXe', 'DanhGiaXe', 'DanhGiaDichVu'].forEach(field => {
                const value = $(`input[name="${field}"]:checked`).val();
                if (value) {
                    highlightStars(`.rating-container-detail[data-field="${field}"]`, value);
                }
            });
        });
    </script>
}

<style>
    .rating-star, .rating-star-small {
        font-size: 2rem;
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s ease;
        margin-right: 0.25rem;
    }
    
    .rating-star-small {
        font-size: 1.5rem;
    }
    
    .rating-star:hover, .rating-star.active,
    .rating-star-small:hover, .rating-star-small.active {
        color: #ffc107;
    }
    
    .rating-container, .rating-container-detail {
        margin-bottom: 0.5rem;
    }
    
    .rating-text {
        font-size: 1.1rem;
        font-weight: 600;
        min-height: 1.5rem;
    }
    
    .card {
        border-radius: 15px;
    }
    
    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    }
    
    .btn-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #000;
        font-weight: 600;
    }
    
    .btn-warning:hover {
        background-color: #ffca2c;
        border-color: #ffc720;
        color: #000;
    }
</style>
