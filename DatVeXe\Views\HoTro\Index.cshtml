@{
    ViewData["Title"] = "Hỗ trợ khách hàng";
}

<div class="container py-4">
    <!-- Hero Section -->
    <div class="hero-section text-center mb-5">
        <h1 class="display-4 fw-bold text-white mb-3">Hỗ Trợ Khách Hàng</h1>
        <p class="lead text-white-50">Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7</p>
    </div>

    <!-- Liên hệ nhanh -->
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card contact-card h-100 border-0 shadow text-center">
                <div class="card-body p-4">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-phone-alt fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">Hotline</h5>
                    <p class="card-text text-muted">Gọi ngay để được hỗ trợ</p>
                    <div class="contact-info">
                        <div class="fw-bold text-primary fs-5">1900 1234</div>
                        <small class="text-muted">24/7 - Miễn phí</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card contact-card h-100 border-0 shadow text-center">
                <div class="card-body p-4">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-envelope fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">Email</h5>
                    <p class="card-text text-muted">Gửi email cho chúng tôi</p>
                    <div class="contact-info">
                        <div class="fw-bold text-success"><EMAIL></div>
                        <small class="text-muted">Phản hồi trong 2 giờ</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card contact-card h-100 border-0 shadow text-center">
                <div class="card-body p-4">
                    <div class="contact-icon mb-3">
                        <i class="fab fa-facebook-messenger fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">Live Chat</h5>
                    <p class="card-text text-muted">Chat trực tiếp với nhân viên</p>
                    <div class="contact-info">
                        <button class="btn btn-info btn-sm">
                            <i class="fas fa-comments me-1"></i>Bắt đầu chat
                        </button>
                        <small class="text-muted d-block mt-1">Trực tuyến</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ -->
    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>Câu hỏi thường gặp
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    Làm thế nào để đặt vé xe?
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>Để đặt vé xe, bạn có thể:</p>
                                    <ol>
                                        <li>Truy cập trang web và chọn "Chuyến xe"</li>
                                        <li>Chọn điểm đi, điểm đến và ngày khởi hành</li>
                                        <li>Chọn chuyến xe phù hợp</li>
                                        <li>Điền thông tin khách hàng và thanh toán</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    Tôi có thể hủy vé không?
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>Bạn có thể hủy vé với các điều kiện sau:</p>
                                    <ul>
                                        <li>Hủy vé trước 2 giờ khởi hành</li>
                                        <li>Phí hủy vé: 10% giá trị vé</li>
                                        <li>Liên hệ hotline hoặc hủy trực tuyến</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    Các hình thức thanh toán được chấp nhận?
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>Chúng tôi chấp nhận các hình thức thanh toán:</p>
                                    <ul>
                                        <li>Thẻ ATM/Visa/Mastercard</li>
                                        <li>Ví điện tử: MoMo, ZaloPay, VNPay</li>
                                        <li>Chuyển khoản ngân hàng</li>
                                        <li>Thanh toán tại quầy</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                    Tôi có thể thay đổi thông tin vé không?
                                </button>
                            </h2>
                            <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>Bạn có thể thay đổi thông tin vé:</p>
                                    <ul>
                                        <li>Thay đổi tên khách hàng: Miễn phí</li>
                                        <li>Thay đổi ngày giờ: Phí 50,000 VNĐ</li>
                                        <li>Thay đổi tuyến đường: Tính theo chênh lệch giá vé</li>
                                        <li>Liên hệ hotline để được hỗ trợ</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Giờ làm việc
                    </h6>
                </div>
                <div class="card-body">
                    <div class="working-hours">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Thứ 2 - Thứ 6:</span>
                            <span class="fw-bold">8:00 - 18:00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Thứ 7:</span>
                            <span class="fw-bold">8:00 - 17:00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Chủ nhật:</span>
                            <span class="fw-bold">9:00 - 16:00</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <span>Hotline 24/7:</span>
                            <span class="fw-bold text-primary">1900 1234</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Văn phòng
                    </h6>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        <strong>Công ty TNHH Đặt Vé Xe</strong><br>
                        123 Đường ABC, Quận 1<br>
                        TP. Hồ Chí Minh<br>
                        <i class="fas fa-phone text-primary me-1"></i> (028) 1234 5678<br>
                        <i class="fas fa-envelope text-primary me-1"></i> <EMAIL>
                    </address>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 4rem 2rem;
            border-radius: 15px;
            margin-bottom: 3rem;
        }

        .contact-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 15px;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .contact-icon {
            background: #f8f9fa;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .accordion-button:not(.collapsed) {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .working-hours {
            font-size: 0.9rem;
        }

        address {
            font-style: normal;
            line-height: 1.6;
        }
    </style>
}
