@{
    ViewData["Title"] = "Thêm dữ liệu mẫu";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-database-add me-2"></i>
                        Thêm dữ liệu mẫu
                    </h4>
                </div>
                <div class="card-body">
                    @if (ViewBag.Message != null)
                    {
                        <div class="alert <EMAIL> alert-dismissible fade show" role="alert">
                            @ViewBag.Message
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="mb-4">
                        <h5>Dữ liệu mẫu bao gồm:</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="bi bi-bus-front text-primary me-2"></i>
                                <strong>4 xe khách</strong> v<PERSON><PERSON> <PERSON><PERSON><PERSON> (Giường nằm, Limousine, <PERSON><PERSON><PERSON>, VIP)
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-geo-alt text-success me-2"></i>
                                <strong>3 tuyến đường</strong> phổ biến (TP.HCM - Nha Trang, TP.HCM - Đà Lạt, TP.HCM - Vũng Tàu)
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-people text-info me-2"></i>
                                <strong>3 người dùng</strong> (1 admin và 2 user thường)
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-calendar-event text-warning me-2"></i>
                                <strong>3 chuyến xe</strong> với thời gian khởi hành khác nhau
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-grid-3x3 text-secondary me-2"></i>
                                <strong>Sơ đồ chỗ ngồi</strong> cho tất cả các xe
                            </li>
                        </ul>
                    </div>

                    @if (ViewBag.MessageType != "success")
                    {
                        <div class="text-center">
                            <a href="@Url.Action("Index", "SeedData")" class="btn btn-primary btn-lg">
                                <i class="bi bi-database-add me-2"></i>
                                Thêm dữ liệu mẫu
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center">
                            <a href="@Url.Action("Index", "Home")" class="btn btn-success btn-lg me-3">
                                <i class="bi bi-house me-2"></i>
                                Về trang chủ
                            </a>
                            <a href="@Url.Action("Index", "ChuyenXe")" class="btn btn-outline-primary btn-lg me-3">
                                <i class="bi bi-bus-front me-2"></i>
                                Xem chuyến xe
                            </a>
                            <form method="post" action="@Url.Action("AddMoreTrips")" class="d-inline me-2">
                                <button type="submit" class="btn btn-warning btn-lg">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    Thêm chuyến xe
                                </button>
                            </form>
                            <form method="post" action="@Url.Action("CreateSeatsForAllVehicles")" class="d-inline me-2">
                                <button type="submit" class="btn btn-info btn-lg">
                                    <i class="bi bi-grid-3x3-gap me-2"></i>
                                    Tạo sơ đồ ghế
                                </button>
                            </form>
                            <a href="@Url.Action("DebugSeats")" class="btn btn-secondary btn-lg me-2">
                                <i class="bi bi-bug me-2"></i>
                                Debug ghế
                            </a>
                            <a href="@Url.Action("CheckVehicleData")" class="btn btn-dark btn-lg">
                                <i class="bi bi-car-front me-2"></i>
                                Kiểm tra xe
                            </a>
                        </div>
                    }
                </div>
            </div>

            @if (ViewBag.MessageType == "success")
            {
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            Thông tin đăng nhập
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Tài khoản Admin:</h6>
                                <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                                <p class="mb-3"><strong>Mật khẩu:</strong> admin123</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">Tài khoản User:</h6>
                                <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
                                <p class="mb-0"><strong>Mật khẩu:</strong> 123456</p>
                            </div>
                        </div>
                    </div>
                </div>
            }

            @if (ViewBag.DebugInfo != null)
            {
                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-bug me-2"></i>
                            Debug Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">@string.Join("\n", (List<string>)ViewBag.DebugInfo)</pre>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }
    
    .list-group-item {
        border: none;
        padding: 0.75rem 0;
    }
    
    .btn-lg {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 8px;
    }
</style>
