@{
    ViewData["Title"] = "Thống kê tuyến đường";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold text-primary mb-0">
            <i class="bi bi-graph-up me-2"></i>@ViewData["Title"]
        </h2>
        <div class="d-flex gap-2">
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
            <a href="@Url.Action("Index", "BaoCao")" class="btn btn-info">
                <i class="bi bi-file-earmark-text me-1"></i>Báo cáo chi tiết
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary text-center">
                <div class="card-body">
                    <i class="bi bi-signpost-2 text-primary" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@ViewBag.TongTuyenDuong</h4>
                    <p class="text-muted">Tổng tuyến đường</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success text-center">
                <div class="card-body">
                    <i class="bi bi-check-circle text-success" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@ViewBag.TuyenHoatDong</h4>
                    <p class="text-muted">Đang hoạt động</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning text-center">
                <div class="card-body">
                    <i class="bi bi-percent text-warning" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@(ViewBag.TongTuyenDuong > 0 ? Math.Round((double)ViewBag.TuyenHoatDong / ViewBag.TongTuyenDuong * 100, 1) : 0)%</h4>
                    <p class="text-muted">Tỷ lệ hoạt động</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info text-center">
                <div class="card-body">
                    <i class="bi bi-calendar-month text-info" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@DateTime.Now.ToString("MM/yyyy")</h4>
                    <p class="text-muted">Tháng hiện tại</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Routes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-trophy me-2"></i>Top 10 tuyến đường phổ biến
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.TopTuyenDuong != null && ((List<dynamic>)ViewBag.TopTuyenDuong).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Hạng</th>
                                        <th>Tuyến đường</th>
                                        <th>Số vé</th>
                                        <th>Doanh thu</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{int rank = 1;}
                                    @foreach (var tuyen in (List<dynamic>)ViewBag.TopTuyenDuong)
                                    {
                                        <tr>
                                            <td>
                                                @if (rank <= 3)
                                                {
                                                    <span class="badge bg-@(rank == 1 ? "warning" : rank == 2 ? "secondary" : "dark")">
                                                        @rank
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-light text-dark">@rank</span>
                                                }
                                            </td>
                                            <td>
                                                <strong>@tuyen.TuyenDuong</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@tuyen.TongVe</span>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">@tuyen.DoanhThu.ToString("N0") VNĐ</span>
                                            </td>
                                        </tr>
                                        rank++;
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">Chưa có dữ liệu</h5>
                            <p class="text-muted">Chưa có tuyến đường nào có vé được bán.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Monthly Stats -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-month me-2"></i>Thống kê tháng này
                    </h5>
                </div>
                <div class="card-body">
                    @if (ViewBag.ThongKeThang != null && ((List<dynamic>)ViewBag.ThongKeThang).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tuyến đường</th>
                                        <th>Chuyến xe</th>
                                        <th>Vé bán</th>
                                        <th>Doanh thu</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var tuyen in (List<dynamic>)ViewBag.ThongKeThang)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@tuyen.TuyenDuong</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@tuyen.SoChuyenXe</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@tuyen.TongVe</span>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">@tuyen.DoanhThu.ToString("N0") VNĐ</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3 text-muted">Chưa có dữ liệu tháng này</h5>
                            <p class="text-muted">Chưa có tuyến đường nào hoạt động trong tháng này.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart me-2"></i>Biểu đồ doanh thu theo tuyến đường
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="routeRevenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Route Revenue Chart
const ctx = document.getElementById('routeRevenueChart').getContext('2d');
const topRoutes = @Html.Raw(Json.Serialize(ViewBag.TopTuyenDuong ?? new List<dynamic>()));

new Chart(ctx, {
    type: 'bar',
    data: {
        labels: topRoutes.map(r => r.TuyenDuong),
        datasets: [{
            label: 'Doanh thu (VNĐ)',
            data: topRoutes.map(r => r.DoanhThu),
            backgroundColor: [
                '#6CABDD',
                '#4A90C2',
                '#357ABD',
                '#2E6BA8',
                '#275C93',
                '#1F4D7E',
                '#183E69',
                '#112F54',
                '#0A203F',
                '#03112A'
            ],
            borderColor: '#fff',
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' VNĐ';
                    }
                }
            },
            x: {
                ticks: {
                    maxRotation: 45,
                    minRotation: 45
                }
            }
        }
    }
});
</script>

<style>
.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.8em;
}

#routeRevenueChart {
    height: 400px !important;
}
</style>
