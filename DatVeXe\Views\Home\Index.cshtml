@{
    ViewData["Title"] = "Trang chủ đặt vé xe";
}



<!-- Hero Banner -->
<div class="city-hero position-relative">
    <div class="city-hero-slider">
        <div class="city-hero-slide">
            <img src="/images/BusBanner.jpg" alt="Banner xe khách" class="w-100 city-hero-img">
        </div>
    </div>
    <div class="city-hero-overlay position-absolute top-0 start-0 w-100 h-100 d-flex flex-column justify-content-center">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-8">
                    <div class="city-hero-content">
                        <h1 class="city-hero-title mb-4">Đặt vé xe trực tuyến<br><span class="text-primary">Dễ dàng & Nhanh chóng</span></h1>
                        <p class="city-hero-subtitle mb-4">Hệ thống đặt vé xe khách hiện đại nhất Việt Nam</p>
                        <div class="d-flex gap-3">
                            <a href="/ChuyenXe/Search" class="btn btn-primary btn-lg city-btn">Tìm chuyến xe</a>
                            @if (Context.Session.GetInt32("UserId") != null)
                            {
                                <a href="/TaiKhoan/Dashboard" class="btn btn-outline-light btn-lg city-btn">Dashboard</a>
                                <a href="/ChuyenXe/Search" class="btn btn-outline-light btn-lg city-btn">Đặt vé ngay</a>
                            }
                            else
                            {
                                <a href="/TaiKhoan/Auth" class="btn btn-outline-light btn-lg city-btn">Đăng nhập</a>
                                <a href="/KhuyenMai" class="btn btn-outline-light btn-lg">Xem khuyến mãi</a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Box -->
<div class="city-search-container">
    <div class="container">
        <div class="city-search-box">
            <form action="/ChuyenXe/Search" method="get">
                <div class="row g-0">
                <div class="col-md-4">
                    <div class="city-search-item">
                        <div class="city-search-icon">
                            <i class="bi bi-geo-alt-fill"></i>
                        </div>
                        <div class="city-search-input">
                            <label>Điểm đi</label>
                            <select class="form-select border-0 shadow-none" name="DiemDi">
                                <option value="">Chọn điểm đi</option>
                                @if (ViewBag.DiemDiList != null)
                                {
                                    foreach (var diemDi in ViewBag.DiemDiList)
                                    {
                                        <option value="@diemDi">@diemDi</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="city-search-item">
                        <div class="city-search-icon">
                            <i class="bi bi-geo-alt-fill"></i>
                        </div>
                        <div class="city-search-input">
                            <label>Điểm đến</label>
                            <select class="form-select border-0 shadow-none" name="DiemDen">
                                <option value="">Chọn điểm đến</option>
                                @if (ViewBag.DiemDenList != null)
                                {
                                    foreach (var diemDen in ViewBag.DiemDenList)
                                    {
                                        <option value="@diemDen">@diemDen</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="city-search-item">
                        <div class="city-search-icon">
                            <i class="bi bi-calendar-event"></i>
                        </div>
                        <div class="city-search-input">
                            <label>Ngày đi</label>
                            <input type="date" class="form-control border-0 shadow-none" name="NgayKhoiHanh" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                        </div>
                    </div>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="city-search-btn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- Latest News Section -->
<div class="city-news-section">
    <div class="container">
        <div class="city-section-header">
            <h2 class="city-section-title">Tin tức & Khuyến mãi</h2>
            <a href="#" class="city-view-all">Xem tất cả <i class="bi bi-arrow-right"></i></a>
        </div>

        <div class="row g-4">
            <div class="col-lg-8">
                <div class="city-news-main">
                    <div class="city-news-img-container">
                        <img src="/images/mancitybus.jpg" alt="Tin tức chính" class="city-news-img">
                    </div>
                    <div class="city-news-content">
                        <div class="city-news-category">Khuyến mãi</div>
                        <h3 class="city-news-title">Giảm 25% cho tất cả các chuyến xe trong tháng 6</h3>
                        <p class="city-news-excerpt">Chương trình khuyến mãi đặc biệt dành cho khách hàng đặt vé trực tuyến trong tháng 6/2025. Áp dụng cho tất cả các tuyến đường.</p>
                        <a href="#" class="city-news-link">Xem chi tiết <i class="bi bi-arrow-right"></i></a>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="city-news-secondary">
                    <div class="city-news-secondary-item">
                        <div class="city-news-secondary-img">
                            <img src="/images/readmarid.jpg" alt="Tin tức phụ" class="w-100">
                        </div>
                        <div class="city-news-secondary-content">
                            <div class="city-news-category">Tin tức</div>
                            <h4 class="city-news-secondary-title">Mở thêm tuyến xe mới Hà Nội - Quảng Ninh</h4>
                            <a href="#" class="city-news-link">Xem chi tiết <i class="bi bi-arrow-right"></i></a>
                        </div>
                    </div>

                    <div class="city-news-secondary-item">
                        <div class="city-news-secondary-img">
                            <img src="/images/mancitybus.jpg" alt="Tin tức phụ" class="w-100">
                        </div>
                        <div class="city-news-secondary-content">
                            <div class="city-news-category">Cập nhật</div>
                            <h4 class="city-news-secondary-title">Nâng cấp đội xe với 20 xe giường nằm cao cấp</h4>
                            <a href="#" class="city-news-link">Xem chi tiết <i class="bi bi-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="city-features-section">
    <div class="container">
        <div class="city-section-header text-center">
            <h2 class="city-section-title">Dịch vụ của chúng tôi</h2>
            <p class="city-section-subtitle">Hệ thống đặt vé xe hiện đại với đầy đủ tính năng quản lý, đặt vé và theo dõi chuyến đi</p>
        </div>

        <div class="row g-4">
            <div class="col-md-3">
                <div class="city-feature-card">
                    <div class="city-feature-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <h3 class="city-feature-title">Tìm kiếm dễ dàng</h3>
                    <p class="city-feature-text">Tìm kiếm chuyến xe theo điểm đi, điểm đến và thời gian một cách nhanh chóng.</p>
                    <a href="/ChuyenXe" class="city-feature-link">Tìm chuyến xe <i class="bi bi-arrow-right"></i></a>
                </div>
            </div>

            <div class="col-md-3">
                <div class="city-feature-card">
                    <div class="city-feature-icon">
                        <i class="bi bi-ticket-perforated"></i>
                    </div>
                    <h3 class="city-feature-title">Đặt vé trực tuyến</h3>
                    <p class="city-feature-text">Đặt vé xe khách trực tuyến 24/7 với chọn chỗ ngồi, thanh toán an toàn và nhận vé ngay lập tức.</p>
                    <a href="/ChuyenXe/Search" class="city-feature-link">Đặt vé ngay <i class="bi bi-arrow-right"></i></a>
                </div>
            </div>

            <div class="col-md-3">
                <div class="city-feature-card">
                    <div class="city-feature-icon">
                        <i class="bi bi-envelope-check"></i>
                    </div>
                    <h3 class="city-feature-title">Xác nhận email</h3>
                    <p class="city-feature-text">Nhận email xác nhận đặt vé với đầy đủ thông tin chuyến đi và hướng dẫn.</p>
                    <a href="/EmailTest" class="city-feature-link">Xem demo email <i class="bi bi-arrow-right"></i></a>
                </div>
            </div>

            <div class="col-md-3">
                <div class="city-feature-card">
                    <div class="city-feature-icon">
                        <i class="bi bi-person-gear"></i>
                    </div>
                    <h3 class="city-feature-title">Quản lý cá nhân</h3>
                    <p class="city-feature-text">Dashboard cá nhân, lịch sử đặt vé, cập nhật thông tin và quản lý tài khoản.</p>
                    @if (Context.Session.GetInt32("UserId") != null)
                    {
                        <a href="/TaiKhoan/Dashboard" class="city-feature-link">Vào Dashboard <i class="bi bi-arrow-right"></i></a>
                    }
                    else
                    {
                        <a href="/TaiKhoan/Auth" class="city-feature-link">Đăng nhập <i class="bi bi-arrow-right"></i></a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Popular Routes Section -->
<div class="city-routes-section">
    <div class="container">
        <div class="city-section-header text-center">
            <h2 class="city-section-title">Tuyến đường phổ biến</h2>
            <p class="city-section-subtitle">Các tuyến đường được đặt vé nhiều nhất</p>
        </div>

        <div class="city-routes-grid">
            @if (ViewBag.PopularRoutes != null && ViewBag.PopularRoutes.Count > 0)
            {
                foreach (var route in ViewBag.PopularRoutes)
                {
                    <div class="city-route-card">
                        <div class="city-route-img">
                            <img src="/images/mancitybus.jpg" alt="@route.DiemDiDisplay - @route.DiemDenDisplay">
                        </div>
                        <div class="city-route-content">
                            <div class="city-route-cities">
                                <span class="city-route-from">@route.DiemDiDisplay</span>
                                <i class="bi bi-arrow-right mx-2"></i>
                                <span class="city-route-to">@route.DiemDenDisplay</span>
                            </div>
                            <div class="city-route-info">
                                <div class="city-route-price">@string.Format("{0:N0}đ", route.Gia)</div>
                                <div class="city-route-time">@route.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</div>
                            </div>
                            <a href="/ChuyenXe/Details/@route.ChuyenXeId" class="city-route-btn">Xem chi tiết</a>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Fallback if no routes are available -->
                <div class="city-route-card">
                    <div class="city-route-img">
                        <img src="/images/BusBanner.jpg" alt="Hà Nội - Sài Gòn">
                    </div>
                    <div class="city-route-content">
                        <div class="city-route-cities">
                            <span class="city-route-from">Hà Nội</span>
                            <i class="bi bi-arrow-right mx-2"></i>
                            <span class="city-route-to">TP. Hồ Chí Minh</span>
                        </div>
                        <div class="city-route-info">
                            <div class="city-route-price">1.200.000đ</div>
                            <div class="city-route-time">36 giờ</div>
                        </div>
                        <a href="/ChuyenXe" class="city-route-btn">Xem chuyến xe</a>
                    </div>
                </div>

                <div class="city-route-card">
                    <div class="city-route-img">
                        <img src="/images/BusBanner.jpg" alt="Hà Nội - Đà Nẵng">
                    </div>
                    <div class="city-route-content">
                        <div class="city-route-cities">
                            <span class="city-route-from">Hà Nội</span>
                            <i class="bi bi-arrow-right mx-2"></i>
                            <span class="city-route-to">Đà Nẵng</span>
                        </div>
                        <div class="city-route-info">
                            <div class="city-route-price">800.000đ</div>
                            <div class="city-route-time">16 giờ</div>
                        </div>
                        <a href="/ChuyenXe" class="city-route-btn">Xem chuyến xe</a>
                    </div>
                </div>

                <div class="city-route-card">
                    <div class="city-route-img">
                        <img src="/images/BusBanner.jpg" alt="TP. Hồ Chí Minh - Đà Lạt">
                    </div>
                    <div class="city-route-content">
                        <div class="city-route-cities">
                            <span class="city-route-from">TP. Hồ Chí Minh</span>
                            <i class="bi bi-arrow-right mx-2"></i>
                            <span class="city-route-to">Đà Lạt</span>
                        </div>
                        <div class="city-route-info">
                            <div class="city-route-price">300.000đ</div>
                            <div class="city-route-time">8 giờ</div>
                        </div>
                        <a href="/ChuyenXe" class="city-route-btn">Xem chuyến xe</a>
                    </div>
                </div>

                <div class="city-route-card">
                    <div class="city-route-img">
                        <img src="/images/BusBanner.jpg" alt="TP. Hồ Chí Minh - Vũng Tàu">
                    </div>
                    <div class="city-route-content">
                        <div class="city-route-cities">
                            <span class="city-route-from">TP. Hồ Chí Minh</span>
                            <i class="bi bi-arrow-right mx-2"></i>
                            <span class="city-route-to">Vũng Tàu</span>
                        </div>
                        <div class="city-route-info">
                            <div class="city-route-price">150.000đ</div>
                            <div class="city-route-time">3 giờ</div>
                        </div>
                        <a href="/ChuyenXe" class="city-route-btn">Xem chuyến xe</a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="city-stats-section">
    <div class="container">
        <div class="city-stats-grid">
            <div class="city-stat-card">
                <div class="city-stat-icon">
                    <i class="bi bi-people-fill"></i>
                </div>
                <div class="city-stat-number">@(ViewBag.TotalUsers > 0 ? string.Format("{0:N0}+", ViewBag.TotalUsers) : "100,000+")</div>
                <div class="city-stat-label">Khách hàng</div>
            </div>

            <div class="city-stat-card">
                <div class="city-stat-icon">
                    <i class="bi bi-bus-front-fill"></i>
                </div>
                <div class="city-stat-number">@(ViewBag.TotalBuses > 0 ? string.Format("{0:N0}+", ViewBag.TotalBuses) : "500+")</div>
                <div class="city-stat-label">Xe khách</div>
            </div>

            <div class="city-stat-card">
                <div class="city-stat-icon">
                    <i class="bi bi-geo-alt-fill"></i>
                </div>
                <div class="city-stat-number">@(ViewBag.TotalRoutes > 0 ? string.Format("{0:N0}+", ViewBag.TotalRoutes) : "50+")</div>
                <div class="city-stat-label">Tuyến đường</div>
            </div>

            <div class="city-stat-card">
                <div class="city-stat-icon">
                    <i class="bi bi-ticket-perforated-fill"></i>
                </div>
                <div class="city-stat-number">@(ViewBag.TotalTickets > 0 ? string.Format("{0:N0}+", ViewBag.TotalTickets) : "1,000,000+")</div>
                <div class="city-stat-label">Vé đã bán</div>
            </div>
        </div>
    </div>
</div>

<!-- App Download Section -->
<div class="city-app-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="city-app-content">
                    <h2 class="city-app-title">Tải ứng dụng Đặt Vé Xe</h2>
                    <p class="city-app-text">Đặt vé xe dễ dàng, nhanh chóng và tiện lợi hơn với ứng dụng di động của chúng tôi. Tải xuống ngay hôm nay!</p>
                    <div class="city-app-buttons">
                        <a href="#" class="city-app-btn">
                            <i class="bi bi-apple me-2"></i> App Store
                        </a>
                        <a href="#" class="city-app-btn">
                            <i class="bi bi-google-play me-2"></i> Google Play
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="city-app-image">
                    <img src="/images/BusBanner.jpg" alt="Ứng dụng đặt vé xe" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
}

<style>
    .city-search-btn {
        background: none;
        border: none;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        background: linear-gradient(135deg, #6cabdd 0%, #4a90e2 100%);
        border-radius: 0 12px 12px 0;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .city-search-btn:hover {
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        transform: scale(1.05);
        color: white;
    }
</style>


