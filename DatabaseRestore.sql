-- Script khôi phục database DatVeXeDb
-- <PERSON><PERSON> dụng script này để khôi phục database từ file backup

USE master;
GO

-- <PERSON><PERSON><PERSON> tất cả kết nối đến database
ALTER DATABASE [DatVeXeDb] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
GO

-- Xóa database cũ (nếu tồn tại)
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'DatVeXeDb')
BEGIN
    DROP DATABASE [DatVeXeDb];
    PRINT 'Đã xóa database cũ';
END
GO

-- Khôi phục database từ backup
-- Thay đổi đường dẫn file backup phù hợp
DECLARE @BackupPath NVARCHAR(500);
SET @BackupPath = 'C:\Backup\DatVeXeDb_Backup_YYYYMMDD_HHMMSS.bak'; -- Thay đổi tên file backup

RESTORE DATABASE [DatVeXeDb] 
FROM DISK = @BackupPath
WITH 
    REPLACE,
    STATS = 10;

PRINT 'Khôi phục database thành công!';

-- Đặt lại chế độ multi-user
ALTER DATABASE [DatVeXeDb] SET MULTI_USER;
GO

-- Kiểm tra database
USE [DatVeXeDb];
GO

SELECT 
    'NguoiDungs' as TableName, COUNT(*) as RecordCount FROM NguoiDungs
UNION ALL
SELECT 'TuyenDuongs', COUNT(*) FROM TuyenDuongs
UNION ALL
SELECT 'Xes', COUNT(*) FROM Xes
UNION ALL
SELECT 'ChoNgois', COUNT(*) FROM ChoNgois
UNION ALL
SELECT 'ChuyenXes', COUNT(*) FROM ChuyenXes
UNION ALL
SELECT 'Ves', COUNT(*) FROM Ves;

PRINT 'Kiểm tra database hoàn tất!';
