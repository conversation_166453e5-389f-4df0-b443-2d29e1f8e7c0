﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class UpdateThanhToanModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "<PERSON>uong<PERSON>hu<PERSON>",
                table: "ThanhToans",
                newName: "PhuongThucThanhToan");

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayThanhToan",
                table: "ThanhToans",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "VeId1",
                table: "ThanhToans",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ThanhToans_VeId1",
                table: "ThanhToans",
                column: "VeId1");

            migrationBuilder.AddForeignKey(
                name: "FK_ThanhToans_Ves_VeId1",
                table: "ThanhToans",
                column: "VeId1",
                principalTable: "Ves",
                principalColumn: "VeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ThanhToans_Ves_VeId1",
                table: "ThanhToans");

            migrationBuilder.DropIndex(
                name: "IX_ThanhToans_VeId1",
                table: "ThanhToans");

            migrationBuilder.DropColumn(
                name: "NgayThanhToan",
                table: "ThanhToans");

            migrationBuilder.DropColumn(
                name: "VeId1",
                table: "ThanhToans");

            migrationBuilder.RenameColumn(
                name: "PhuongThucThanhToan",
                table: "ThanhToans",
                newName: "PhuongThuc");
        }
    }
}
