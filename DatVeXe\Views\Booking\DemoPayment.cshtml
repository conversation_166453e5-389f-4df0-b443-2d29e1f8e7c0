@{
    ViewData["Title"] = "Demo Payment Gateway";
    var returnUrl = ViewBag.ReturnUrl as string ?? "";
    var cancelUrl = ViewBag.CancelUrl as string ?? "";
    var amount = ViewBag.Amount as decimal? ?? 0;
    var orderInfo = ViewBag.OrderInfo as string ?? "";
    var transactionId = ViewBag.TransactionId as string ?? "";
    var paymentMethod = ViewBag.PaymentMethod as string ?? "";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="bi bi-credit-card me-2"></i>
                        Demo Payment Gateway
                    </h3>
                    <p class="mb-0 mt-2">@paymentMethod</p>
                </div>
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Chế độ Demo:</strong> <PERSON><PERSON><PERSON> là mô phỏng cổng thanh toán
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="payment-info mb-4">
                        <h5 class="mb-3">Thông tin thanh toán</h5>
                        <div class="row">
                            <div class="col-6">
                                <div class="info-item">
                                    <label class="text-muted">Mã giao dịch:</label>
                                    <div class="fw-bold">@transactionId</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="info-item">
                                    <label class="text-muted">Số tiền:</label>
                                    <div class="fw-bold text-success">@string.Format("{0:N0}", amount) VNĐ</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="text-muted">Nội dung:</label>
                            <div class="fw-bold">@orderInfo</div>
                        </div>
                    </div>

                    <!-- Demo Payment Options -->
                    <div class="demo-options mb-4">
                        <h5 class="mb-3">Chọn kết quả thanh toán (Demo)</h5>
                        <div class="d-grid gap-3">
                            <button type="button" class="btn btn-success btn-lg" onclick="processPayment(true)">
                                <i class="bi bi-check-circle me-2"></i>
                                Thanh toán thành công
                            </button>
                            <button type="button" class="btn btn-danger btn-lg" onclick="processPayment(false)">
                                <i class="bi bi-x-circle me-2"></i>
                                Thanh toán thất bại
                            </button>
                            <a href="@cancelUrl" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>
                                Hủy thanh toán
                            </a>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="loadingState" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Đang xử lý thanh toán...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function processPayment(success) {
        // Show loading state
        document.querySelector('.demo-options').style.display = 'none';
        document.getElementById('loadingState').style.display = 'block';

        // Simulate processing delay
        setTimeout(() => {
            if (success) {
                // Redirect to success callback
                const returnUrl = '@Html.Raw(returnUrl)';
                const successUrl = returnUrl +
                    (returnUrl.includes('?') ? '&' : '?') +
                    'status=success&transaction_id=@transactionId&amount=@amount&demo=true';
                window.location.href = successUrl;
            } else {
                // Redirect to failure callback
                const returnUrl = '@Html.Raw(returnUrl)';
                const failureUrl = returnUrl +
                    (returnUrl.includes('?') ? '&' : '?') +
                    'status=failed&transaction_id=@transactionId&error=' + encodeURIComponent('Payment failed') + '&demo=true';
                window.location.href = failureUrl;
            }
        }, 2000);
    }
</script>

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .info-item {
        margin-bottom: 1rem;
    }

    .info-item label {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        display: block;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .payment-info {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
    }
</style>
