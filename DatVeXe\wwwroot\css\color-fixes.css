/* ==========================================================================
   COLOR FIXES - Khắc phục vấn đề màu sắc và contrast
   ========================================================================== */

/* Override Bootstrap và các style xung đột */

/* 1. FIX GENERAL TEXT COLORS */
body {
    background: #0a2640 !important;
    color: #ffffff !important;
}

/* 2. FIX CARD BACKGROUNDS */
.card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.card-header {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-bottom: 1px solid #dee2e6 !important;
}

.card-body {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.card-footer {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-top: 1px solid #dee2e6 !important;
}

/* 3. FIX TABLE COLORS */
.table {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.table th {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-bottom: 2px solid #dee2e6 !important;
}

.table td {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-top: 1px solid #dee2e6 !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.table-hover tbody tr:hover {
    background-color: #e9ecef !important;
    color: #333333 !important;
}

/* 4. FIX FORM CONTROLS */
.form-control {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #ced4da !important;
}

.form-control:focus {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #6CABDD !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.25) !important;
}

.form-control::placeholder {
    color: #6c757d !important;
}

.form-select {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #ced4da !important;
}

.form-label {
    color: #ffffff !important;
    font-weight: 500 !important;
}

/* 5. FIX BUTTON COLORS */
.btn-primary {
    background-color: #6CABDD !important;
    border-color: #6CABDD !important;
    color: #ffffff !important;
}

.btn-primary:hover {
    background-color: #5A91BA !important;
    border-color: #5A91BA !important;
    color: #ffffff !important;
}

.btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
}

.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000000 !important;
}

.btn-info {
    background-color: #0dcaf0 !important;
    border-color: #0dcaf0 !important;
    color: #000000 !important;
}

.btn-light {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #333333 !important;
}

.btn-dark {
    background-color: #212529 !important;
    border-color: #212529 !important;
    color: #ffffff !important;
}

/* 6. FIX BADGE COLORS */
.badge.bg-primary {
    background-color: #6CABDD !important;
    color: #ffffff !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000000 !important;
}

.badge.bg-light {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.badge.bg-dark {
    background-color: #212529 !important;
    color: #ffffff !important;
}

/* 7. FIX ALERT COLORS */
.alert-primary {
    background-color: rgba(108, 171, 221, 0.1) !important;
    border-color: #6CABDD !important;
    color: #0a2640 !important;
}

.alert-secondary {
    background-color: rgba(108, 117, 125, 0.1) !important;
    border-color: #6c757d !important;
    color: #333333 !important;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    border-color: #198754 !important;
    color: #0f5132 !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: #dc3545 !important;
    color: #721c24 !important;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: #ffc107 !important;
    color: #664d03 !important;
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1) !important;
    border-color: #0dcaf0 !important;
    color: #055160 !important;
}

/* 8. FIX DROPDOWN COLORS */
.dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
}

.dropdown-item {
    color: #333333 !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #e9ecef !important;
    color: #333333 !important;
}

.dropdown-item.active {
    background-color: #6CABDD !important;
    color: #ffffff !important;
}

/* 9. FIX MODAL COLORS */
.modal-content {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.modal-header {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-bottom: 1px solid #dee2e6 !important;
}

.modal-body {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.modal-footer {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    border-top: 1px solid #dee2e6 !important;
}

/* 10. FIX PAGINATION COLORS */
.page-link {
    background-color: #ffffff !important;
    color: #6CABDD !important;
    border: 1px solid #dee2e6 !important;
}

.page-link:hover {
    background-color: #e9ecef !important;
    color: #5A91BA !important;
    border-color: #dee2e6 !important;
}

.page-item.active .page-link {
    background-color: #6CABDD !important;
    border-color: #6CABDD !important;
    color: #ffffff !important;
}

.page-item.disabled .page-link {
    background-color: #ffffff !important;
    color: #6c757d !important;
    border-color: #dee2e6 !important;
}

/* 11. FIX BREADCRUMB COLORS */
.breadcrumb {
    background-color: transparent !important;
}

.breadcrumb-item a {
    color: #6CABDD !important;
}

.breadcrumb-item.active {
    color: #ffffff !important;
}

/* 12. FIX LIST GROUP COLORS */
.list-group-item {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.list-group-item:hover {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.list-group-item.active {
    background-color: #6CABDD !important;
    border-color: #6CABDD !important;
    color: #ffffff !important;
}

/* 13. FIX PROGRESS BAR COLORS */
.progress {
    background-color: #e9ecef !important;
}

.progress-bar {
    background-color: #6CABDD !important;
    color: #ffffff !important;
}

/* 14. FIX NAVBAR COLORS (Override conflicts) */
.navbar-dark .navbar-nav .nav-link {
    color: #ffffff !important;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
    color: #6CABDD !important;
}

.navbar-dark .navbar-brand {
    color: #ffffff !important;
}

.navbar-dark .navbar-brand:hover,
.navbar-dark .navbar-brand:focus {
    color: #6CABDD !important;
}

/* 15. FIX SPECIFIC MANCHESTER CITY THEME CONFLICTS */
.city-search-box {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.city-search-input label {
    color: #666666 !important;
}

.city-search-input input,
.city-search-input select {
    color: #333333 !important;
    background-color: #ffffff !important;
    border: 1px solid #ced4da !important;
}

/* 16. FIX TEXT UTILITIES */
.text-primary {
    color: #6CABDD !important;
}

.text-secondary {
    color: #6c757d !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

.text-light {
    color: #f8f9fa !important;
}

.text-dark {
    color: #212529 !important;
}

.text-white {
    color: #ffffff !important;
}

.text-muted {
    color: #6c757d !important;
}

/* 17. FIX BACKGROUND UTILITIES */
.bg-primary {
    background-color: #6CABDD !important;
    color: #ffffff !important;
}

.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.bg-info {
    background-color: #0dcaf0 !important;
    color: #000000 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.bg-dark {
    background-color: #212529 !important;
    color: #ffffff !important;
}

.bg-white {
    background-color: #ffffff !important;
    color: #333333 !important;
}
