-- <PERSON><PERSON><PERSON> để thêm dữ liệu mẫu cho hệ thống đặt vé xe
USE DatVeXeDb;
GO

-- Thêm chỗ ngồi cho các xe
-- Xe 1: <PERSON><PERSON><PERSON><PERSON><PERSON> nằm 40 chỗ (51A-12345)
DECLARE @i INT = 1;
WHILE @i <= 40
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (1, 
            CASE 
                WHEN @i < 10 THEN 'A0' + CAST(@i AS VARCHAR(2))
                ELSE 'A' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 4) + 1,
            ((@i - 1) % 4) + 1,
            'Gi<PERSON>ờng nằm',
            1);
    SET @i = @i + 1;
END

-- Xe 2: Limousine 20 chỗ (51B-67890)
SET @i = 1;
WHILE @i <= 20
BEGIN
    INSERT INTO ChoNgois (XeId, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
    VALUES (2, 
            CASE 
                WHEN @i < 10 THEN 'B0' + CAST(@i AS VARCHAR(2))
                ELSE 'B' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 2) + 1,
            ((@i - 1) % 2) + 1,
            'Limousine',
            1);
    SET @i = @i + 1;
END

-- Xe 3: Ghế ngồi 45 chỗ (51C-11111)
SET @i = 1;
WHILE @i <= 45
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (3, 
            CASE 
                WHEN @i < 10 THEN 'C0' + CAST(@i AS VARCHAR(2))
                ELSE 'C' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 5) + 1,
            ((@i - 1) % 5) + 1,
            'Ghế ngồi',
            1);
    SET @i = @i + 1;
END

-- Xe 4: Giường nằm 36 chỗ (51D-22222)
SET @i = 1;
WHILE @i <= 36
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (4, 
            CASE 
                WHEN @i < 10 THEN 'D0' + CAST(@i AS VARCHAR(2))
                ELSE 'D' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 4) + 1,
            ((@i - 1) % 4) + 1,
            'Giường nằm',
            1);
    SET @i = @i + 1;
END

-- Xe 5: Limousine 16 chỗ (51E-33333)
SET @i = 1;
WHILE @i <= 16
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (5, 
            CASE 
                WHEN @i < 10 THEN 'E0' + CAST(@i AS VARCHAR(2))
                ELSE 'E' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 2) + 1,
            ((@i - 1) % 2) + 1,
            'Limousine',
            1);
    SET @i = @i + 1;
END

-- Xe 6: Ghế ngồi 50 chỗ (30A-44444)
SET @i = 1;
WHILE @i <= 50
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (6, 
            CASE 
                WHEN @i < 10 THEN 'F0' + CAST(@i AS VARCHAR(2))
                ELSE 'F' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 5) + 1,
            ((@i - 1) % 5) + 1,
            'Ghế ngồi',
            1);
    SET @i = @i + 1;
END

-- Xe 7: Giường nằm 32 chỗ (30B-55555)
SET @i = 1;
WHILE @i <= 32
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (7, 
            CASE 
                WHEN @i < 10 THEN 'G0' + CAST(@i AS VARCHAR(2))
                ELSE 'G' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 4) + 1,
            ((@i - 1) % 4) + 1,
            'Giường nằm',
            1);
    SET @i = @i + 1;
END

-- Xe 8: Limousine 24 chỗ (30C-66666)
SET @i = 1;
WHILE @i <= 24
BEGIN
    INSERT INTO ChoNgois (XeId, SoGhe, Hang, Cot, LoaiGhe, TrangThaiHoatDong)
    VALUES (8, 
            CASE 
                WHEN @i < 10 THEN 'H0' + CAST(@i AS VARCHAR(2))
                ELSE 'H' + CAST(@i AS VARCHAR(2))
            END,
            ((@i - 1) / 3) + 1,
            ((@i - 1) % 3) + 1,
            'Limousine',
            1);
    SET @i = @i + 1;
END

-- Thêm các chuyến xe mẫu
INSERT INTO ChuyenXes (TuyenDuongId, NgayKhoiHanh, XeId, Gia, ThoiGianDi, GhiChu, TrangThai, NgayTao)
VALUES 
-- Tuyến TP.HCM - Nha Trang
(1, '2024-12-15 08:00:00', 1, 350000, '08:00:00', 'Chuyến sáng', 1, '2024-12-01'),
(1, '2024-12-15 20:00:00', 4, 380000, '20:00:00', 'Chuyến tối', 1, '2024-12-01'),
(1, '2024-12-16 08:00:00', 1, 350000, '08:00:00', 'Chuyến sáng', 1, '2024-12-01'),
(1, '2024-12-16 20:00:00', 4, 380000, '20:00:00', 'Chuyến tối', 1, '2024-12-01'),

-- Tuyến TP.HCM - Đà Lạt  
(2, '2024-12-15 07:00:00', 2, 280000, '07:00:00', 'Chuyến sáng sớm', 1, '2024-12-01'),
(2, '2024-12-15 14:00:00', 5, 320000, '14:00:00', 'Chuyến chiều', 1, '2024-12-01'),
(2, '2024-12-15 21:00:00', 7, 300000, '21:00:00', 'Chuyến tối', 1, '2024-12-01'),
(2, '2024-12-16 07:00:00', 2, 280000, '07:00:00', 'Chuyến sáng sớm', 1, '2024-12-01'),

-- Tuyến TP.HCM - Vũng Tàu
(3, '2024-12-15 06:00:00', 3, 120000, '06:00:00', 'Chuyến sáng sớm', 1, '2024-12-01'),
(3, '2024-12-15 09:00:00', 6, 100000, '09:00:00', 'Chuyến sáng', 1, '2024-12-01'),
(3, '2024-12-15 12:00:00', 3, 120000, '12:00:00', 'Chuyến trưa', 1, '2024-12-01'),
(3, '2024-12-15 15:00:00', 6, 100000, '15:00:00', 'Chuyến chiều', 1, '2024-12-01'),
(3, '2024-12-15 18:00:00', 3, 120000, '18:00:00', 'Chuyến tối', 1, '2024-12-01'),

-- Tuyến TP.HCM - Cần Thơ
(4, '2024-12-15 06:30:00', 6, 150000, '06:30:00', 'Chuyến sáng', 1, '2024-12-01'),
(4, '2024-12-15 13:00:00', 3, 160000, '13:00:00', 'Chuyến chiều', 1, '2024-12-01'),
(4, '2024-12-15 19:00:00', 6, 150000, '19:00:00', 'Chuyến tối', 1, '2024-12-01'),

-- Tuyến Hà Nội - Hạ Long
(5, '2024-12-15 08:00:00', 8, 200000, '08:00:00', 'Chuyến sáng', 1, '2024-12-01'),
(5, '2024-12-15 14:00:00', 8, 220000, '14:00:00', 'Chuyến chiều', 1, '2024-12-01');

-- Thêm một số vé đã đặt mẫu
INSERT INTO Ves (ChuyenXeId, NguoiDungId, ChoNgoiId, TenKhach, SoDienThoai, Email, NgayDat, TrangThai, GiaVe, MaVe)
VALUES 
(1, 2, 1, 'Nguyễn Văn An', '0987654321', '<EMAIL>', '2024-12-01', 1, 350000, 'VE001'),
(1, 3, 2, 'Trần Thị Bình', '0912345678', '<EMAIL>', '2024-12-01', 1, 350000, 'VE002'),
(2, NULL, 41, 'Lê Văn Cường', '0901111111', '<EMAIL>', '2024-12-01', 1, 380000, 'VE003'),
(3, 2, 21, 'Nguyễn Văn An', '0987654321', '<EMAIL>', '2024-12-02', 1, 280000, 'VE004'),
(9, NULL, 61, 'Phạm Thị Dung', '0922222222', '<EMAIL>', '2024-12-02', 1, 120000, 'VE005');

PRINT 'Đã thêm dữ liệu mẫu thành công!';

DECLARE @ChoNgoiCount INT, @ChuyenXeCount INT, @VeCount INT;
SELECT @ChoNgoiCount = COUNT(*) FROM ChoNgois;
SELECT @ChuyenXeCount = COUNT(*) FROM ChuyenXes;
SELECT @VeCount = COUNT(*) FROM Ves;

PRINT 'Tổng số chỗ ngồi đã tạo: ' + CAST(@ChoNgoiCount AS VARCHAR(10));
PRINT 'Tổng số chuyến xe đã tạo: ' + CAST(@ChuyenXeCount AS VARCHAR(10));
PRINT 'Tổng số vé đã đặt: ' + CAST(@VeCount AS VARCHAR(10));
