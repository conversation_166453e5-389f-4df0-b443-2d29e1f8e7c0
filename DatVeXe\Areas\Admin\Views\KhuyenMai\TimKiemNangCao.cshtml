@model IEnumerable<DatVeXe.Models.KhuyenMai>

@{
    ViewData["Title"] = "Tìm kiếm nâng cao khuyến mãi";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-search" style="color: #e74c3c;"></i>
        Tìm kiếm nâng cao khuyến mãi
    </h3>
    <div>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-list"></i> Danh sách khuyến mãi
        </a>
        <a asp-action="Create" class="btn btn-success">
            <i class="fas fa-plus"></i> Tạo mới
        </a>
    </div>
</div>

<!-- Form tìm kiếm nâng cao -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter"></i>
            Bộ lọc tìm kiếm nâng cao
        </h5>
    </div>
    <div class="card-body">
        <form method="get" asp-action="TimKiemNangCao">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">Tên khuyến mãi</label>
                    <input type="text" name="tenKhuyenMai" class="form-control" 
                           value="@ViewBag.CurrentTenKhuyenMai" placeholder="Nhập tên khuyến mãi..." />
                </div>
                <div class="col-md-6">
                    <label class="form-label">Mã khuyến mãi</label>
                    <input type="text" name="maKhuyenMai" class="form-control" 
                           value="@ViewBag.CurrentMaKhuyenMai" placeholder="Nhập mã khuyến mãi..." />
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">Loại khuyến mãi</label>
                    <select name="loaiKhuyenMai" class="form-select">
                        <option value="">-- Tất cả --</option>
                        @{
                            var currentLoaiKhuyenMai = ViewBag.CurrentLoaiKhuyenMai?.ToString();
                        }
                        @if (currentLoaiKhuyenMai == LoaiKhuyenMai.GiamPhanTram.ToString())
                        {
                            <option value="@LoaiKhuyenMai.GiamPhanTram" selected>Giảm theo phần trăm</option>
                        }
                        else
                        {
                            <option value="@LoaiKhuyenMai.GiamPhanTram">Giảm theo phần trăm</option>
                        }

                        @if (currentLoaiKhuyenMai == LoaiKhuyenMai.GiamSoTien.ToString())
                        {
                            <option value="@LoaiKhuyenMai.GiamSoTien" selected>Giảm theo số tiền</option>
                        }
                        else
                        {
                            <option value="@LoaiKhuyenMai.GiamSoTien">Giảm theo số tiền</option>
                        }

                        @if (currentLoaiKhuyenMai == LoaiKhuyenMai.MienPhi.ToString())
                        {
                            <option value="@LoaiKhuyenMai.MienPhi" selected>Miễn phí</option>
                        }
                        else
                        {
                            <option value="@LoaiKhuyenMai.MienPhi">Miễn phí</option>
                        }
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Trạng thái</label>
                    <select name="trangThaiHoatDong" class="form-select">
                        <option value="">-- Tất cả --</option>
                        @{
                            var currentTrangThai = ViewBag.CurrentTrangThaiHoatDong?.ToString();
                        }
                        @if (currentTrangThai == "True")
                        {
                            <option value="true" selected>Đang hoạt động</option>
                        }
                        else
                        {
                            <option value="true">Đang hoạt động</option>
                        }

                        @if (currentTrangThai == "False")
                        {
                            <option value="false" selected>Tạm dừng</option>
                        }
                        else
                        {
                            <option value="false">Tạm dừng</option>
                        }
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Trạng thái thời gian</label>
                    <select id="trangThaiThoiGian" class="form-select">
                        <option value="">-- Tùy chọn --</option>
                        <option value="chua_bat_dau">Chưa bắt đầu</option>
                        <option value="dang_dien_ra">Đang diễn ra</option>
                        <option value="da_ket_thuc">Đã kết thúc</option>
                        <option value="sap_het_han">Sắp hết hạn (7 ngày)</option>
                    </select>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">Từ ngày bắt đầu</label>
                    <input type="date" name="tuNgay" class="form-control" 
                           value="@(ViewBag.CurrentTuNgay != null ? ((DateTime)ViewBag.CurrentTuNgay).ToString("yyyy-MM-dd") : "")" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Đến ngày kết thúc</label>
                    <input type="date" name="denNgay" class="form-control" 
                           value="@(ViewBag.CurrentDenNgay != null ? ((DateTime)ViewBag.CurrentDenNgay).ToString("yyyy-MM-dd") : "")" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Giá trị từ</label>
                    <input type="number" name="giaTriTu" class="form-control" 
                           value="@ViewBag.CurrentGiaTriTu" placeholder="0" min="0" step="0.01" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Giá trị đến</label>
                    <input type="number" name="giaTriDen" class="form-control" 
                           value="@ViewBag.CurrentGiaTriDen" placeholder="1000000" min="0" step="0.01" />
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                    <a asp-action="TimKiemNangCao" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Đặt lại
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Kết quả tìm kiếm -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i>
            Kết quả tìm kiếm (@ViewBag.TotalItems kết quả)
        </h5>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead style="background-color: #34495e; color: white;">
                        <tr>
                            <th>Mã khuyến mãi</th>
                            <th>Tên chương trình</th>
                            <th>Loại giảm giá</th>
                            <th>Giá trị</th>
                            <th>Thời gian</th>
                            <th>Đã sử dụng</th>
                            <th>Trạng thái</th>
                            <th style="width: 200px;">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            var isExpired = item.NgayKetThuc < DateTime.Now;
                            var isExpiringSoon = !isExpired && item.NgayKetThuc <= DateTime.Now.AddDays(7);
                            var isNotStarted = item.NgayBatDau > DateTime.Now;

                            <tr class="@(isExpired ? "table-danger" : isExpiringSoon ? "table-warning" : "")">
                                <td>
                                    <span class="badge bg-primary">@item.MaKhuyenMai</span>
                                </td>
                                <td>@item.TenKhuyenMai</td>
                                <td>
                                    @if (item.LoaiKhuyenMai == LoaiKhuyenMai.GiamPhanTram)
                                    {
                                        <span class="badge bg-info">Giảm %</span>
                                    }
                                    else if (item.LoaiKhuyenMai == LoaiKhuyenMai.GiamSoTien)
                                    {
                                        <span class="badge bg-success">Giảm tiền</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">Miễn phí</span>
                                    }
                                </td>
                                <td>
                                    @if (item.LoaiKhuyenMai == LoaiKhuyenMai.GiamPhanTram)
                                    {
                                        <strong>@item.GiaTri%</strong>
                                        @if (item.GiaTriToiDa.HasValue)
                                        {
                                            <br><small class="text-muted">Tối đa: @item.GiaTriToiDa.Value.ToString("N0") VNĐ</small>
                                        }
                                    }
                                    else
                                    {
                                        <strong>@item.GiaTri.ToString("N0") VNĐ</strong>
                                    }
                                </td>
                                <td>
                                    <div>
                                        <small>@item.NgayBatDau.ToString("dd/MM/yyyy")</small>
                                        <br>
                                        <small>@item.NgayKetThuc.ToString("dd/MM/yyyy")</small>
                                    </div>
                                    @if (isNotStarted)
                                    {
                                        <span class="badge bg-secondary">Chưa bắt đầu</span>
                                    }
                                    else if (isExpired)
                                    {
                                        <span class="badge bg-danger">Đã hết hạn</span>
                                    }
                                    else if (isExpiringSoon)
                                    {
                                        <span class="badge bg-warning">Sắp hết hạn</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">Đang diễn ra</span>
                                    }
                                </td>
                                <td>
                                    <strong>@item.SoLuongDaSuDung</strong>
                                    @if (item.SoLuongToiDa.HasValue)
                                    {
                                        <span>/@item.SoLuongToiDa</span>
                                    }
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input toggle-status" type="checkbox" 
                                               data-id="@item.KhuyenMaiId" @(item.TrangThaiHoatDong ? "checked" : "") />
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.KhuyenMaiId" 
                                           class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.KhuyenMaiId" 
                                           class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Copy" asp-route-id="@item.KhuyenMaiId" 
                                           class="btn btn-sm btn-outline-secondary" title="Sao chép">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                        <a asp-action="BaoCao" asp-route-id="@item.KhuyenMaiId" 
                                           class="btn btn-sm btn-outline-success" title="Báo cáo">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Phân trang -->
            @if (ViewBag.TotalPages > 1)
            {
                <nav aria-label="Phân trang">
                    <ul class="pagination justify-content-center">
                        @if (ViewBag.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="TimKiemNangCao" 
                                   asp-route-tenKhuyenMai="@ViewBag.CurrentTenKhuyenMai"
                                   asp-route-maKhuyenMai="@ViewBag.CurrentMaKhuyenMai"
                                   asp-route-loaiKhuyenMai="@ViewBag.CurrentLoaiKhuyenMai"
                                   asp-route-trangThaiHoatDong="@ViewBag.CurrentTrangThaiHoatDong"
                                   asp-route-tuNgay="@ViewBag.CurrentTuNgay"
                                   asp-route-denNgay="@ViewBag.CurrentDenNgay"
                                   asp-route-giaTriTu="@ViewBag.CurrentGiaTriTu"
                                   asp-route-giaTriDen="@ViewBag.CurrentGiaTriDen"
                                   asp-route-page="@(ViewBag.CurrentPage - 1)">Trước</a>
                            </li>
                        }

                        @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                <a class="page-link" asp-action="TimKiemNangCao" 
                                   asp-route-tenKhuyenMai="@ViewBag.CurrentTenKhuyenMai"
                                   asp-route-maKhuyenMai="@ViewBag.CurrentMaKhuyenMai"
                                   asp-route-loaiKhuyenMai="@ViewBag.CurrentLoaiKhuyenMai"
                                   asp-route-trangThaiHoatDong="@ViewBag.CurrentTrangThaiHoatDong"
                                   asp-route-tuNgay="@ViewBag.CurrentTuNgay"
                                   asp-route-denNgay="@ViewBag.CurrentDenNgay"
                                   asp-route-giaTriTu="@ViewBag.CurrentGiaTriTu"
                                   asp-route-giaTriDen="@ViewBag.CurrentGiaTriDen"
                                   asp-route-page="@i">@i</a>
                            </li>
                        }

                        @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                        {
                            <li class="page-item">
                                <a class="page-link" asp-action="TimKiemNangCao" 
                                   asp-route-tenKhuyenMai="@ViewBag.CurrentTenKhuyenMai"
                                   asp-route-maKhuyenMai="@ViewBag.CurrentMaKhuyenMai"
                                   asp-route-loaiKhuyenMai="@ViewBag.CurrentLoaiKhuyenMai"
                                   asp-route-trangThaiHoatDong="@ViewBag.CurrentTrangThaiHoatDong"
                                   asp-route-tuNgay="@ViewBag.CurrentTuNgay"
                                   asp-route-denNgay="@ViewBag.CurrentDenNgay"
                                   asp-route-giaTriTu="@ViewBag.CurrentGiaTriTu"
                                   asp-route-giaTriDen="@ViewBag.CurrentGiaTriDen"
                                   asp-route-page="@(ViewBag.CurrentPage + 1)">Sau</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h5>Không tìm thấy kết quả</h5>
                <p>Không có khuyến mãi nào phù hợp với điều kiện tìm kiếm.</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Xử lý toggle trạng thái
            $('.toggle-status').change(function() {
                var id = $(this).data('id');
                var status = $(this).is(':checked');
                
                $.post('@Url.Action("ToggleStatus", "KhuyenMai")', { id: id, status: status })
                    .done(function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                        } else {
                            toastr.error(response.message);
                        }
                    })
                    .fail(function() {
                        toastr.error('Có lỗi xảy ra khi cập nhật trạng thái');
                    });
            });

            // Xử lý dropdown trạng thái thời gian
            $('#trangThaiThoiGian').change(function() {
                var value = $(this).val();
                var today = new Date();
                var tuNgay = $('input[name="tuNgay"]');
                var denNgay = $('input[name="denNgay"]');
                
                switch(value) {
                    case 'chua_bat_dau':
                        tuNgay.val(today.toISOString().split('T')[0]);
                        denNgay.val('');
                        break;
                    case 'dang_dien_ra':
                        var yesterday = new Date(today);
                        yesterday.setDate(yesterday.getDate() - 1);
                        var tomorrow = new Date(today);
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        tuNgay.val(yesterday.toISOString().split('T')[0]);
                        denNgay.val(tomorrow.toISOString().split('T')[0]);
                        break;
                    case 'da_ket_thuc':
                        denNgay.val(today.toISOString().split('T')[0]);
                        tuNgay.val('');
                        break;
                    case 'sap_het_han':
                        var nextWeek = new Date(today);
                        nextWeek.setDate(nextWeek.getDate() + 7);
                        tuNgay.val(today.toISOString().split('T')[0]);
                        denNgay.val(nextWeek.toISOString().split('T')[0]);
                        break;
                }
            });
        });
    </script>
}
