/* Base Styles */
html {
  font-size: 14px;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  background: #0a2640;
  color: #fff;
  font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Manchester City Style Navigation */
.navbar, .footer {
  background: #0a2640 !important;
  color: #fff !important;
}

.navbar {
  border-bottom: 1px solid rgba(255,255,255,0.1);
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar .nav-link, .navbar-brand {
  color: #fff !important;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.navbar .nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background: #6CABDD;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar .nav-link:hover::after,
.navbar .nav-link.active::after {
  width: 80%;
}

.navbar .nav-link.active, .navbar .nav-link:hover {
  color: #6CABDD !important;
}

/* City Style Navigation */
.city-nav-container {
  background-color: #0a2640;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  position: relative;
  z-index: 100;
}

.city-nav {
  padding: 10px 0;
}

.city-nav-link {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
  position: relative;
}

.city-nav-link:hover {
  color: #6CABDD;
}

.city-nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #6CABDD;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.city-nav-link:hover::after {
  width: 80%;
}

/* City Hero Section */
.city-hero {
  height: 600px;
  overflow: hidden;
  position: relative;
}

.city-hero-slider {
  width: 100%;
  height: 100%;
}

.city-hero-slide {
  width: 100%;
  height: 100%;
}

.city-hero-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.city-hero-overlay {
  background: linear-gradient(90deg, rgba(10,38,64,0.9) 0%, rgba(10,38,64,0.7) 50%, rgba(10,38,64,0.4) 100%);
}

.city-hero-content {
  padding: 2rem 0;
  max-width: 600px;
}

.city-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.city-hero-title span {
  color: #6CABDD;
}

.city-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: rgba(255,255,255,0.8);
}

.city-btn {
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 0;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #6CABDD;
  border-color: #6CABDD;
}

.btn-primary:hover {
  background-color: #5A91BA;
  border-color: #5A91BA;
}

.btn-outline-light:hover {
  background-color: rgba(255,255,255,0.1);
  color: #fff;
}

/* City Search Box */
.city-search-container {
  margin-top: -50px;
  position: relative;
  z-index: 10;
  margin-bottom: 3rem;
}

.city-search-box {
  background-color: #fff;
  border-radius: 0;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow: hidden;
}

.city-search-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-right: 1px solid #f0f0f0;
  height: 100%;
}

.city-search-icon {
  font-size: 1.5rem;
  color: #6CABDD;
  margin-right: 1rem;
}

.city-search-input {
  flex: 1;
}

.city-search-input label {
  display: block;
  font-size: 0.875rem;
  color: #666 !important;
  margin-bottom: 0.25rem;
}

.city-search-input .form-select,
.city-search-input .form-control {
  border: none;
  padding: 0.5rem 0;
  font-size: 1.125rem;
  color: #333;
  font-weight: 500;
}

.city-search-input .form-select:focus,
.city-search-input .form-control:focus {
  box-shadow: none;
  outline: none;
}

.city-search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6CABDD;
  color: #fff;
  font-size: 1.5rem;
  height: 100%;
  width: 100%;
  transition: all 0.3s ease;
}

.city-search-btn:hover {
  background-color: #5A91BA;
  color: #fff;
}

/* City News Section */
.city-news-section {
  padding: 4rem 0;
  background-color: #f5f5f5;
  color: #333;
}

.city-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.city-section-title {
  font-size: 2rem;
  font-weight: 700;
  text-transform: uppercase;
  color: #0a2640;
  margin: 0;
  position: relative;
  padding-bottom: 0.5rem;
}

.city-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background-color: #6CABDD;
}

.city-section-subtitle {
  font-size: 1.125rem;
  color: #666;
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.city-view-all {
  color: #6CABDD;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.city-view-all i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.city-view-all:hover {
  color: #5A91BA;
}

.city-view-all:hover i {
  transform: translateX(5px);
}

.city-news-main {
  position: relative;
  height: 100%;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.city-news-main:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.city-news-img-container {
  height: 300px;
  overflow: hidden;
}

.city-news-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.city-news-main:hover .city-news-img {
  transform: scale(1.05);
}

.city-news-content {
  padding: 2rem;
}

.city-news-category {
  display: inline-block;
  background-color: #6CABDD;
  color: #fff;
  padding: 0.25rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.city-news-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #0a2640;
}

.city-news-excerpt {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.city-news-link {
  color: #6CABDD;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
}

.city-news-link i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.city-news-link:hover {
  color: #5A91BA;
}

.city-news-link:hover i {
  transform: translateX(5px);
}

.city-news-secondary {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1.5rem;
}

.city-news-secondary-item {
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  height: calc(50% - 0.75rem);
  transition: all 0.3s ease;
}

.city-news-secondary-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.city-news-secondary-img {
  height: 150px;
  overflow: hidden;
}

.city-news-secondary-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.city-news-secondary-item:hover .city-news-secondary-img img {
  transform: scale(1.05);
}

.city-news-secondary-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.city-news-secondary-title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #0a2640;
}

/* City Features Section */
.city-features-section {
  padding: 4rem 0;
  background-color: #0a2640;
  color: #fff;
}

.city-feature-card {
  background-color: rgba(255,255,255,0.05);
  border: 1px solid rgba(255,255,255,0.1);
  padding: 2rem;
  height: 100%;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.city-feature-card:hover {
  transform: translateY(-10px);
  background-color: rgba(255,255,255,0.1);
}

.city-feature-icon {
  font-size: 2.5rem;
  color: #6CABDD;
  margin-bottom: 1.5rem;
}

.city-feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #fff;
}

.city-feature-text {
  color: rgba(255,255,255,0.7);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  flex: 1;
}

.city-feature-link {
  color: #6CABDD;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
  margin-top: auto;
}

.city-feature-link i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.city-feature-link:hover {
  color: #fff;
}

.city-feature-link:hover i {
  transform: translateX(5px);
}

/* City Routes Section */
.city-routes-section {
  padding: 4rem 0;
  background-color: #f5f5f5;
  color: #333;
}

.city-routes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.city-route-card {
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.city-route-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.city-route-img {
  height: 180px;
  overflow: hidden;
}

.city-route-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.city-route-card:hover .city-route-img img {
  transform: scale(1.05);
}

.city-route-content {
  padding: 1.5rem;
}

.city-route-cities {
  display: flex;
  align-items: center;
  font-size: 1.125rem;
  font-weight: 700;
  color: #0a2640;
  margin-bottom: 1rem;
}

.city-route-from, .city-route-to {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.city-route-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.city-route-price {
  font-weight: 700;
  color: #6CABDD;
  font-size: 1.125rem;
}

.city-route-time {
  color: #666;
}

.city-route-btn {
  display: block;
  width: 100%;
  background-color: #6CABDD;
  color: #fff;
  text-align: center;
  padding: 0.75rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.city-route-btn:hover {
  background-color: #5A91BA;
  color: #fff;
}

/* City Stats Section */
.city-stats-section {
  padding: 3rem 0;
  background-color: #0a2640;
  position: relative;
  overflow: hidden;
}

.city-stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/BusBanner.jpg') center/cover no-repeat;
  opacity: 0.1;
  z-index: 1;
}

.city-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.city-stat-card {
  text-align: center;
  padding: 2rem;
  background-color: rgba(255,255,255,0.05);
  border: 1px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.city-stat-card:hover {
  transform: translateY(-5px);
  background-color: rgba(255,255,255,0.1);
}

.city-stat-icon {
  font-size: 2.5rem;
  color: #6CABDD;
  margin-bottom: 1rem;
}

.city-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0.5rem;
}

.city-stat-label {
  color: rgba(255,255,255,0.7);
  font-size: 1.125rem;
}

/* City App Section */
.city-app-section {
  padding: 4rem 0;
  background-color: #f5f5f5;
  color: #333;
}

.city-app-content {
  padding: 2rem 0;
}

.city-app-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0a2640;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 1rem;
}

.city-app-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background-color: #6CABDD;
}

.city-app-text {
  font-size: 1.125rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.city-app-buttons {
  display: flex;
  gap: 1rem;
}

.city-app-btn {
  display: inline-flex;
  align-items: center;
  background-color: #0a2640;
  color: #fff;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.city-app-btn:hover {
  background-color: #6CABDD;
  color: #fff;
}

.city-app-image {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.city-app-image img {
  max-height: 500px;
  object-fit: contain;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-slide-up {
  animation: slideUp 0.8s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.animate-slide-right {
  animation: slideRight 0.8s forwards;
  opacity: 0;
  transform: translateX(-30px);
}

.animate-slide-left {
  animation: slideLeft 0.8s forwards;
  opacity: 0;
  transform: translateX(30px);
}

.animate-fade-in {
  animation: fadeIn 1s forwards;
  opacity: 0;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Buttons */
.btn {
  transition: all 0.3s ease;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.btn-primary {
  background: #00b4d8;
  border: none;
  color: #fff;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background: #0096c7 !important;
  color: #fff !important;
}

.btn-outline-primary {
  border: 2px solid #00b4d8;
  color: #00b4d8;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #00b4d8;
  color: #fff;
}

.rounded-lg {
  border-radius: 1rem !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.shadow-lg {
  box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.shadow-2xl {
  box-shadow: 0 25px 50px rgba(0,0,0,0.25) !important;
}

/* Footer */
.footer {
  background: #0a2640;
  color: #fff;
  border-top: 2px solid #00b4d8;
  padding: 1.5rem 0;
  margin-top: 2rem;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  background: #14477a;
}

::-webkit-scrollbar-thumb {
  background: #00b4d8;
  border-radius: 4px;
}