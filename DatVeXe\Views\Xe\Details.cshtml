@model DatVeXe.Models.Xe

@{
    ViewData["Title"] = "Chi tiết xe";
    var soChuyenXe = Model.ChuyenXes?.Count ?? 0;
    var tongVe = Model.ChuyenXes?.Sum(c => c.Ves?.Count ?? 0) ?? 0;
    var tyLeLapDay = soChuyenXe > 0 ? (double)tongVe / (soChuyenXe * Model.SoGhe) * 100 : 0;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-bus"></i> @ViewData["Title"] - @Model.BienSo
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-id-card"></i> Bi<PERSON>n số xe:</strong></td>
                                    <td><span class="badge bg-primary fs-6">@Model.BienSo</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-bus"></i> Loại xe:</strong></td>
                                    <td><span class="badge bg-info fs-6">@Model.LoaiXe</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-chair"></i> Số ghế:</strong></td>
                                    <td><span class="badge bg-secondary fs-6">@Model.SoGhe chỗ</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="fas fa-route"></i> Số chuyến:</strong></td>
                                    <td><span class="badge bg-success fs-6">@soChuyenXe chuyến</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-ticket-alt"></i> Tổng vé bán:</strong></td>
                                    <td><span class="badge bg-warning fs-6">@tongVe vé</span></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="fas fa-chart-line"></i> Tỷ lệ lấp đầy:</strong></td>
                                    <td>
                                        @if (tyLeLapDay > 0)
                                        {
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar @(tyLeLapDay >= 80 ? "bg-success" : tyLeLapDay >= 50 ? "bg-warning" : "bg-danger")" 
                                                     role="progressbar" style="width: @tyLeLapDay.ToString("F1")%" 
                                                     aria-valuenow="@tyLeLapDay.ToString("F1")" aria-valuemin="0" aria-valuemax="100">
                                                    @tyLeLapDay.ToString("F1")%
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có dữ liệu</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="d-flex gap-2 mt-3">
                        @if (Context.Session.GetInt32("IsAdmin") == 1)
                        {
                            <a asp-action="Edit" asp-route-id="@Model.XeId" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </a>
                            @if (soChuyenXe == 0)
                            {
                                <a asp-action="Delete" asp-route-id="@Model.XeId" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Xóa
                                </a>
                            }
                        }
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Thống kê nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">@soChuyenXe</h4>
                            <small class="text-muted">Chuyến xe</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-success">@tongVe</h4>
                            <small class="text-muted">Vé đã bán</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="@(tyLeLapDay >= 80 ? "text-success" : tyLeLapDay >= 50 ? "text-warning" : "text-danger")">
                                @tyLeLapDay.ToString("F1")%
                            </h4>
                            <small class="text-muted">Tỷ lệ lấp đầy</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (Model.ChuyenXes != null && Model.ChuyenXes.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> Danh sách chuyến xe
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Điểm đi</th>
                                        <th>Điểm đến</th>
                                        <th>Ngày khởi hành</th>
                                        <th>Giá vé</th>
                                        <th>Số vé đã bán</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var chuyen in Model.ChuyenXes.OrderByDescending(c => c.NgayKhoiHanh))
                                    {
                                        var daDi = chuyen.NgayKhoiHanh <= DateTime.Now;
                                        var soVeDaBan = chuyen.Ves?.Count ?? 0;
                                        
                                        <tr>
                                            <td>@chuyen.DiemDiDisplay</td>
                                            <td>@chuyen.DiemDenDisplay</td>
                                            <td>@chuyen.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@chuyen.Gia.ToString("N0") VNĐ</td>
                                            <td>
                                                <span class="badge bg-info">@soVeDaBan/@Model.SoGhe</span>
                                            </td>
                                            <td>
                                                @if (daDi)
                                                {
                                                    <span class="badge bg-secondary">Đã đi</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">Chưa đi</span>
                                                }
                                            </td>
                                            <td>
                                                <a asp-controller="ChuyenXe" asp-action="Details" asp-route-id="@chuyen.ChuyenXeId" 
                                                   class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
