@model DatVeXe.Models.ChonChoNgoiViewModel
@{
    ViewData["Title"] = "Chọn chỗ ngồi";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="seat-selection-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="seat-title">
                        <i class="bi bi-geo-alt-fill me-2"></i>
                        @Model.ChuyenXe.DiemDiDisplay - @Model.ChuyenXe.DiemDenDisplay
                    </h2>
                    <div class="trip-info">
                        <span class="badge bg-primary me-2">
                            <i class="bi bi-calendar me-1"></i>
                            @Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                        </span>
                        <span class="badge bg-success me-2">
                            <i class="bi bi-bus-front me-1"></i>
                            @Model.ChuyenXe.Xe?.BienSo - @Model.ChuyenXe.Xe?.LoaiXe
                        </span>
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-currency-dollar me-1"></i>
                            @Model.ChuyenXe.Gia.ToString("N0") VNĐ
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="@Url.Action("Search", "ChuyenXe")" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-1"></i>Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="row">
            <!-- Sơ đồ ghế -->
            <div class="col-lg-8">
                <div class="card seat-map-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-grid-3x3-gap me-2"></i>Sơ đồ ghế xe
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Legend -->
                        <div class="seat-legend mb-4">
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="seat-demo seat-available"></div>
                                    <small>Còn trống</small>
                                </div>
                                <div class="col-3">
                                    <div class="seat-demo seat-selected"></div>
                                    <small>Đang chọn</small>
                                </div>
                                <div class="col-3">
                                    <div class="seat-demo seat-occupied"></div>
                                    <small>Đã đặt</small>
                                </div>
                                <div class="col-3">
                                    <div class="seat-demo seat-disabled"></div>
                                    <small>Không khả dụng</small>
                                </div>
                            </div>
                        </div>

                        <!-- Bus Layout -->
                        <div class="bus-layout">
                            <div class="bus-front">
                                <i class="bi bi-steering-wheel"></i>
                                <span>Tài xế</span>
                            </div>
                            
                            <div class="seat-grid" id="seatGrid">
                                @{
                                    var maxHang = Model.DanhSachChoNgoi.Any() ? Model.DanhSachChoNgoi.Max(c => c.Hang) : 0;
                                    var maxCot = Model.DanhSachChoNgoi.Any() ? Model.DanhSachChoNgoi.Max(c => c.Cot) : 0;
                                    var loaiXe = Model.ChuyenXe.Xe?.LoaiXe?.ToLower() ?? "";
                                }

                                <!-- Hiển thị thông tin xe -->
                                <div class="vehicle-info mb-3">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted">Loại xe</small>
                                            <div class="fw-bold">@Model.ChuyenXe.Xe?.LoaiXe</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Tổng ghế</small>
                                            <div class="fw-bold">@Model.ChuyenXe.Xe?.SoGhe</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Còn trống</small>
                                            <div class="fw-bold text-success">@(Model.DanhSachChoNgoi.Count(c => c.TrangThaiHoatDong && !c.DaDat))</div>
                                        </div>
                                    </div>
                                </div>

                                @for (int hang = 1; hang <= maxHang; hang++)
                                {
                                    <div class="seat-row" data-row="@hang">
                                        <!-- Row number -->
                                        <div class="row-number">@hang</div>

                                        @for (int cot = 1; cot <= maxCot; cot++)
                                        {
                                            var choNgoi = Model.DanhSachChoNgoi.FirstOrDefault(c => c.Hang == hang && c.Cot == cot);
                                            if (choNgoi != null)
                                            {
                                                var seatClass = "seat-item seat-available";
                                                if (!choNgoi.TrangThaiHoatDong)
                                                {
                                                    seatClass = "seat-item seat-disabled";
                                                }
                                                else if (choNgoi.DaDat)
                                                {
                                                    seatClass = "seat-item seat-occupied";
                                                }

                                                <div class="@seatClass"
                                                     data-seat-id="@choNgoi.ChoNgoiId"
                                                     data-seat-number="@choNgoi.SoGhe"
                                                     data-seat-type="@choNgoi.LoaiGhe"
                                                     data-row="@hang"
                                                     data-col="@cot"
                                                     data-available="@(choNgoi.TrangThaiHoatDong && !choNgoi.DaDat ? "true" : "false")"
                                                     title="@choNgoi.SoGhe - @choNgoi.LoaiGhe @(choNgoi.DaDat ? $"(Đã đặt - {choNgoi.TenKhachDat})" : "")">
                                                    <span class="seat-number">@choNgoi.SoGhe</span>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="seat-empty"></div>
                                            }

                                            @* Thêm lối đi dựa trên loại xe *@
                                            @if (ShouldShowAisle(loaiXe, cot, maxCot))
                                            {
                                                <div class="seat-aisle">
                                                    <i class="bi bi-arrow-down text-muted"></i>
                                                </div>
                                            }
                                        }

                                        <!-- Row number (right side) -->
                                        <div class="row-number">@hang</div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form đặt vé -->
            <div class="col-lg-4">
                <div class="card booking-form-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-ticket-perforated me-2"></i>Thông tin đặt vé
                        </h5>
                    </div>
                    <div class="card-body">
                        <form asp-action="DatVe" method="post" id="bookingForm">
                            <input type="hidden" name="ChuyenXeId" value="@Model.ChuyenXe.ChuyenXeId" />
                            <input type="hidden" name="ChoNgoiId" id="selectedSeatId" />
                            
                            <!-- Thông tin ghế đã chọn -->
                            <div class="selected-seat-info mb-3" id="selectedSeatInfo" style="display: none;">
                                <div class="alert alert-info">
                                    <strong>Ghế đã chọn:</strong>
                                    <span id="selectedSeatDisplay"></span>
                                </div>
                            </div>

                            <!-- Thông tin khách hàng -->
                            <div class="mb-3">
                                <label class="form-label">Tên khách hàng *</label>
                                <input type="text" name="TenKhach" class="form-control" required 
                                       placeholder="Nhập họ tên đầy đủ" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Số điện thoại *</label>
                                <input type="tel" name="SoDienThoai" class="form-control" required 
                                       placeholder="Nhập số điện thoại" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="Email" class="form-control" 
                                       placeholder="Nhập email để nhận xác nhận" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Ghi chú</label>
                                <textarea name="GhiChu" class="form-control" rows="3" 
                                          placeholder="Ghi chú thêm (nếu có)"></textarea>
                            </div>

                            <!-- Tóm tắt đơn hàng -->
                            <div class="order-summary">
                                <h6>Tóm tắt đơn hàng</h6>
                                <div class="d-flex justify-content-between">
                                    <span>Giá vé:</span>
                                    <strong>@Model.ChuyenXe.Gia.ToString("N0") VNĐ</strong>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>Tổng cộng:</strong>
                                    <strong class="text-primary">@Model.ChuyenXe.Gia.ToString("N0") VNĐ</strong>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mt-3" id="bookButton" disabled>
                                <i class="bi bi-credit-card me-1"></i>Đặt vé ngay
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Thông tin chuyến xe -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Thông tin chuyến xe</h6>
                    </div>
                    <div class="card-body">
                        <div class="trip-detail">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tuyến:</span>
                                <strong>@Model.ChuyenXe.DiemDiDisplay - @Model.ChuyenXe.DiemDenDisplay</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Ngày giờ:</span>
                                <strong>@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Xe:</span>
                                <strong>@Model.ChuyenXe.Xe?.BienSo</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Loại xe:</span>
                                <strong>@Model.ChuyenXe.Xe?.LoaiXe</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Còn trống:</span>
                                <strong class="text-success">
                                    @(Model.ChuyenXe.Xe?.SoGhe - Model.DanhSachChoNgoi.Count(c => c.DaDat)) / @Model.ChuyenXe.Xe?.SoGhe chỗ
                                </strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Header */
        .seat-selection-header {
            background-color: #2c3e50;
            padding: 2rem 0;
            color: white;
            border-bottom: 4px solid #3498db;
        }

        .seat-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .trip-info .badge {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }

        .trip-info .bg-primary {
            background-color: #3498db !important;
        }

        .trip-info .bg-success {
            background-color: #27ae60 !important;
        }

        .trip-info .bg-warning {
            background-color: #f39c12 !important;
            color: #ffffff !important;
        }

        /* Cards */
        .seat-map-card, .booking-form-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 15px;
        }

        /* Seat Legend */
        .seat-legend {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .seat-legend small {
            color: #2c3e50;
            font-weight: 500;
        }

        .seat-demo {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            margin: 0 auto 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            color: #ffffff;
        }

        /* Bus Layout */
        .bus-layout {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
        }

        .bus-front {
            background-color: #34495e;
            color: #ffffff;
            padding: 0.75rem;
            border-radius: 10px 10px 0 0;
            text-align: center;
            margin-bottom: 1rem;
            font-weight: 600;
            border: 2px solid #3498db;
        }

        .bus-front i {
            font-size: 1.2rem;
            margin-right: 0.5rem;
            color: #3498db;
        }

        /* Vehicle Info */
        .vehicle-info {
            background: #ecf0f1;
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid #bdc3c7;
        }

        .vehicle-info small {
            color: #7f8c8d;
            font-size: 0.75rem;
        }

        /* Seat Grid */
        .seat-grid {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 600px;
            margin: 0 auto;
        }

        .seat-row {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
            position: relative;
        }

        .row-number {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #ecf0f1;
            border-radius: 50%;
            font-size: 0.8rem;
            font-weight: 600;
            color: #2c3e50;
            border: 2px solid #bdc3c7;
        }

        .seat-item {
            width: 45px;
            height: 45px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .seat-number {
            color: #ffffff;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Seat States */
        .seat-available {
            background-color: #27ae60;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
            border: 2px solid #2ecc71;
        }

        .seat-available:hover {
            background-color: #2ecc71;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
        }

        .seat-selected {
            background-color: #3498db;
            border: 3px solid #ffffff;
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.5);
        }

        .seat-occupied {
            background-color: #e74c3c;
            cursor: not-allowed;
            opacity: 0.8;
            border: 2px solid #c0392b;
        }

        .seat-disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
            opacity: 0.6;
            border: 2px solid #7f8c8d;
        }

        /* Demo seat colors */
        .seat-demo.seat-available {
            background-color: #27ae60;
            border: 2px solid #2ecc71;
        }

        .seat-demo.seat-selected {
            background-color: #3498db;
            border: 2px solid #ffffff;
        }

        .seat-demo.seat-occupied {
            background-color: #e74c3c;
            border: 2px solid #c0392b;
        }

        .seat-demo.seat-disabled {
            background-color: #95a5a6;
            border: 2px solid #7f8c8d;
        }

        .seat-empty {
            width: 45px;
            height: 45px;
        }

        .seat-aisle {
            width: 30px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(to bottom, transparent 40%, #ecf0f1 40%, #ecf0f1 60%, transparent 60%);
            position: relative;
        }

        .seat-aisle i {
            font-size: 1.2rem;
            color: #95a5a6;
        }

        .seat-aisle::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 2px;
            height: 20px;
            background-color: #bdc3c7;
            border-radius: 1px;
        }

        /* Booking Form */
        .booking-form-card .card-header {
            background-color: #3498db !important;
            border-radius: 15px 15px 0 0;
            color: #ffffff !important;
        }

        .selected-seat-info {
            border-left: 4px solid #3498db;
        }

        .selected-seat-info .alert-info {
            background-color: #ecf0f1;
            border-color: #3498db;
            color: #2c3e50;
        }

        .order-summary {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
        }

        .trip-detail {
            font-size: 0.9rem;
        }

        /* Fix text visibility issues */
        .form-label {
            color: #2c3e50 !important;
            font-weight: 600;
        }

        .card-header h5,
        .card-header h6 {
            color: #ffffff !important;
            margin-bottom: 0;
        }

        .card-body {
            color: #495057;
        }

        .trip-detail span {
            color: #6c757d;
        }

        .trip-detail strong {
            color: #2c3e50;
        }

        .order-summary h6 {
            color: #2c3e50 !important;
            font-weight: 600;
        }

        .order-summary span {
            color: #495057;
        }

        .order-summary strong {
            color: #2c3e50;
        }

        .text-primary {
            color: #3498db !important;
        }

        .text-success {
            color: #27ae60 !important;
        }

        /* Card headers with light background */
        .card-header.bg-light {
            background-color: #f8f9fa !important;
            color: #2c3e50 !important;
            border-bottom: 1px solid #e9ecef;
        }

        .card-header.bg-light h5,
        .card-header.bg-light h6 {
            color: #2c3e50 !important;
        }

        /* Button styling */
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .btn-primary:disabled {
            background-color: #95a5a6;
            border-color: #95a5a6;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: #ffffff;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            .seat-item {
                width: 35px;
                height: 35px;
                font-size: 0.7rem;
            }

            .seat-demo {
                width: 25px;
                height: 25px;
            }

            .bus-layout {
                padding: 1rem;
            }

            .seat-title {
                font-size: 1.4rem;
            }

            .row-number {
                width: 25px;
                height: 25px;
                font-size: 0.7rem;
            }

            .seat-aisle {
                width: 20px;
                height: 35px;
            }

            .vehicle-info {
                padding: 0.75rem;
            }

            .seat-grid {
                gap: 6px;
            }

            .seat-row {
                gap: 6px;
            }
        }

        @@media (max-width: 480px) {
            .seat-item {
                width: 30px;
                height: 30px;
                font-size: 0.6rem;
            }

            .row-number {
                width: 20px;
                height: 20px;
                font-size: 0.6rem;
            }

            .seat-aisle {
                width: 15px;
                height: 30px;
            }

            .seat-grid {
                gap: 4px;
            }

            .seat-row {
                gap: 4px;
            }
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            let selectedSeat = null;
            let holdTimeout = null;

            // Xử lý click chọn ghế
            $('.seat-available').click(function() {
                const seatId = $(this).data('seat-id');
                const seatNumber = $(this).data('seat-number');
                const seatType = $(this).data('seat-type');

                // Bỏ chọn ghế cũ
                $('.seat-selected').removeClass('seat-selected').addClass('seat-available');

                // Chọn ghế mới
                $(this).removeClass('seat-available').addClass('seat-selected');

                selectedSeat = {
                    id: seatId,
                    number: seatNumber,
                    type: seatType
                };

                // Cập nhật form
                $('#selectedSeatId').val(seatId);
                $('#selectedSeatDisplay').text(`${seatNumber} (${seatType})`);
                $('#selectedSeatInfo').show();
                $('#bookButton').prop('disabled', false);

                // Giữ chỗ tạm thời (5 phút)
                holdSeat(seatId);
            });

            // Giữ chỗ tạm thời
            function holdSeat(seatId) {
                if (holdTimeout) {
                    clearTimeout(holdTimeout);
                }

                $.post('@Url.Action("GiuCho")', {
                    choNgoiId: seatId,
                    chuyenXeId: @Model.ChuyenXe.ChuyenXeId
                }, function(response) {
                    if (response.success) {
                        // Đặt timeout để tự động hủy giữ chỗ
                        holdTimeout = setTimeout(function() {
                            releaseSeat(seatId);
                            showNotification('Hết thời gian giữ chỗ. Vui lòng chọn lại.', 'warning');
                        }, response.timeout * 1000);
                    }
                });
            }

            // Hủy giữ chỗ
            function releaseSeat(seatId) {
                $.post('@Url.Action("HuyGiuCho")', {
                    choNgoiId: seatId,
                    chuyenXeId: @Model.ChuyenXe.ChuyenXeId
                });

                // Reset UI
                $(`.seat-item[data-seat-id="${seatId}"]`)
                    .removeClass('seat-selected')
                    .addClass('seat-available');

                $('#selectedSeatInfo').hide();
                $('#bookButton').prop('disabled', true);
                selectedSeat = null;
            }

            // Validation form
            $('#bookingForm').submit(function(e) {
                if (!selectedSeat) {
                    e.preventDefault();
                    showNotification('Vui lòng chọn chỗ ngồi', 'error');
                    return false;
                }

                const tenKhach = $('input[name="TenKhach"]').val().trim();
                const soDienThoai = $('input[name="SoDienThoai"]').val().trim();

                if (!tenKhach || !soDienThoai) {
                    e.preventDefault();
                    showNotification('Vui lòng điền đầy đủ thông tin bắt buộc', 'error');
                    return false;
                }

                // Disable button để tránh double submit
                $('#bookButton').prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>Đang xử lý...');
            });

            // Hủy giữ chỗ khi rời trang
            $(window).on('beforeunload', function() {
                if (selectedSeat) {
                    releaseSeat(selectedSeat.id);
                }
            });

            // Notification helper
            function showNotification(message, type) {
                const alertClass = type === 'error' ? 'alert-danger' :
                                 type === 'warning' ? 'alert-warning' : 'alert-info';

                const notification = $(`
                    <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                         style="top: 20px; right: 20px; z-index: 9999;">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `);

                $('body').append(notification);

                setTimeout(function() {
                    notification.alert('close');
                }, 5000);
            }
        });
    </script>
}

@functions {
    private bool ShouldShowAisle(string loaiXe, int cot, int maxCot)
    {
        switch (loaiXe.ToLower())
        {
            case "limousine":
                return cot == 1; // Lối đi giữa cột 1 và 3
            case "giường nằm":
                return cot == 3; // Lối đi giữa cột 3 và 5 (bên trái: 1,2,3 - bên phải: 5,6,7)
            case "ghế ngồi":
                return cot == 2; // Lối đi giữa cột 2 và 4 (bên trái: 1,2 - bên phải: 4,5,6)
            case "vip":
                return cot == 1; // Lối đi giữa cột 1 và 3 (bên trái: 1 - bên phải: 3,4)
            default:
                return cot == 2 && maxCot > 3; // Mặc định
        }
    }
}
