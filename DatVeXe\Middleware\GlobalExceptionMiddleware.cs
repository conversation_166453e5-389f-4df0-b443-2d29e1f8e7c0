using System.Net;
using System.Text.Json;

namespace DatVeXe.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly IWebHostEnvironment _environment;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, IWebHostEnvironment environment)
        {
            _next = next;
            _logger = logger;
            _environment = environment;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";
            
            var response = new ErrorResponse();

            switch (exception)
            {
                case BookingException bookingEx:
                    response.Message = bookingEx.Message;
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Details = bookingEx.Details;
                    break;

                case PaymentException paymentEx:
                    response.Message = paymentEx.Message;
                    response.StatusCode = (int)HttpStatusCode.PaymentRequired;
                    response.Details = paymentEx.Details;
                    break;

                case ValidationException validationEx:
                    response.Message = "Dữ liệu không hợp lệ";
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Details = validationEx.Errors;
                    break;

                case UnauthorizedAccessException:
                    response.Message = "Bạn không có quyền truy cập";
                    response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    break;

                case ArgumentNullException argEx:
                    response.Message = "Thiếu thông tin bắt buộc";
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Details = new { parameter = argEx.ParamName };
                    break;

                case InvalidOperationException:
                    response.Message = "Thao tác không hợp lệ";
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    break;

                default:
                    response.Message = _environment.IsDevelopment() 
                        ? exception.Message 
                        : "Có lỗi xảy ra trong hệ thống. Vui lòng thử lại sau.";
                    response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    
                    if (_environment.IsDevelopment())
                    {
                        response.Details = new 
                        { 
                            stackTrace = exception.StackTrace,
                            innerException = exception.InnerException?.Message
                        };
                    }
                    break;
            }

            context.Response.StatusCode = response.StatusCode;

            // For AJAX requests, return JSON
            if (context.Request.Headers["X-Requested-With"] == "XMLHttpRequest" || 
                context.Request.ContentType?.Contains("application/json") == true)
            {
                var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await context.Response.WriteAsync(jsonResponse);
            }
            else
            {
                // For regular requests, redirect to error page
                context.Response.Redirect($"/Home/Error?statusCode={response.StatusCode}&message={Uri.EscapeDataString(response.Message)}");
            }
        }
    }

    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public object? Details { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    // Custom exceptions
    public class BookingException : Exception
    {
        public object? Details { get; }

        public BookingException(string message) : base(message) { }
        
        public BookingException(string message, object? details) : base(message)
        {
            Details = details;
        }

        public BookingException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class PaymentException : Exception
    {
        public object? Details { get; }

        public PaymentException(string message) : base(message) { }
        
        public PaymentException(string message, object? details) : base(message)
        {
            Details = details;
        }

        public PaymentException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class ValidationException : Exception
    {
        public object? Errors { get; }

        public ValidationException(string message) : base(message) { }
        
        public ValidationException(string message, object? errors) : base(message)
        {
            Errors = errors;
        }
    }

    // Extension method to register middleware
    public static class GlobalExceptionMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GlobalExceptionMiddleware>();
        }
    }
}
