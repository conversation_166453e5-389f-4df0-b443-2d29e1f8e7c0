﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "NguoiDungs",
                columns: table => new
                {
                    NguoiDungId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    HoTen = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MatKhau = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SoDienThoai = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    Dia<PERSON>hi = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    NgaySinh = table.Column<DateTime>(type: "datetime2", nullable: true),
                    GioiTinh = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    LaAdmin = table.Column<bool>(type: "bit", nullable: false),
                    TrangThaiHoatDong = table.Column<bool>(type: "bit", nullable: false),
                    NgayDangKy = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LanDangNhapCuoi = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NguoiDungs", x => x.NguoiDungId);
                });

            migrationBuilder.CreateTable(
                name: "TuyenDuongs",
                columns: table => new
                {
                    TuyenDuongId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TenTuyen = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    DiemDi = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DiemDen = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    KhoangCach = table.Column<int>(type: "int", nullable: false),
                    ThoiGianDuKien = table.Column<TimeSpan>(type: "time", nullable: false),
                    MoTa = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    TrangThaiHoatDong = table.Column<bool>(type: "bit", nullable: false),
                    NgayTao = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TuyenDuongs", x => x.TuyenDuongId);
                });

            migrationBuilder.CreateTable(
                name: "Xes",
                columns: table => new
                {
                    XeId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BienSo = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LoaiXe = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SoGhe = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Xes", x => x.XeId);
                });

            migrationBuilder.CreateTable(
                name: "ChoNgois",
                columns: table => new
                {
                    ChoNgoiId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    XeId = table.Column<int>(type: "int", nullable: false),
                    SoGhe = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Hang = table.Column<int>(type: "int", nullable: false),
                    Cot = table.Column<int>(type: "int", nullable: false),
                    LoaiGhe = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    TrangThaiHoatDong = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChoNgois", x => x.ChoNgoiId);
                    table.ForeignKey(
                        name: "FK_ChoNgois_Xes_XeId",
                        column: x => x.XeId,
                        principalTable: "Xes",
                        principalColumn: "XeId");
                });

            migrationBuilder.CreateTable(
                name: "ChuyenXes",
                columns: table => new
                {
                    ChuyenXeId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TuyenDuongId = table.Column<int>(type: "int", nullable: true),
                    DiemDi = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DiemDen = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    NgayKhoiHanh = table.Column<DateTime>(type: "datetime2", nullable: false),
                    XeId = table.Column<int>(type: "int", nullable: false),
                    Gia = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ThoiGianDi = table.Column<TimeSpan>(type: "time", nullable: false),
                    GhiChu = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    TrangThai = table.Column<bool>(type: "bit", nullable: false),
                    NgayTao = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChuyenXes", x => x.ChuyenXeId);
                    table.ForeignKey(
                        name: "FK_ChuyenXes_TuyenDuongs_TuyenDuongId",
                        column: x => x.TuyenDuongId,
                        principalTable: "TuyenDuongs",
                        principalColumn: "TuyenDuongId",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_ChuyenXes_Xes_XeId",
                        column: x => x.XeId,
                        principalTable: "Xes",
                        principalColumn: "XeId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ves",
                columns: table => new
                {
                    VeId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ChuyenXeId = table.Column<int>(type: "int", nullable: false),
                    NguoiDungId = table.Column<int>(type: "int", nullable: true),
                    ChoNgoiId = table.Column<int>(type: "int", nullable: true),
                    TenKhach = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SoDienThoai = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    NgayDat = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TrangThai = table.Column<int>(type: "int", nullable: false),
                    GiaVe = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    GhiChu = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    NgayHuy = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LyDoHuy = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MaVe = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ves", x => x.VeId);
                    table.ForeignKey(
                        name: "FK_Ves_ChoNgois_ChoNgoiId",
                        column: x => x.ChoNgoiId,
                        principalTable: "ChoNgois",
                        principalColumn: "ChoNgoiId");
                    table.ForeignKey(
                        name: "FK_Ves_ChuyenXes_ChuyenXeId",
                        column: x => x.ChuyenXeId,
                        principalTable: "ChuyenXes",
                        principalColumn: "ChuyenXeId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Ves_NguoiDungs_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "NguoiDungs",
                        principalColumn: "NguoiDungId",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.InsertData(
                table: "NguoiDungs",
                columns: new[] { "NguoiDungId", "DiaChi", "Email", "GioiTinh", "HoTen", "LaAdmin", "LanDangNhapCuoi", "MatKhau", "NgayDangKy", "NgaySinh", "SoDienThoai", "TrangThaiHoatDong" },
                values: new object[,]
                {
                    { 1, "123 Nguyễn Huệ, Q1, TP.HCM", "<EMAIL>", "Nam", "Administrator", true, new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Admin@123", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1990, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "0901234567", true },
                    { 2, "456 Lê Lợi, Q1, TP.HCM", "<EMAIL>", "Nam", "Nguyễn Văn An", false, null, "User@123", new DateTime(2024, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1995, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "0987654321", true },
                    { 3, "789 Trần Hưng Đạo, Q5, TP.HCM", "<EMAIL>", "Nữ", "Trần Thị Bình", false, null, "User@123", new DateTime(2024, 11, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1992, 8, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), "0912345678", true }
                });

            migrationBuilder.InsertData(
                table: "TuyenDuongs",
                columns: new[] { "TuyenDuongId", "DiemDen", "DiemDi", "KhoangCach", "MoTa", "NgayTao", "TenTuyen", "ThoiGianDuKien", "TrangThaiHoatDong" },
                values: new object[,]
                {
                    { 1, "Nha Trang", "TP.HCM", 450, "Tuyến đường du lịch nổi tiếng, phong cảnh đẹp", new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "TP.HCM - Nha Trang", new TimeSpan(0, 8, 30, 0, 0), true },
                    { 2, "Đà Lạt", "TP.HCM", 300, "Tuyến đường lên thành phố ngàn hoa", new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "TP.HCM - Đà Lạt", new TimeSpan(0, 6, 0, 0, 0), true },
                    { 3, "Vũng Tàu", "TP.HCM", 125, "Tuyến đường ngắn đến biển Vũng Tàu", new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "TP.HCM - Vũng Tàu", new TimeSpan(0, 2, 30, 0, 0), true },
                    { 4, "Cần Thơ", "TP.HCM", 170, "Tuyến đường đến miền Tây sông nước", new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "TP.HCM - Cần Thơ", new TimeSpan(0, 3, 30, 0, 0), true },
                    { 5, "Hạ Long", "Hà Nội", 165, "Tuyến đường đến vịnh Hạ Long", new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Hà Nội - Hạ Long", new TimeSpan(0, 3, 0, 0, 0), true }
                });

            migrationBuilder.InsertData(
                table: "Xes",
                columns: new[] { "XeId", "BienSo", "LoaiXe", "SoGhe" },
                values: new object[,]
                {
                    { 1, "51A-12345", "Giường nằm 40 chỗ", 40 },
                    { 2, "51B-67890", "Limousine 20 chỗ", 20 },
                    { 3, "51C-11111", "Ghế ngồi 45 chỗ", 45 },
                    { 4, "51D-22222", "Giường nằm 36 chỗ", 36 },
                    { 5, "51E-33333", "Limousine 16 chỗ", 16 },
                    { 6, "30A-44444", "Ghế ngồi 50 chỗ", 50 },
                    { 7, "30B-55555", "Giường nằm 32 chỗ", 32 },
                    { 8, "30C-66666", "Limousine 24 chỗ", 24 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_ChoNgois_XeId_SoGhe",
                table: "ChoNgois",
                columns: new[] { "XeId", "SoGhe" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChuyenXes_TuyenDuongId",
                table: "ChuyenXes",
                column: "TuyenDuongId");

            migrationBuilder.CreateIndex(
                name: "IX_ChuyenXes_XeId",
                table: "ChuyenXes",
                column: "XeId");

            migrationBuilder.CreateIndex(
                name: "IX_TuyenDuongs_DiemDi_DiemDen",
                table: "TuyenDuongs",
                columns: new[] { "DiemDi", "DiemDen" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Ves_ChoNgoiId",
                table: "Ves",
                column: "ChoNgoiId");

            migrationBuilder.CreateIndex(
                name: "IX_Ves_ChuyenXeId",
                table: "Ves",
                column: "ChuyenXeId");

            migrationBuilder.CreateIndex(
                name: "IX_Ves_NguoiDungId",
                table: "Ves",
                column: "NguoiDungId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Ves");

            migrationBuilder.DropTable(
                name: "ChoNgois");

            migrationBuilder.DropTable(
                name: "ChuyenXes");

            migrationBuilder.DropTable(
                name: "NguoiDungs");

            migrationBuilder.DropTable(
                name: "TuyenDuongs");

            migrationBuilder.DropTable(
                name: "Xes");
        }
    }
}
