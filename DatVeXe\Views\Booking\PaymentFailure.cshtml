@model DatVeXe.Models.PaymentFailureViewModel
@{
    ViewData["Title"] = "Thanh toán thất bại";
}

<div class="container py-4">
    <!-- Failure Header -->
    <div class="text-center mb-5">
        <div class="failure-animation mb-4">
            <div class="error-circle">
                <div class="error-mark"></div>
            </div>
        </div>
        <h1 class="display-4 fw-bold text-danger mb-3">Thanh toán thất bại!</h1>
        <p class="lead text-muted">R<PERSON>t tiế<PERSON>, giao dịch của bạn không thể hoàn tất</p>
        
        @if (TempData["Error"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                @TempData["Error"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Error Details Card -->
            <div class="card shadow-lg border-0 mb-4 payment-failure-card">
                <div class="card-header bg-gradient-danger text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>Chi tiết lỗi
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="error-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-x-circle text-danger me-1"></i>Mã lỗi
                                    </div>
                                    <div class="info-value error-code">
                                        @(Model.ErrorCode ?? "PAYMENT_FAILED")
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-info-circle text-warning me-1"></i>Mô tả lỗi
                                    </div>
                                    <div class="info-value">
                                        @(Model.ErrorMessage ?? "Giao dịch thanh toán không thành công")
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-clock text-info me-1"></i>Thời gian
                                    </div>
                                    <div class="info-value">
                                        @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info">
                                @if (!string.IsNullOrEmpty(Model.TransactionId))
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-receipt text-secondary me-1"></i>Mã giao dịch
                                        </div>
                                        <div class="info-value transaction-code">
                                            @Model.TransactionId
                                        </div>
                                    </div>
                                }

                                @if (!string.IsNullOrEmpty(Model.PaymentMethod))
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-credit-card text-primary me-1"></i>Phương thức thanh toán
                                        </div>
                                        <div class="info-value">
                                            <span class="badge bg-secondary fs-6">@Model.PaymentMethod</span>
                                        </div>
                                    </div>
                                }

                                @if (Model.Amount.HasValue)
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-currency-dollar text-success me-1"></i>Số tiền
                                        </div>
                                        <div class="info-value">
                                            @string.Format("{0:N0}", Model.Amount.Value) VNĐ
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Information (if available) -->
            @if (Model.BookingInfo != null)
            {
                <div class="card shadow-lg border-0 mb-4">
                    <div class="card-header bg-warning text-dark py-3">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-ticket-perforated me-2"></i>Thông tin đặt vé
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="booking-info">
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-hash text-primary me-1"></i>Mã vé
                                        </div>
                                        <div class="info-value">@Model.BookingInfo.MaVe</div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-person text-warning me-1"></i>Hành khách
                                        </div>
                                        <div class="info-value">@Model.BookingInfo.TenKhach</div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-telephone text-info me-1"></i>Số điện thoại
                                        </div>
                                        <div class="info-value">@Model.BookingInfo.SoDienThoai</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="trip-info">
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-geo-alt text-success me-1"></i>Tuyến đường
                                        </div>
                                        <div class="info-value route-display">
                                            @Model.BookingInfo.DiemDi
                                            <i class="bi bi-arrow-right mx-2"></i>
                                            @Model.BookingInfo.DiemDen
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-calendar-event text-primary me-1"></i>Thời gian khởi hành
                                        </div>
                                        <div class="info-value">
                                            @Model.BookingInfo.NgayKhoiHanh?.ToString("dd/MM/yyyy HH:mm")
                                        </div>
                                    </div>

                                    @if (!string.IsNullOrEmpty(Model.BookingInfo.SoGhe))
                                    {
                                        <div class="info-item">
                                            <div class="info-label">
                                                <i class="bi bi-square-fill text-success me-1"></i>Ghế ngồi
                                            </div>
                                            <div class="info-value">
                                                <span class="badge bg-warning text-dark fs-6">Ghế @Model.BookingInfo.SoGhe</span>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-3" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Lưu ý:</strong> Vé của bạn vẫn được giữ chỗ trong <strong>15 phút</strong>. 
                            Bạn có thể thử thanh toán lại hoặc chọn phương thức thanh toán khác.
                        </div>
                    </div>
                </div>
            }

            <!-- Common Error Causes -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-question-circle me-2"></i>Nguyên nhân thường gặp
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Số dư tài khoản không đủ
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Thẻ bị khóa hoặc hết hạn
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Thông tin thẻ không chính xác
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Vượt quá hạn mức giao dịch
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Lỗi kết nối mạng
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-x-circle text-danger me-2"></i>
                                    Hệ thống ngân hàng bảo trì
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Solutions -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-lightbulb me-2"></i>Giải pháp khắc phục
                    </h6>
                </div>
                <div class="card-body">
                    <div class="solutions-list">
                        <div class="solution-item">
                            <div class="solution-icon">
                                <i class="bi bi-arrow-clockwise text-primary"></i>
                            </div>
                            <div class="solution-content">
                                <h6>Thử lại thanh toán</h6>
                                <p class="text-muted mb-0">Kiểm tra lại thông tin và thử thanh toán một lần nữa</p>
                            </div>
                        </div>
                        
                        <div class="solution-item">
                            <div class="solution-icon">
                                <i class="bi bi-credit-card text-success"></i>
                            </div>
                            <div class="solution-content">
                                <h6>Đổi phương thức thanh toán</h6>
                                <p class="text-muted mb-0">Sử dụng thẻ khác hoặc ví điện tử khác</p>
                            </div>
                        </div>
                        
                        <div class="solution-item">
                            <div class="solution-icon">
                                <i class="bi bi-shop text-warning"></i>
                            </div>
                            <div class="solution-content">
                                <h6>Thanh toán tại quầy</h6>
                                <p class="text-muted mb-0">Chọn thanh toán trực tiếp tại bến xe</p>
                            </div>
                        </div>
                        
                        <div class="solution-item">
                            <div class="solution-icon">
                                <i class="bi bi-headset text-info"></i>
                            </div>
                            <div class="solution-content">
                                <h6>Liên hệ hỗ trợ</h6>
                                <p class="text-muted mb-0">Gọi hotline để được hỗ trợ trực tiếp</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    @if (!string.IsNullOrEmpty(Model.SessionId))
                    {
                        <a href="@Url.Action("Payment", new { sessionId = Model.SessionId })" class="btn btn-primary btn-lg">
                            <i class="bi bi-arrow-clockwise me-2"></i>Thử lại thanh toán
                        </a>
                    }
                    
                    <a href="@Url.Action("Search")" class="btn btn-outline-primary btn-lg">
                        <i class="bi bi-search me-2"></i>Tìm chuyến khác
                    </a>
                    
                    <a href="@Url.Action("Index", "Home")" class="btn btn-outline-secondary btn-lg">
                        <i class="bi bi-house me-2"></i>Về trang chủ
                    </a>
                    
                    <a href="@Url.Action("Index", "HoTro")" class="btn btn-outline-info btn-lg">
                        <i class="bi bi-headset me-2"></i>Hỗ trợ
                    </a>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="text-center mt-4">
                <div class="contact-info">
                    <h6 class="text-muted mb-2">Cần hỗ trợ?</h6>
                    <div class="d-flex justify-content-center gap-4 flex-wrap">
                        <div class="contact-item">
                            <i class="bi bi-telephone-fill text-primary me-1"></i>
                            <span>Hotline: 1900-xxxx</span>
                        </div>
                        <div class="contact-item">
                            <i class="bi bi-envelope-fill text-success me-1"></i>
                            <span>Email: <EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="bi bi-chat-dots-fill text-info me-1"></i>
                            <span>Chat trực tuyến</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .failure-animation {
        position: relative;
        display: inline-block;
    }

    .error-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(45deg, #dc3545, #e74c3c);
        position: relative;
        margin: 0 auto;
        animation: scaleIn 0.5s ease-in-out;
    }

    .error-mark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
    }

    .error-mark::before,
    .error-mark::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 3px;
        background: white;
        border-radius: 2px;
        top: 50%;
        left: 50%;
        transform-origin: center;
    }

    .error-mark::before {
        transform: translate(-50%, -50%) rotate(45deg);
        animation: errorMark1 0.5s ease-in-out 0.5s both;
    }

    .error-mark::after {
        transform: translate(-50%, -50%) rotate(-45deg);
        animation: errorMark2 0.5s ease-in-out 0.6s both;
    }

    @@keyframes scaleIn {
        0% {
            transform: scale(0);
        }
        100% {
            transform: scale(1);
        }
    }

    @@keyframes errorMark1 {
        0% {
            width: 0;
        }
        100% {
            width: 20px;
        }
    }

    @@keyframes errorMark2 {
        0% {
            width: 0;
        }
        100% {
            width: 20px;
        }
    }

    .bg-gradient-danger {
        background: linear-gradient(45deg, #dc3545, #e74c3c);
    }

    .payment-failure-card {
        border-left: 5px solid #dc3545;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .info-item {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .info-value {
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
    }

    .error-code, .transaction-code {
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        color: #dc3545;
        background: #fff5f5;
        padding: 0.5rem;
        border-radius: 5px;
        border: 2px dashed #dc3545;
    }

    .route-display {
        font-size: 1.1rem;
        color: #28a745;
    }

    .solutions-list {
        display: grid;
        gap: 1rem;
    }

    .solution-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .solution-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .solution-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        font-size: 1.2rem;
    }

    .solution-content h6 {
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .solution-content p {
        font-size: 0.85rem;
    }

    .contact-info {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        margin-top: 2rem;
    }

    .contact-item {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    @@media (max-width: 768px) {
        .solutions-list {
            grid-template-columns: 1fr;
        }
        
        .contact-item {
            margin-bottom: 0.5rem;
        }
    }
</style>
