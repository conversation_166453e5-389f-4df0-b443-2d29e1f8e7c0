namespace DatVeXe.Services
{
    public interface ISMSService
    {
        Task<bool> SendSMSAsync(string phoneNumber, string message);
        Task<bool> SendTicketConfirmationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            string tripInfo, string seatInfo, decimal price, DateTime departureTime);
        Task<bool> SendTicketCancellationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            string tripInfo, string reason);
        Task<bool> SendPaymentConfirmationSMSAsync(string phoneNumber, string customerName, string ticketCode, 
            decimal amount, string paymentMethod);
    }
}
