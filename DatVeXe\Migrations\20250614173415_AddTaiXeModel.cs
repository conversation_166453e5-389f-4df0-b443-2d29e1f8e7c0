﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddTaiXeModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "TaiXeId",
                table: "ChuyenXes",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "TaiX<PERSON>",
                columns: table => new
                {
                    TaiXeId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    HoTen = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SoDienThoai = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    DiaChi = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    NgaySinh = table.Column<DateTime>(type: "datetime2", nullable: true),
                    GioiTinh = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    CMND = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    SoBangLai = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    LoaiBangLai = table.Column<int>(type: "int", nullable: false),
                    NgayCapBangLai = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NgayHetHanBangLai = table.Column<DateTime>(type: "datetime2", nullable: true),
                    KinhNghiem = table.Column<int>(type: "int", nullable: true),
                    GhiChu = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    TrangThai = table.Column<int>(type: "int", nullable: false),
                    NgayVaoLam = table.Column<DateTime>(type: "datetime2", nullable: false),
                    NgayTao = table.Column<DateTime>(type: "datetime2", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaiXes", x => x.TaiXeId);
                });

            migrationBuilder.InsertData(
                table: "TaiXes",
                columns: new[] { "TaiXeId", "CMND", "DiaChi", "GhiChu", "GioiTinh", "HoTen", "KinhNghiem", "LoaiBangLai", "NgayCapBangLai", "NgayCapNhat", "NgayHetHanBangLai", "NgaySinh", "NgayTao", "NgayVaoLam", "SoBangLai", "SoDienThoai", "TrangThai" },
                values: new object[,]
                {
                    { 1, "025123456789", "123 Lê Lợi, Q1, TP.HCM", null, "Nam", "Nguyễn Văn Tài", 14, 4, new DateTime(2010, 5, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2030, 5, 20, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1985, 3, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2020, 1, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), "BL001234567", "0901111111", 1 },
                    { 2, "025987654321", "456 Nguyễn Huệ, Q1, TP.HCM", null, "Nam", "Trần Minh Đức", 12, 4, new DateTime(2012, 8, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2032, 8, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1988, 7, 22, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2021, 3, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "BL002345678", "0902222222", 1 },
                    { 3, "025456789123", "789 Trần Hưng Đạo, Q5, TP.HCM", null, "Nam", "Lê Hoàng Nam", 9, 4, new DateTime(2015, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), null, new DateTime(2035, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(1990, 12, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2022, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "BL003456789", "0903333333", 1 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_ChuyenXes_TaiXeId",
                table: "ChuyenXes",
                column: "TaiXeId");

            migrationBuilder.CreateIndex(
                name: "IX_TaiXes_CMND",
                table: "TaiXes",
                column: "CMND",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaiXes_SoBangLai",
                table: "TaiXes",
                column: "SoBangLai",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaiXes_SoDienThoai",
                table: "TaiXes",
                column: "SoDienThoai");

            migrationBuilder.AddForeignKey(
                name: "FK_ChuyenXes_TaiXes_TaiXeId",
                table: "ChuyenXes",
                column: "TaiXeId",
                principalTable: "TaiXes",
                principalColumn: "TaiXeId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ChuyenXes_TaiXes_TaiXeId",
                table: "ChuyenXes");

            migrationBuilder.DropTable(
                name: "TaiXes");

            migrationBuilder.DropIndex(
                name: "IX_ChuyenXes_TaiXeId",
                table: "ChuyenXes");

            migrationBuilder.DropColumn(
                name: "TaiXeId",
                table: "ChuyenXes");
        }
    }
}
