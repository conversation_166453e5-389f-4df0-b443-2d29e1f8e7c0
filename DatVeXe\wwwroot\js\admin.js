// Admin Panel JavaScript Functions

// Global Admin Object
window.Admin = {
    // Configuration
    config: {
        toastDuration: 5000,
        animationDuration: 300,
        debounceDelay: 300
    },

    // Initialize admin panel
    init: function() {
        this.initSidebar();
        this.initTooltips();
        this.initConfirmDialogs();
        this.initSearchFilters();
        this.initDataTables();
        this.initCharts();
        this.initFormValidation();
        this.bindEvents();
    },

    // Sidebar functionality
    initSidebar: function() {
        const sidebar = document.querySelector('.admin-sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle');
        
        if (toggleBtn && sidebar) {
            toggleBtn.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Auto-close sidebar on mobile when clicking outside
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // Highlight active menu item
        this.highlightActiveMenu();
    },

    // Highlight active menu item
    highlightActiveMenu: function() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('.admin-sidebar .nav-link');
        
        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href)) {
                link.classList.add('active');
            }
        });
    },

    // Initialize tooltips
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // Initialize confirm dialogs
    initConfirmDialogs: function() {
        document.addEventListener('click', function(e) {
            const confirmBtn = e.target.closest('[data-confirm]');
            if (confirmBtn) {
                e.preventDefault();
                const message = confirmBtn.getAttribute('data-confirm');
                if (confirm(message)) {
                    if (confirmBtn.tagName === 'A') {
                        window.location.href = confirmBtn.href;
                    } else if (confirmBtn.tagName === 'BUTTON') {
                        confirmBtn.click();
                    }
                }
            }
        });
    },

    // Initialize search and filters
    initSearchFilters: function() {
        const searchInputs = document.querySelectorAll('.admin-search');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    Admin.performSearch(this);
                }, Admin.config.debounceDelay);
            });
        });
    },

    // Perform search
    performSearch: function(input) {
        const searchTerm = input.value.toLowerCase();
        const targetTable = input.getAttribute('data-target');
        const table = document.querySelector(targetTable);
        
        if (table) {
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }
    },

    // Initialize data tables
    initDataTables: function() {
        const tables = document.querySelectorAll('.admin-datatable');
        tables.forEach(table => {
            // Add sorting functionality
            this.addTableSorting(table);
            // Add row selection
            this.addRowSelection(table);
        });
    },

    // Add table sorting
    addTableSorting: function(table) {
        const headers = table.querySelectorAll('thead th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                Admin.sortTable(table, this);
            });
        });
    },

    // Sort table
    sortTable: function(table, header) {
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const isAscending = !header.classList.contains('sort-asc');
        
        // Remove existing sort classes
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add new sort class
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aText.localeCompare(bText, undefined, { numeric: true });
            } else {
                return bText.localeCompare(aText, undefined, { numeric: true });
            }
        });
        
        // Reorder rows in DOM
        const tbody = table.querySelector('tbody');
        rows.forEach(row => tbody.appendChild(row));
    },

    // Add row selection
    addRowSelection: function(table) {
        const selectAllCheckbox = table.querySelector('thead input[type="checkbox"]');
        const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                Admin.updateBulkActions();
            });
        }
        
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                Admin.updateBulkActions();
            });
        });
    },

    // Update bulk actions
    updateBulkActions: function() {
        const selectedRows = document.querySelectorAll('tbody input[type="checkbox"]:checked');
        const bulkActions = document.querySelector('.bulk-actions');
        
        if (bulkActions) {
            bulkActions.style.display = selectedRows.length > 0 ? 'block' : 'none';
            const countElement = bulkActions.querySelector('.selected-count');
            if (countElement) {
                countElement.textContent = selectedRows.length;
            }
        }
    },

    // Initialize charts
    initCharts: function() {
        // Initialize Chart.js charts if available
        if (typeof Chart !== 'undefined') {
            this.initRevenueChart();
            this.initStatsCharts();
        }
    },

    // Initialize revenue chart
    initRevenueChart: function() {
        const ctx = document.getElementById('revenueChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: window.chartData?.labels || [],
                    datasets: [{
                        label: 'Doanh thu',
                        data: window.chartData?.revenue || [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    },

    // Initialize stats charts
    initStatsCharts: function() {
        const pieCtx = document.getElementById('statsChart');
        if (pieCtx) {
            new Chart(pieCtx, {
                type: 'doughnut',
                data: {
                    labels: window.statsData?.labels || [],
                    datasets: [{
                        data: window.statsData?.values || [],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    },

    // Initialize form validation
    initFormValidation: function() {
        const forms = document.querySelectorAll('.admin-form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                this.classList.add('was-validated');
            });
        });
    },

    // Bind global events
    bindEvents: function() {
        // Auto-refresh functionality
        const autoRefreshElements = document.querySelectorAll('[data-auto-refresh]');
        autoRefreshElements.forEach(element => {
            const interval = parseInt(element.getAttribute('data-auto-refresh')) * 1000;
            setInterval(() => {
                location.reload();
            }, interval);
        });

        // Copy to clipboard functionality
        document.addEventListener('click', function(e) {
            const copyBtn = e.target.closest('[data-copy]');
            if (copyBtn) {
                const text = copyBtn.getAttribute('data-copy');
                navigator.clipboard.writeText(text).then(() => {
                    Admin.showToast('Đã sao chép vào clipboard', 'success');
                });
            }
        });
    },

    // Show toast notification
    showToast: function(message, type = 'info', duration = null) {
        duration = duration || this.config.toastDuration;
        
        const toastClass = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info'
        }[type] || 'bg-info';

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white ${toastClass} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }

        container.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        setTimeout(() => {
            toast.remove();
        }, duration);
    },

    // Show loading state
    showLoading: function(element, text = 'Đang xử lý...') {
        const originalContent = element.innerHTML;
        element.innerHTML = `<span class="admin-loading"></span> ${text}`;
        element.disabled = true;
        
        return function() {
            element.innerHTML = originalContent;
            element.disabled = false;
        };
    },

    // Utility functions
    utils: {
        // Format number with thousand separators
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        // Format currency
        formatCurrency: function(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        },

        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    Admin.init();
});

// Export for global use
window.Admin = Admin;
