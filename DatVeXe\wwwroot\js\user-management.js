// User Management JavaScript Functions

// Global variables
let currentUserId = null;

// Toggle user active status
function toggleStatus(userId) {
    if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái tài khoản này?')) {
        $.post('/Admin/UserManagement/ToggleStatus', { userId: userId })
            .done(function(data) {
                if (data.success) {
                    // Update badge
                    const badge = $('#status-badge-' + userId);
                    if (data.isActive) {
                        badge.removeClass('bg-danger').addClass('bg-success');
                        badge.html('<i class="fas fa-check me-1"></i>Hoạt động');
                    } else {
                        badge.removeClass('bg-success').addClass('bg-danger');
                        badge.html('<i class="fas fa-times me-1"></i>Không hoạt động');
                    }
                    
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message, 'error');
                }
            })
            .fail(function() {
                showToast('Có lỗi xảy ra khi thay đổi trạng thái', 'error');
            });
    }
}

// Toggle user lock status
function toggleLock(userId) {
    currentUserId = userId;
    const badge = $('#lock-badge-' + userId);
    const isLocked = badge.hasClass('bg-danger');
    
    if (isLocked) {
        // Unlock account
        if (confirm('Bạn có chắc chắn muốn mở khoá tài khoản này?')) {
            $.post('/Admin/UserManagement/ToggleLock', { userId: userId })
                .done(function(data) {
                    if (data.success) {
                        badge.removeClass('bg-danger').addClass('bg-success');
                        badge.html('<i class="fas fa-unlock me-1"></i>Bình thường');
                        showToast(data.message, 'success');
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('Có lỗi xảy ra khi mở khoá tài khoản', 'error');
                });
        }
    } else {
        // Lock account with reason
        $('#lockModal').modal('show');
    }
}

// Reset user password
function resetPassword(userId) {
    currentUserId = userId;
    $('#resetPasswordModal').modal('show');
}

// Initialize page
$(document).ready(function() {
    // Lock modal confirm button
    $('#confirmLock').click(function() {
        const reason = $('#lockReason').val();
        
        $.post('/Admin/UserManagement/ToggleLock', { 
            userId: currentUserId, 
            reason: reason 
        })
        .done(function(data) {
            if (data.success) {
                const badge = $('#lock-badge-' + currentUserId);
                badge.removeClass('bg-success').addClass('bg-danger');
                badge.html('<i class="fas fa-lock me-1"></i>Bị khoá');
                
                $('#lockModal').modal('hide');
                $('#lockReason').val('');
                showToast(data.message, 'success');
            } else {
                showToast(data.message, 'error');
            }
        })
        .fail(function() {
            showToast('Có lỗi xảy ra khi khoá tài khoản', 'error');
        });
    });

    // Reset password modal confirm button
    $('#confirmResetPassword').click(function() {
        const newPassword = $('#newPassword').val();
        const confirmPassword = $('#confirmPassword').val();
        
        if (newPassword.length < 6) {
            showToast('Mật khẩu phải có ít nhất 6 ký tự', 'error');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            showToast('Mật khẩu xác nhận không khớp', 'error');
            return;
        }
        
        $.post('/Admin/UserManagement/ResetPassword', { 
            userId: currentUserId, 
            newPassword: newPassword 
        })
        .done(function(data) {
            if (data.success) {
                $('#resetPasswordModal').modal('hide');
                $('#newPassword').val('');
                $('#confirmPassword').val('');
                showToast(data.message, 'success');
            } else {
                showToast(data.message, 'error');
            }
        })
        .fail(function() {
            showToast('Có lỗi xảy ra khi đặt lại mật khẩu', 'error');
        });
    });

    // Search form auto-submit on change
    $('.search-filters select, .search-filters input').change(function() {
        if ($(this).attr('type') !== 'text') {
            $(this).closest('form').submit();
        }
    });

    // Auto-format phone number inputs
    $('input[type="tel"], input[name="SoDienThoai"]').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length > 10) {
            value = value.slice(0, 10);
        }
        $(this).val(value);
    });

    // Confirm delete actions
    $('button[onclick*="delete"], a[onclick*="delete"]').click(function(e) {
        if (!confirm('Bạn có chắc chắn muốn xóa? Hành động này không thể hoàn tác!')) {
            e.preventDefault();
            return false;
        }
    });
});

// Utility function to show toast notifications
function showToast(message, type) {
    const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
    const iconClass = type === 'success' ? 'fa-check' : 'fa-times';
    
    const toast = $(`
        <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${iconClass} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `);
    
    if (!$('#toast-container').length) {
        $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }
    
    $('#toast-container').append(toast);
    toast.toast('show');
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// Email validation function
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone number validation function
function isValidPhone(phone) {
    const phoneRegex = /^(0[0-9]{9})$/;
    return phoneRegex.test(phone);
}

// Export functions for global use
window.userManagement = {
    toggleStatus,
    toggleLock,
    resetPassword,
    showToast,
    isValidEmail,
    isValidPhone
};
