# Hệ Thống Đặt Vé Xe - ASP.NET MVC

Dự án web đặt vé xe khách hoàn chỉnh sử dụng ASP.NET MVC 8.0, Entity Framework Core (Code-First) với giao diện Manchester City theme.

## 🚀 Tính năng chính

### 👤 Quản lý người dùng
- ✅ Đăng ký, đăng nhập với mã hóa mật khẩu SHA256
- ✅ Phân quyền Admin/User
- ✅ Quản lý thông tin cá nhân
- ✅ Session management với timeout 30 phút

### 🚌 Quản lý xe khách
- ✅ CRUD xe khách (biển số, loại xe, số ghế)
- ✅ Validation biển số duy nhất
- ✅ Thống kê tỷ lệ lấp đầy xe
- ✅ Kiểm tra xe đã được sử dụng

### 🛣️ Quản lý chuyến xe
- ✅ CRUD chuyến xe với thông tin đầy đủ
- ✅ Tìm kiếm nâng cao với nhiều tiêu chí:
  - Đ<PERSON><PERSON><PERSON> đi/đến, ngày khởi hành
  - <PERSON><PERSON><PERSON> theo gi<PERSON> vé, loại xe
  - Sắp xếp theo nhiều tiêu chí
  - Lọc chuyến xe có ghế trống
- ✅ Xuất báo cáo Excel
- ✅ Kiểm tra trùng lịch xe

### 🎫 Đặt vé và quản lý vé
- ✅ Đặt vé trực tuyến với validation real-time
- ✅ Kiểm tra số ghế trống bằng AJAX
- ✅ Hủy vé (trước 2 giờ khởi hành)
- ✅ Sửa đổi thông tin vé
- ✅ Lịch sử đặt vé của người dùng

### 📊 Báo cáo và thống kê
- ✅ Dashboard admin với biểu đồ
- ✅ Báo cáo doanh thu theo thời gian
- ✅ Top tuyến đường phổ biến
- ✅ Thống kê hiệu suất xe
- ✅ Biểu đồ Chart.js tương tác
- ✅ Xuất báo cáo CSV/Excel

### 🎨 Giao diện người dùng
- ✅ Responsive design với Bootstrap 5
- ✅ Manchester City theme (xanh dương)
- ✅ Font Awesome icons
- ✅ Loading states và error handling
- ✅ Validation client-side và server-side

### 🛍️ Trang bổ sung
- ✅ Trang khuyến mãi với các ưu đãi
- ✅ Trang hỗ trợ khách hàng với FAQ
- ✅ Hotline và thông tin liên hệ

## 🏗️ Cấu trúc dự án

```
DatVeXe/
├── Controllers/
│   ├── HomeController.cs          # Trang chủ, dashboard
│   ├── TaiKhoanController.cs      # Authentication
│   ├── DatVeXeControllers.cs      # CRUD chính (Xe, ChuyenXe, Ve, NguoiDung)
│   ├── BaoCaoController.cs        # Báo cáo thống kê
│   ├── KhuyenMaiController.cs     # Trang khuyến mãi
│   └── HoTroController.cs         # Trang hỗ trợ
├── Models/
│   ├── DatVeXeModels.cs          # Entity models
│   ├── DatVeXeContext.cs         # DbContext
│   └── ViewModels/               # Search, Report ViewModels
├── Views/
│   ├── Shared/                   # Layout, partial views
│   ├── Home/                     # Trang chủ, dashboard
│   ├── ChuyenXe/                 # Quản lý chuyến xe
│   ├── Ve/                       # Quản lý vé
│   ├── Xe/                       # Quản lý xe
│   ├── NguoiDung/                # Quản lý người dùng
│   ├── TaiKhoan/                 # Authentication views
│   ├── BaoCao/                   # Báo cáo
│   ├── KhuyenMai/                # Khuyến mãi
│   └── HoTro/                    # Hỗ trợ
└── wwwroot/
    ├── css/                      # Custom styles
    ├── js/                       # Custom scripts
    ├── images/                   # Manchester City images
    └── lib/                      # Bootstrap, jQuery, etc.
```

## 🗄️ Database Schema

### Entities chính:
- **NguoiDung**: Thông tin người dùng, phân quyền
- **Xe**: Thông tin xe khách (biển số, loại xe, số ghế)
- **ChuyenXe**: Chuyến xe (điểm đi/đến, ngày giờ, giá vé)
- **Ve**: Vé đã đặt (liên kết người dùng và chuyến xe)

### Relationships:
- Xe 1:N ChuyenXe
- ChuyenXe 1:N Ve
- NguoiDung 1:N Ve

## 🚀 Khởi động dự án

### Yêu cầu hệ thống:
- .NET 8.0 SDK
- SQL Server Express/LocalDB
- Visual Studio 2022 hoặc VS Code

### Cài đặt:
1. Clone repository
2. Mở terminal tại thư mục `DatVeXe`
3. Restore packages:
   ```bash
   dotnet restore
   ```
4. Cập nhật connection string trong `appsettings.json`
5. Chạy migration:
   ```bash
   dotnet ef database update
   ```
6. Khởi động ứng dụng:
   ```bash
   dotnet run
   ```
7. Truy cập: `https://localhost:7038` hoặc `http://localhost:5057`

### Tài khoản mặc định:
- **Admin**: <EMAIL> / Admin@123
- **User**: <EMAIL> / User@123

## 🔧 Hướng dẫn phát triển

### Thêm migration mới:
```bash
dotnet ef migrations add <TenMigration>
dotnet ef database update
```

### Thêm controller mới:
1. Tạo controller trong `Controllers/`
2. Thêm views tương ứng trong `Views/`
3. Cập nhật navigation menu trong `_Layout.cshtml`

### Thêm model mới:
1. Định nghĩa entity trong `Models/DatVeXeModels.cs`
2. Thêm DbSet vào `DatVeXeContext.cs`
3. Tạo migration và update database

## 🎯 Tính năng nâng cao đã implement

### Security:
- Password hashing với SHA256
- CSRF protection với ValidateAntiForgeryToken
- Session-based authentication
- Role-based authorization

### Performance:
- Eager loading với Include()
- Efficient queries với LINQ
- Client-side validation
- AJAX for real-time updates

### User Experience:
- Responsive design cho mobile
- Loading states và progress indicators
- Error handling và user feedback
- Auto-complete và suggestions

### Business Logic:
- Kiểm tra ghế trống real-time
- Validation thời gian đặt/hủy vé
- Tính toán doanh thu tự động
- Thống kê hiệu suất chi tiết

## 📱 Screenshots

### Trang chủ
- Hero banner với Manchester City theme
- Thống kê tổng quan
- Chuyến xe phổ biến
- Tin tức khuyến mãi

### Dashboard Admin
- Biểu đồ doanh thu
- Thống kê real-time
- Top tuyến đường
- Quản lý nhanh

### Đặt vé
- Form đặt vé với validation
- Kiểm tra ghế trống real-time
- Thông tin chuyến xe chi tiết
- Xác nhận đặt vé

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 📞 Liên hệ

- Email: <EMAIL>
- Hotline: 1900 1234
- Website: https://datvexe.com
