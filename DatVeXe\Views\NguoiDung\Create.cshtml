@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Thêm người dùng mới";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus"></i> @ViewData["Title"]
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="createUserForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="HoTen" class="form-label">
                                <i class="fas fa-user"></i> @Html.DisplayNameFor(model => model.HoTen)
                            </label>
                            <input asp-for="HoTen" class="form-control" placeholder="Nhập họ tên đầy đủ" />
                            <span asp-validation-for="HoTen" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <input asp-for="Email" class="form-control" type="email" placeholder="Nhập địa chỉ email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="MatKhau" class="form-label">
                                <i class="fas fa-lock"></i> @Html.DisplayNameFor(model => model.MatKhau)
                            </label>
                            <div class="input-group">
                                <input asp-for="MatKhau" class="form-control" type="password" placeholder="Nhập mật khẩu" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="MatKhau" class="text-danger"></span>
                            <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự</div>
                        </div>

                        <div class="mb-3">
                            <label for="XacNhanMatKhau" class="form-label">
                                <i class="fas fa-lock"></i> Xác nhận mật khẩu
                            </label>
                            <div class="input-group">
                                <input id="XacNhanMatKhau" name="XacNhanMatKhau" class="form-control" type="password" placeholder="Nhập lại mật khẩu" />
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye" id="toggleConfirmIcon"></i>
                                </button>
                            </div>
                            <span id="confirmPasswordError" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="LaAdmin" class="form-check-input" type="checkbox" />
                                <label asp-for="LaAdmin" class="form-check-label">
                                    <i class="fas fa-crown"></i> @Html.DisplayNameFor(model => model.LaAdmin)
                                </label>
                            </div>
                            <div class="form-text">Chỉ tích chọn nếu muốn cấp quyền quản trị cho người dùng này</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Lưu
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            var form = $('#createUserForm');
            
            // Toggle password visibility
            $('#togglePassword').click(function() {
                var passwordField = $('#MatKhau');
                var icon = $('#toggleIcon');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Toggle confirm password visibility
            $('#toggleConfirmPassword').click(function() {
                var confirmField = $('#XacNhanMatKhau');
                var icon = $('#toggleConfirmIcon');
                
                if (confirmField.attr('type') === 'password') {
                    confirmField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    confirmField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Custom validation for password confirmation
            $.validator.addMethod('passwordMatch', function(value, element) {
                return value === $('#MatKhau').val();
            }, 'Mật khẩu xác nhận không khớp');

            $("#XacNhanMatKhau").rules("add", {
                required: true,
                passwordMatch: true,
                messages: {
                    required: "Vui lòng xác nhận mật khẩu"
                }
            });

            // Handle form submission
            form.on('submit', function(e) {
                if (!form.valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    var firstError = $('.field-validation-error, .text-danger').filter(':visible').first();
                    if (firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 200);
                    }
                    return false;
                }
                
                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');
                return true;
            });
        });
    </script>
}
