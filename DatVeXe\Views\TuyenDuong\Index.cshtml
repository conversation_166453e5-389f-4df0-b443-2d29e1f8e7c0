@model List<DatVeXe.Models.TuyenDuongViewModel>
@{
    ViewData["Title"] = "Quản lý tuyến đường";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold text-primary mb-0">
            <i class="bi bi-geo-alt-fill me-2"></i>@ViewData["Title"]
        </h2>
        <div class="d-flex gap-2">
            <a href="@Url.Action("ThongKe")" class="btn btn-info">
                <i class="bi bi-graph-up me-1"></i>Thống kê tuyến đường
            </a>
            <a href="@Url.Action("Create", "ChuyenXe")" class="btn btn-success">
                <i class="bi bi-plus-circle me-1"></i>Thêm chuyến xe mới
            </a>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>@TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>@TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="bi bi-signpost-2 text-primary" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@Model.Count</h4>
                    <p class="text-muted">Tổng tuyến đường</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="bi bi-check-circle text-success" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@Model.Count(t => t.TrangThaiHoatDong)</h4>
                    <p class="text-muted">Đang hoạt động</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="bi bi-bus-front text-info" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@Model.Sum(t => t.SoChuyenXe)</h4>
                    <p class="text-muted">Tổng chuyến xe</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="bi bi-currency-dollar text-warning" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-2">@Model.Sum(t => t.DoanhThu).ToString("N0")</h4>
                    <p class="text-muted">Tổng doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Routes Table -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="bi bi-list me-2"></i>Danh sách tuyến đường
            </h5>
        </div>
        <div class="card-body">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Tuyến đường</th>
                                <th>Số chuyến xe</th>
                                <th>Vé đã bán</th>
                                <th>Doanh thu</th>
                                <th>Giá vé</th>
                                <th>Chuyến gần nhất</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var tuyen in Model)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-geo-alt me-2 text-primary"></i>
                                            <div>
                                                <strong>@tuyen.DiemDi</strong>
                                                <br>
                                                <i class="bi bi-arrow-down text-muted"></i>
                                                <br>
                                                <strong>@tuyen.DiemDen</strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info fs-6">@tuyen.SoChuyenXe</span>
                                    </td>
                                    <td>
                                        <span class="text-success fw-bold">@tuyen.TongVeDaBan</span>
                                    </td>
                                    <td>
                                        <span class="text-success fw-bold">@tuyen.DoanhThu.ToString("N0") VNĐ</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            @tuyen.GiaThapNhat.ToString("N0") - @tuyen.GiaCaoNhat.ToString("N0") VNĐ
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            @tuyen.ChuyenXeGanNhat.ToString("dd/MM/yyyy HH:mm")
                                        </small>
                                    </td>
                                    <td>
                                        @if (tuyen.TrangThaiHoatDong)
                                        {
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Hoạt động
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-pause-circle me-1"></i>Tạm dừng
                                            </span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="@Url.Action("Details", new { diemDi = tuyen.DiemDi, diemDen = tuyen.DiemDen })" 
                                               class="btn btn-sm btn-outline-primary" title="Xem chi tiết">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="@Url.Action("Index", "ChuyenXe", new { diemDi = tuyen.DiemDi, diemDen = tuyen.DiemDen })" 
                                               class="btn btn-sm btn-outline-info" title="Quản lý chuyến xe">
                                                <i class="bi bi-bus-front"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-warning" 
                                                    onclick="toggleTrangThai('@tuyen.DiemDi', '@tuyen.DiemDen', @tuyen.TrangThaiHoatDong.ToString().ToLower())"
                                                    title="Thay đổi trạng thái">
                                                <i class="bi bi-gear"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-geo-alt text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">Chưa có tuyến đường nào</h4>
                    <p class="text-muted">Hãy thêm chuyến xe đầu tiên để tạo tuyến đường.</p>
                    <a href="@Url.Action("Create", "ChuyenXe")" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Thêm chuyến xe
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<script>
function toggleTrangThai(diemDi, diemDen, trangThaiHienTai) {
    const trangThaiMoi = !trangThaiHienTai;
    const action = trangThaiMoi ? 'kích hoạt' : 'tạm dừng';
    
    if (confirm(`Bạn có chắc chắn muốn ${action} tuyến đường ${diemDi} - ${diemDen}?`)) {
        fetch('@Url.Action("CapNhatTrangThai")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            body: JSON.stringify({
                diemDi: diemDi,
                diemDen: diemDen,
                trangThai: trangThaiMoi
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            alert('Có lỗi xảy ra khi cập nhật trạng thái.');
        });
    }
}
</script>

<style>
.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group .btn {
    margin-right: 2px;
}

.badge {
    font-size: 0.8em;
}
</style>
