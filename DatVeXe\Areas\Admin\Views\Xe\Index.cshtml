@model IEnumerable<DatVeXe.Models.Xe>
@{
    ViewData["Title"] = "Quản lý xe";
}
<h2>Quản lý xe</h2>
<p>
    <a asp-action="Create" class="btn btn-primary" style="color: black; font-weight: 500;">Thêm xe mới</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>Biển số xe</th>
            <th>Loại xe</th>
            <th>Số ghế</th>
            <th><PERSON><PERSON> tả</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>@item.BienSoXe</td>
            <td>@item.LoaiXe</td>
            <td>@item.SoGhe</td>
            <td>@item.MoTa</td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.XeId">Sửa</a> |
                <a asp-action="Details" asp-route-id="@item.XeId">Chi tiết</a> |
                <a asp-action="Delete" asp-route-id="@item.XeId">Xóa</a>
            </td>
        </tr>
}
    </tbody>
</table>
