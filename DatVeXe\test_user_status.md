# Test Plan - Kiểm tra chức năng vô hiệu hóa và khóa tài khoản

## C<PERSON><PERSON> chức năng đã sửa:

1. **Logic đăng nhập thường**: Thêm kiểm tra `TrangThaiHoatDong` và `TaiKhoanBiKhoa`
2. **Logic đăng nhập Google**: Thêm kiểm tra trạng thái tài khoản
3. **Middleware kiểm tra session**: Tự động đăng xuất nếu tài khoản bị vô hiệu hóa/khóa
4. **Logic đăng ký tự động**: Kiểm tra trạng thái trước khi đăng nhập tự động

## Test Cases:

### Test Case 1: Đăng nhập với tài khoản bình thường
**Mục tiêu**: <PERSON><PERSON><PERSON> nhận tài khoản bình thường vẫn đăng nhập được
**Bước thực hiện**:
1. <PERSON><PERSON><PERSON> cậ<PERSON> http://localhost:5057/TaiKhoan/DangNhap
2. Đăng nhập với tài khoản admin: <EMAIL> / Admin@123
3. Kiểm tra có đăng nhập thành công không

**Kết quả mong đợi**: Đăng nhập thành công

### Test Case 2: Vô hiệu hóa tài khoản và test đăng nhập
**Mục tiêu**: Xác nhận tài khoản bị vô hiệu hóa không thể đăng nhập
**Bước thực hiện**:
1. Đăng nhập với tài khoản admin
2. Vào Admin > Quản lý người dùng
3. Vô hiệu hóa tài khoản "Nguyễn Văn An" (<EMAIL>)
4. Đăng xuất
5. Thử đăng nhập với tài khoản <EMAIL> / User@123

**Kết quả mong đợi**: Hiển thị thông báo "Tài khoản của bạn đã bị vô hiệu hóa"

### Test Case 3: Khóa tài khoản và test đăng nhập
**Mục tiêu**: Xác nhận tài khoản bị khóa không thể đăng nhập
**Bước thực hiện**:
1. Đăng nhập với tài khoản admin
2. Vào Admin > Quản lý người dùng
3. Khóa tài khoản "Trần Thị Bình" (<EMAIL>) với lý do "Vi phạm quy định"
4. Đăng xuất
5. Thử đăng nhập với tài khoản <EMAIL> / User@123

**Kết quả mong đợi**: Hiển thị thông báo "Tài khoản của bạn đã bị khóa. Lý do: Vi phạm quy định"

### Test Case 4: Test middleware - Vô hiệu hóa tài khoản đang đăng nhập
**Mục tiêu**: Xác nhận middleware tự động đăng xuất khi tài khoản bị vô hiệu hóa
**Bước thực hiện**:
1. Đăng nhập với tài khoản "Nguyễn Văn An" (nếu đã kích hoạt lại)
2. Mở tab mới, đăng nhập admin
3. Vô hiệu hóa tài khoản "Nguyễn Văn An"
4. Quay lại tab đầu, thực hiện một hành động bất kỳ (refresh trang, click menu)

**Kết quả mong đợi**: Tự động chuyển về trang đăng nhập với thông báo lỗi

### Test Case 5: Test middleware - Khóa tài khoản đang đăng nhập
**Mục tiêu**: Xác nhận middleware tự động đăng xuất khi tài khoản bị khóa
**Bước thực hiện**:
1. Đăng nhập với tài khoản "Trần Thị Bình" (nếu đã mở khóa)
2. Mở tab mới, đăng nhập admin
3. Khóa tài khoản "Trần Thị Bình"
4. Quay lại tab đầu, thực hiện một hành động bất kỳ

**Kết quả mong đợi**: Tự động chuyển về trang đăng nhập với thông báo lỗi

## Ghi chú:
- Tất cả các test case trên cần được thực hiện để đảm bảo chức năng hoạt động đúng
- Nếu có lỗi, cần kiểm tra console browser và server logs
- Middleware chỉ hoạt động khi có request mới, không tự động đăng xuất ngay lập tức
