@using System.Text.Json
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Thống kê đặt vé";
    var byDay = ViewBag.ByDay as IEnumerable<dynamic>;
    var byWeek = ViewBag.ByWeek as IEnumerable<dynamic>;
    var byMonth = ViewBag.ByMonth as IEnumerable<dynamic>;
    var topTrips = ViewBag.TopTrips as IEnumerable<dynamic>;
    var topSeats = ViewBag.TopSeats as IEnumerable<dynamic>;
    var cancelRate = ViewBag.CancelRate;
    var from = ((DateTime)ViewBag.From).ToString("yyyy-MM-dd");
    var to = ((DateTime)ViewBag.To).ToString("yyyy-MM-dd");
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-graph-up me-2"></i>Thống kê đặt vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Thống kê</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <i class="bi bi-funnel me-1"></i> Bộ lọc thời gian
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">Từ ngày</label>
                    <input type="date" name="from" class="form-control" value="@from" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Đến ngày</label>
                    <input type="date" name="to" class="form-control" value="@to" />
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-1"></i>Xem thống kê
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Tổng số vé</h6>
                            <h3 class="my-2" id="totalTickets">@ViewBag.TotalTickets</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-info me-2"><i class="bi bi-ticket-detailed me-1"></i>Tất cả</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-primary d-flex align-items-center justify-content-center">
                            <i class="bi bi-ticket-detailed text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Tổng doanh thu</h6>
                            <h3 class="my-2" id="totalRevenue">@ViewBag.TotalRevenue?.ToString("#,##0") VNĐ</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="bi bi-cash-coin me-1"></i>Đã thu</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-success d-flex align-items-center justify-content-center">
                            <i class="bi bi-cash-coin text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Tỷ lệ hủy vé</h6>
                            <h3 class="my-2" id="cancelRate">@String.Format("{0:0.##}", cancelRate)%</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-danger me-2"><i class="bi bi-x-circle me-1"></i>Đã hủy</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-danger d-flex align-items-center justify-content-center">
                            <i class="bi bi-x-circle text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mt-0">Vé trung bình/ngày</h6>
                            <h3 class="my-2" id="avgPerDay">@ViewBag.AvgPerDay?.ToString("F1")</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-info me-2"><i class="bi bi-calendar-day me-1"></i>Hàng ngày</span>
                            </p>
                        </div>
                        <div class="avatar-sm rounded-circle bg-info d-flex align-items-center justify-content-center">
                            <i class="bi bi-calendar-day text-white fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Daily Chart -->
        <div class="col-xl-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-bar-chart me-1"></i>Biểu đồ đặt vé theo ngày</h5>
                </div>
                <div class="card-body">
                    <canvas id="chartDay" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Status Pie Chart -->
        <div class="col-xl-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-pie-chart me-1"></i>Trạng thái vé</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly and Weekly Charts -->
    <div class="row mb-4">
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-calendar-month me-1"></i>Thống kê theo tháng</h5>
                </div>
                <div class="card-body">
                    <canvas id="chartMonth" height="150"></canvas>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-calendar-week me-1"></i>Thống kê theo tuần</h5>
                </div>
                <div class="card-body">
                    <canvas id="weeklyChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Lists -->
    <div class="row mb-4">
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-trophy me-1"></i>Top 5 chuyến xe được đặt nhiều nhất</h5>
                </div>
                <div class="card-body">
                    @if (topTrips != null)
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var trip in topTrips)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Chuyến xe #@trip.ChuyenXeId</strong>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">@trip.Count vé</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">Không có dữ liệu</p>
                    }
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0"><i class="bi bi-geo-alt me-1"></i>Top 5 ghế được chọn nhiều nhất</h5>
                </div>
                <div class="card-body">
                    @if (topSeats != null)
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var seat in topSeats)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Ghế #@seat.ChoNgoiId</strong>
                                    </div>
                                    <span class="badge bg-success rounded-pill">@seat.Count lần</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">Không có dữ liệu</p>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Daily Chart
        const dailyData = @Html.Raw(JsonSerializer.Serialize(byDay ?? new List<object>()));
        const dailyCtx = document.getElementById('chartDay').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: dailyData.map(d => new Date(d.Date).toLocaleDateString('vi-VN')),
                datasets: [{
                    label: 'Số vé đặt',
                    data: dailyData.map(d => d.Total),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Status Pie Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Đã đặt', 'Đã thanh toán', 'Đã sử dụng', 'Đã hủy', 'Đã hoàn thành'],
                datasets: [{
                    data: [@ViewBag.StatusCounts?.DaDat ?? 0, @ViewBag.StatusCounts?.DaThanhToan ?? 0, @ViewBag.StatusCounts?.DaSuDung ?? 0, @ViewBag.StatusCounts?.DaHuy ?? 0, @ViewBag.StatusCounts?.DaHoanThanh ?? 0],
                    backgroundColor: [
                        '#ffc107',
                        '#28a745',
                        '#17a2b8',
                        '#dc3545',
                        '#6f42c1'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Monthly Chart
        const monthlyData = @Html.Raw(JsonSerializer.Serialize(byMonth ?? new List<object>()));
        const monthlyCtx = document.getElementById('chartMonth').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: monthlyData.map(d => `${d.Month}/${d.Year}`),
                datasets: [{
                    label: 'Số vé đặt',
                    data: monthlyData.map(d => d.Total),
                    backgroundColor: 'rgba(54, 162, 235, 0.8)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Weekly Chart
        const weeklyData = @Html.Raw(JsonSerializer.Serialize(byWeek ?? new List<object>()));
        const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
        new Chart(weeklyCtx, {
            type: 'bar',
            data: {
                labels: weeklyData.map(d => `Tuần ${d.Week}`),
                datasets: [{
                    label: 'Số vé đặt',
                    data: weeklyData.map(d => d.Total),
                    backgroundColor: 'rgba(255, 99, 132, 0.8)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>

    <style>
        .avatar-sm {
            height: 3rem;
            width: 3rem;
            font-size: 1.25rem;
        }

        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            border: none;
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
        }
    </style>
}
