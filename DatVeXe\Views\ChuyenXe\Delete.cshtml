@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Xóa chuyến xe";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> @ViewData["Title"]
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle"></i>
                        Bạn có chắc chắn muốn xóa chuyến xe này? Hành động này không thể hoàn tác.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3">Thông tin chuyến xe</h6>
                            <dl class="row">
                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.DiemDi)</dt>
                                <dd class="col-sm-8">@Model.DiemDi</dd>

                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.DiemDen)</dt>
                                <dd class="col-sm-8">@Model.DiemDen</dd>

                                <dt class="col-sm-4">@Html.DisplayNameFor(model => model.NgayKhoiHanh)</dt>
                                <dd class="col-sm-8">@Model.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3">Thông tin xe</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Biển số xe</dt>
                                <dd class="col-sm-8">@Model.Xe.BienSo</dd>

                                <dt class="col-sm-4">Loại xe</dt>
                                <dd class="col-sm-8">@Model.Xe.LoaiXe</dd>

                                <dt class="col-sm-4">Số ghế</dt>
                                <dd class="col-sm-8">@Model.Xe.SoGhe chỗ</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-4">
                        <form asp-action="Delete" method="post">
                            <input type="hidden" asp-for="ChuyenXeId" />
                            <div class="btn-group">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Xác nhận xóa
                                </button>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Quay lại
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        dt {
            font-weight: 600;
        }
    </style>
} 