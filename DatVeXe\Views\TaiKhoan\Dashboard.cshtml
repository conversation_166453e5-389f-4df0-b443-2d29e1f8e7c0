@using DatVeXe.Models
@{
    ViewData["Title"] = "Dashboard - Tài khoản của tôi";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="bi bi-person-circle" style="font-size: 4rem; color: #6CABDD;"></i>
                    </div>
                    <h5 class="card-title">@ViewBag.User.HoTen</h5>
                    <p class="text-muted">@ViewBag.User.Email</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-list me-2"></i>Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="@Url.Action("Dashboard", "TaiKhoan")" class="list-group-item list-group-item-action active">
                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("Profile", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-gear me-2"></i>Cập nhật thông tin
                    </a>
                    <a href="@Url.Action("Search", "Booking")" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                    <a href="@Url.Action("Index", "MyTickets")" class="list-group-item list-group-item-action">
                        <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                    </a>
                    <a href="@Url.Action("Index", "Contacts")" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-lines-fill me-2"></i>Danh bạ liên hệ
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Welcome Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <h4 class="card-title">
                        <i class="bi bi-house-heart me-2 text-primary"></i>
                        Chào mừng trở lại, @ViewBag.User.HoTen!
                    </h4>
                    <p class="text-muted">Quản lý thông tin cá nhân và đặt vé xe tại đây.</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <i class="bi bi-ticket-perforated text-primary" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.TotalTickets</h4>
                            <p class="text-muted">Tổng booking</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.UpcomingTrips</h4>
                            <p class="text-muted">Chuyến sắp tới</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <i class="bi bi-check-circle text-info" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.CompletedTrips</h4>
                            <p class="text-muted">Đã hoàn thành</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <i class="bi bi-currency-dollar text-warning" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">@ViewBag.TotalSpent.ToString("N0")</h4>
                            <p class="text-muted">Tổng chi tiêu (VNĐ)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Tickets -->
            @if (ViewBag.RecentTickets != null && ((List<Ve>)ViewBag.RecentTickets).Any())
            {
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            Vé gần đây
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mã vé</th>
                                        <th>Tuyến đường</th>
                                        <th>Ngày khởi hành</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var ticket in (List<Ve>)ViewBag.RecentTickets)
                                    {
                                        <tr>
                                            <td><strong>@ticket.MaVe</strong></td>
                                            <td>
                                                @if (ticket.ChuyenXe?.TuyenDuong != null)
                                                {
                                                    @ticket.ChuyenXe.TuyenDuong.DiemDi <i class="bi bi-arrow-right"></i> @ticket.ChuyenXe.TuyenDuong.DiemDen
                                                }
                                                else
                                                {
                                                    @ticket.ChuyenXe?.DiemDi <i class="bi bi-arrow-right"></i> @ticket.ChuyenXe?.DiemDen
                                                }
                                            </td>
                                            <td>@ticket.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>
                                                @if (ticket.VeTrangThai == TrangThaiVe.DaDat)
                                                {
                                                    <span class="badge bg-success">Đã đặt</span>
                                                }
                                                else if (ticket.VeTrangThai == TrangThaiVe.DaHuy)
                                                {
                                                    <span class="badge bg-danger">Đã hủy</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">Chờ xử lý</span>
                                                }
                                            </td>
                                            <td>
                                                <a href="@Url.Action("Details", "MyTickets", new { id = ticket.VeId })" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Xem
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="@Url.Action("Index", "MyTickets")" class="btn btn-outline-primary">
                                <i class="bi bi-list me-2"></i>Xem tất cả vé
                            </a>
                        </div>
                    </div>
                </div>
            }

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>
                        Thao tác nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="@Url.Action("Search", "Booking")" class="btn btn-primary btn-lg">
                                    <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="@Url.Action("Index", "MyTickets")" class="btn btn-outline-success btn-lg">
                                    <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="@Url.Action("Index", "Contacts")" class="btn btn-outline-info btn-lg">
                                    <i class="bi bi-person-lines-fill me-2"></i>Danh bạ
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <p class="text-muted">Sử dụng hệ thống booking để đặt vé xe bus một cách dễ dàng!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
    }
    
    .user-avatar {
        margin-bottom: 1rem;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #eee;
    }
    
    .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    .list-group-item.active {
        background-color: #6CABDD;
        border-color: #6CABDD;
    }
    
    .border-primary { border-color: #6CABDD !important; }
    .text-primary { color: #6CABDD !important; }
    .bg-primary { background-color: #6CABDD !important; }
    .btn-primary { background-color: #6CABDD; border-color: #6CABDD; }
    .btn-primary:hover { background-color: #5a9bc4; border-color: #5a9bc4; }
</style>
