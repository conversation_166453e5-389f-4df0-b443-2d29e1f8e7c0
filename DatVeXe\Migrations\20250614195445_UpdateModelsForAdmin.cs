﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class UpdateModelsForAdmin : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "BienS<PERSON>",
                table: "Xes",
                newName: "BienSoXe");

            migrationBuilder.RenameColumn(
                name: "Trang<PERSON><PERSON>",
                table: "Ves",
                newName: "VeTrangThai");

            migrationBuilder.RenameColumn(
                name: "SoLuong",
                table: "KhuyenMais",
                newName: "SoLuongToiDa");

            migrationBuilder.RenameColumn(
                name: "Da<PERSON>u<PERSON><PERSON>",
                table: "KhuyenMais",
                newName: "SoLuongDaSuDung");

            migrationBuilder.RenameColumn(
                name: "NoiDungDanhGia",
                table: "DanhGiaChuyenDis",
                newName: "NoiD<PERSON>");

            migrationBuilder.RenameColumn(
                name: "DiemDanh<PERSON><PERSON>",
                table: "DanhGiaChuyenDis",
                newName: "DiemSo");

            migrationBuilder.AddColumn<string>(
                name: "MauSac",
                table: "Xes",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NamSanXuat",
                table: "Xes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "Xes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NhaSanXuat",
                table: "Xes",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "GiaVe",
                table: "TuyenDuongs",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "TuyenDuongs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LyDoKhoa",
                table: "NguoiDungs",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "NguoiDungs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "TaiKhoanBiKhoa",
                table: "NguoiDungs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "KhuyenMais",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SoLanSuDungToiDa",
                table: "KhuyenMais",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "BiAn",
                table: "DanhGiaChuyenDis",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "LyDoAn",
                table: "DanhGiaChuyenDis",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayAn",
                table: "DanhGiaChuyenDis",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LyDoTuChoi",
                table: "ChuyenXes",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "ChuyenXes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayDuyet",
                table: "ChuyenXes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TrangThaiDuyet",
                table: "ChuyenXes",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 1,
                columns: new[] { "LyDoKhoa", "NgayCapNhat", "TaiKhoanBiKhoa" },
                values: new object[] { null, null, false });

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 2,
                columns: new[] { "LyDoKhoa", "NgayCapNhat", "TaiKhoanBiKhoa" },
                values: new object[] { null, null, false });

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 3,
                columns: new[] { "LyDoKhoa", "NgayCapNhat", "TaiKhoanBiKhoa" },
                values: new object[] { null, null, false });

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 1,
                columns: new[] { "GiaVe", "NgayCapNhat" },
                values: new object[] { 0m, null });

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 2,
                columns: new[] { "GiaVe", "NgayCapNhat" },
                values: new object[] { 0m, null });

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 3,
                columns: new[] { "GiaVe", "NgayCapNhat" },
                values: new object[] { 0m, null });

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 4,
                columns: new[] { "GiaVe", "NgayCapNhat" },
                values: new object[] { 0m, null });

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 5,
                columns: new[] { "GiaVe", "NgayCapNhat" },
                values: new object[] { 0m, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 1,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 2,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 3,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 4,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 5,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 6,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 7,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 8,
                columns: new[] { "MauSac", "NamSanXuat", "NgayCapNhat", "NhaSanXuat" },
                values: new object[] { null, null, null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MauSac",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NamSanXuat",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NhaSanXuat",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "GiaVe",
                table: "TuyenDuongs");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "TuyenDuongs");

            migrationBuilder.DropColumn(
                name: "LyDoKhoa",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "TaiKhoanBiKhoa",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "SoLanSuDungToiDa",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "BiAn",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "LyDoAn",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "NgayAn",
                table: "DanhGiaChuyenDis");

            migrationBuilder.DropColumn(
                name: "LyDoTuChoi",
                table: "ChuyenXes");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "ChuyenXes");

            migrationBuilder.DropColumn(
                name: "NgayDuyet",
                table: "ChuyenXes");

            migrationBuilder.DropColumn(
                name: "TrangThaiDuyet",
                table: "ChuyenXes");

            migrationBuilder.RenameColumn(
                name: "BienSoXe",
                table: "Xes",
                newName: "BienSo");

            migrationBuilder.RenameColumn(
                name: "VeTrangThai",
                table: "Ves",
                newName: "TrangThai");

            migrationBuilder.RenameColumn(
                name: "SoLuongToiDa",
                table: "KhuyenMais",
                newName: "SoLuong");

            migrationBuilder.RenameColumn(
                name: "SoLuongDaSuDung",
                table: "KhuyenMais",
                newName: "DaSuDung");

            migrationBuilder.RenameColumn(
                name: "NoiDung",
                table: "DanhGiaChuyenDis",
                newName: "NoiDungDanhGia");

            migrationBuilder.RenameColumn(
                name: "DiemSo",
                table: "DanhGiaChuyenDis",
                newName: "DiemDanhGia");
        }
    }
}
