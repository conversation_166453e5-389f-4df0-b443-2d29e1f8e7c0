@model IEnumerable<DatVeXe.Models.Xe>

@{
    ViewData["Title"] = "Quản lý xe";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-bus"></i> @ViewData["Title"]
                    </h3>
                    @if (Context.Session.GetInt32("IsAdmin") == 1)
                    {
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Thêm xe mới
                        </a>
                    }
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <i class="fas fa-id-card"></i> @Html.DisplayNameFor(model => model.BienSo)
                                    </th>
                                    <th>
                                        <i class="fas fa-bus"></i> @Html.DisplayNameFor(model => model.LoaiXe)
                                    </th>
                                    <th>
                                        <i class="fas fa-chair"></i> @Html.DisplayNameFor(model => model.SoGhe)
                                    </th>
                                    <th>
                                        <i class="fas fa-route"></i> Số chuyến
                                    </th>
                                    <th>
                                        <i class="fas fa-chart-line"></i> Tỷ lệ lấp đầy
                                    </th>
                                    <th>
                                        <i class="fas fa-cogs"></i> Thao tác
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var item in Model)
                                    {
                                        var soChuyenXe = item.ChuyenXes?.Count ?? 0;
                                        var tongVe = item.ChuyenXes?.Sum(c => c.Ves?.Count ?? 0) ?? 0;
                                        var tyLeLapDay = soChuyenXe > 0 ? (double)tongVe / (soChuyenXe * item.SoGhe) * 100 : 0;

                                        <tr>
                                            <td>
                                                <strong class="text-primary">@Html.DisplayFor(modelItem => item.BienSo)</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@Html.DisplayFor(modelItem => item.LoaiXe)</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@Html.DisplayFor(modelItem => item.SoGhe) chỗ</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@soChuyenXe chuyến</span>
                                            </td>
                                            <td>
                                                @if (tyLeLapDay > 0)
                                                {
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar @(tyLeLapDay >= 80 ? "bg-success" : tyLeLapDay >= 50 ? "bg-warning" : "bg-danger")"
                                                             role="progressbar" style="width: @tyLeLapDay.ToString("F1")%"
                                                             aria-valuenow="@tyLeLapDay.ToString("F1")" aria-valuemin="0" aria-valuemax="100">
                                                            @tyLeLapDay.ToString("F1")%
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa có dữ liệu</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a asp-action="Details" asp-route-id="@item.XeId"
                                                       class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                        <i class="fas fa-info-circle"></i>
                                                    </a>
                                                    @if (Context.Session.GetInt32("IsAdmin") == 1)
                                                    {
                                                        <a asp-action="Edit" asp-route-id="@item.XeId"
                                                           class="btn btn-outline-warning btn-sm" title="Chỉnh sửa">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        @if (soChuyenXe == 0)
                                                        {
                                                            <a asp-action="Delete" asp-route-id="@item.XeId"
                                                               class="btn btn-outline-danger btn-sm" title="Xóa">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        }
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-bus fa-3x mb-3"></i>
                                            <p>Chưa có xe nào trong hệ thống</p>
                                            @if (Context.Session.GetInt32("IsAdmin") == 1)
                                            {
                                                <a asp-action="Create" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> Thêm xe đầu tiên
                                                </a>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
