﻿// <auto-generated />
using System;
using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DatVeXe.Migrations
{
    [DbContext(typeof(DatVeXeContext))]
    [Migration("20250614195445_UpdateModelsForAdmin")]
    partial class UpdateModelsForAdmin
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("DatVeXe.Models.ChoNgoi", b =>
                {
                    b.Property<int>("ChoNgoiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ChoNgoiId"));

                    b.Property<int>("Cot")
                        .HasColumnType("int");

                    b.Property<int>("Hang")
                        .HasColumnType("int");

                    b.Property<string>("LoaiGhe")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SoGhe")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<bool>("TrangThaiHoatDong")
                        .HasColumnType("bit");

                    b.Property<int>("XeId")
                        .HasColumnType("int");

                    b.HasKey("ChoNgoiId");

                    b.HasIndex("XeId", "SoGhe")
                        .IsUnique();

                    b.ToTable("ChoNgois");
                });

            modelBuilder.Entity("DatVeXe.Models.ChuyenXe", b =>
                {
                    b.Property<int>("ChuyenXeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ChuyenXeId"));

                    b.Property<string>("DiemDen")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DiemDi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal>("Gia")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("LyDoTuChoi")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayDuyet")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayKhoiHanh")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<int?>("TaiXeId")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("ThoiGianDi")
                        .HasColumnType("time");

                    b.Property<bool>("TrangThai")
                        .HasColumnType("bit");

                    b.Property<int>("TrangThaiChuyenXe")
                        .HasColumnType("int");

                    b.Property<int>("TrangThaiDuyet")
                        .HasColumnType("int");

                    b.Property<int?>("TuyenDuongId")
                        .HasColumnType("int");

                    b.Property<int>("XeId")
                        .HasColumnType("int");

                    b.HasKey("ChuyenXeId");

                    b.HasIndex("TaiXeId");

                    b.HasIndex("TuyenDuongId");

                    b.HasIndex("XeId");

                    b.ToTable("ChuyenXes");
                });

            modelBuilder.Entity("DatVeXe.Models.DanhBaHanhKhach", b =>
                {
                    b.Property<int>("DanhBaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DanhBaId"));

                    b.Property<string>("CCCD")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("DiaChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("LanSuDungCuoi")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("int");

                    b.Property<string>("SoDienThoai")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("SoLanSuDung")
                        .HasColumnType("int");

                    b.Property<string>("TenHanhKhach")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("DanhBaId");

                    b.HasIndex("NguoiDungId", "SoDienThoai");

                    b.ToTable("DanhBaHanhKhachs");
                });

            modelBuilder.Entity("DatVeXe.Models.DanhGiaChuyenDi", b =>
                {
                    b.Property<int>("DanhGiaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DanhGiaId"));

                    b.Property<bool>("BiAn")
                        .HasColumnType("bit");

                    b.Property<bool>("CoKhuyenNghi")
                        .HasColumnType("bit");

                    b.Property<int?>("DanhGiaDichVu")
                        .HasColumnType("int");

                    b.Property<int?>("DanhGiaTaiXe")
                        .HasColumnType("int");

                    b.Property<int?>("DanhGiaXe")
                        .HasColumnType("int");

                    b.Property<int>("DiemSo")
                        .HasColumnType("int");

                    b.Property<string>("LyDoAn")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("NgayAn")
                        .HasColumnType("datetime2");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("int");

                    b.Property<string>("NoiDung")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("ThoiGianDanhGia")
                        .HasColumnType("datetime2");

                    b.Property<int>("VeId")
                        .HasColumnType("int");

                    b.HasKey("DanhGiaId");

                    b.HasIndex("NguoiDungId");

                    b.HasIndex("VeId");

                    b.ToTable("DanhGiaChuyenDis");
                });

            modelBuilder.Entity("DatVeXe.Models.KhuyenMai", b =>
                {
                    b.Property<int>("KhuyenMaiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("KhuyenMaiId"));

                    b.Property<decimal>("GiaTri")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GiaTriDonHangToiThieu")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GiaTriToiDa")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("LoaiKhuyenMai")
                        .HasColumnType("int");

                    b.Property<string>("MaKhuyenMai")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("MoTa")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("NgayBatDau")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayKetThuc")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SoLanSuDungToiDa")
                        .HasColumnType("int");

                    b.Property<int>("SoLuongDaSuDung")
                        .HasColumnType("int");

                    b.Property<int?>("SoLuongToiDa")
                        .HasColumnType("int");

                    b.Property<string>("TenKhuyenMai")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("TrangThaiHoatDong")
                        .HasColumnType("bit");

                    b.HasKey("KhuyenMaiId");

                    b.HasIndex("MaKhuyenMai")
                        .IsUnique();

                    b.HasIndex("NgayBatDau", "NgayKetThuc");

                    b.ToTable("KhuyenMais");
                });

            modelBuilder.Entity("DatVeXe.Models.NguoiDung", b =>
                {
                    b.Property<int>("NguoiDungId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("NguoiDungId"));

                    b.Property<string>("DiaChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("HoTen")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("LaAdmin")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LanDangNhapCuoi")
                        .HasColumnType("datetime2");

                    b.Property<string>("LyDoKhoa")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayDangKy")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("datetime2");

                    b.Property<string>("SoDienThoai")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<bool>("TaiKhoanBiKhoa")
                        .HasColumnType("bit");

                    b.Property<bool>("TrangThaiHoatDong")
                        .HasColumnType("bit");

                    b.HasKey("NguoiDungId");

                    b.ToTable("NguoiDungs");

                    b.HasData(
                        new
                        {
                            NguoiDungId = 1,
                            DiaChi = "123 Nguyễn Huệ, Q1, TP.HCM",
                            Email = "<EMAIL>",
                            GioiTinh = "Nam",
                            HoTen = "Administrator",
                            LaAdmin = true,
                            LanDangNhapCuoi = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            MatKhau = "e86f78a8a3caf0b60d8e74e5942aa6d86dc150cd3c03338aef25b7d2d7e3acc7",
                            NgayDangKy = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1990, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoDienThoai = "0901234567",
                            TaiKhoanBiKhoa = false,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            NguoiDungId = 2,
                            DiaChi = "456 Lê Lợi, Q1, TP.HCM",
                            Email = "<EMAIL>",
                            GioiTinh = "Nam",
                            HoTen = "Nguyễn Văn An",
                            LaAdmin = false,
                            MatKhau = "3e7c19576488862816f13b512cacf3e4ba97dd97243ea0bd6a2ad1642d86ba72",
                            NgayDangKy = new DateTime(2024, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1995, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoDienThoai = "0987654321",
                            TaiKhoanBiKhoa = false,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            NguoiDungId = 3,
                            DiaChi = "789 Trần Hưng Đạo, Q5, TP.HCM",
                            Email = "<EMAIL>",
                            GioiTinh = "Nữ",
                            HoTen = "Trần Thị Bình",
                            LaAdmin = false,
                            MatKhau = "3e7c19576488862816f13b512cacf3e4ba97dd97243ea0bd6a2ad1642d86ba72",
                            NgayDangKy = new DateTime(2024, 11, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1992, 8, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoDienThoai = "0912345678",
                            TaiKhoanBiKhoa = false,
                            TrangThaiHoatDong = true
                        });
                });

            modelBuilder.Entity("DatVeXe.Models.SeatReservation", b =>
                {
                    b.Property<int>("ReservationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ReservationId"));

                    b.Property<int>("ChoNgoiId")
                        .HasColumnType("int");

                    b.Property<int>("ChuyenXeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("ReservedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("SessionId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("ReservationId");

                    b.HasIndex("ChoNgoiId");

                    b.HasIndex("ExpiresAt");

                    b.HasIndex("ChuyenXeId", "ChoNgoiId", "IsActive");

                    b.ToTable("SeatReservations");
                });

            modelBuilder.Entity("DatVeXe.Models.TaiXe", b =>
                {
                    b.Property<int>("TaiXeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TaiXeId"));

                    b.Property<string>("CMND")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("DiaChi")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("HoTen")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("KinhNghiem")
                        .HasColumnType("int");

                    b.Property<int>("LoaiBangLai")
                        .HasColumnType("int");

                    b.Property<DateTime?>("NgayCapBangLai")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayHetHanBangLai")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayVaoLam")
                        .HasColumnType("datetime2");

                    b.Property<string>("SoBangLai")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SoDienThoai")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("TrangThai")
                        .HasColumnType("int");

                    b.HasKey("TaiXeId");

                    b.HasIndex("CMND")
                        .IsUnique();

                    b.HasIndex("SoBangLai")
                        .IsUnique();

                    b.HasIndex("SoDienThoai");

                    b.ToTable("TaiXes");

                    b.HasData(
                        new
                        {
                            TaiXeId = 1,
                            CMND = "025123456789",
                            DiaChi = "123 Lê Lợi, Q1, TP.HCM",
                            GioiTinh = "Nam",
                            HoTen = "Nguyễn Văn Tài",
                            KinhNghiem = 14,
                            LoaiBangLai = 4,
                            NgayCapBangLai = new DateTime(2010, 5, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayHetHanBangLai = new DateTime(2030, 5, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1985, 3, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayVaoLam = new DateTime(2020, 1, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoBangLai = "BL001234567",
                            SoDienThoai = "0901111111",
                            TrangThai = 1
                        },
                        new
                        {
                            TaiXeId = 2,
                            CMND = "025987654321",
                            DiaChi = "456 Nguyễn Huệ, Q1, TP.HCM",
                            GioiTinh = "Nam",
                            HoTen = "Trần Minh Đức",
                            KinhNghiem = 12,
                            LoaiBangLai = 4,
                            NgayCapBangLai = new DateTime(2012, 8, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayHetHanBangLai = new DateTime(2032, 8, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1988, 7, 22, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayVaoLam = new DateTime(2021, 3, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoBangLai = "BL002345678",
                            SoDienThoai = "0902222222",
                            TrangThai = 1
                        },
                        new
                        {
                            TaiXeId = 3,
                            CMND = "025456789123",
                            DiaChi = "789 Trần Hưng Đạo, Q5, TP.HCM",
                            GioiTinh = "Nam",
                            HoTen = "Lê Hoàng Nam",
                            KinhNghiem = 9,
                            LoaiBangLai = 4,
                            NgayCapBangLai = new DateTime(2015, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayHetHanBangLai = new DateTime(2035, 2, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgaySinh = new DateTime(1990, 12, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NgayVaoLam = new DateTime(2022, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            SoBangLai = "BL003456789",
                            SoDienThoai = "0903333333",
                            TrangThai = 1
                        });
                });

            modelBuilder.Entity("DatVeXe.Models.ThanhToan", b =>
                {
                    b.Property<int>("ThanhToanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ThanhToanId"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MaGiaoDich")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MaPhanHoi")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("NgayThanhToan")
                        .HasColumnType("datetime2");

                    b.Property<int>("PhuongThucThanhToan")
                        .HasColumnType("int");

                    b.Property<decimal>("SoTien")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("ThoiGianTao")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ThoiGianThanhToan")
                        .HasColumnType("datetime2");

                    b.Property<string>("ThongTinPhanHoi")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("TrangThai")
                        .HasColumnType("int");

                    b.Property<int>("VeId")
                        .HasColumnType("int");

                    b.Property<int?>("VeId1")
                        .HasColumnType("int");

                    b.HasKey("ThanhToanId");

                    b.HasIndex("MaGiaoDich")
                        .IsUnique();

                    b.HasIndex("VeId1");

                    b.HasIndex("VeId", "TrangThai");

                    b.ToTable("ThanhToans");
                });

            modelBuilder.Entity("DatVeXe.Models.TuyenDuong", b =>
                {
                    b.Property<int>("TuyenDuongId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TuyenDuongId"));

                    b.Property<string>("DiemDen")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DiemDi")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("GiaVe")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("KhoangCach")
                        .HasColumnType("int");

                    b.Property<string>("MoTa")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("TenTuyen")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<TimeSpan>("ThoiGianDuKien")
                        .HasColumnType("time");

                    b.Property<bool>("TrangThaiHoatDong")
                        .HasColumnType("bit");

                    b.HasKey("TuyenDuongId");

                    b.HasIndex("DiemDi", "DiemDen")
                        .IsUnique();

                    b.ToTable("TuyenDuongs");

                    b.HasData(
                        new
                        {
                            TuyenDuongId = 1,
                            DiemDen = "Nha Trang",
                            DiemDi = "TP.HCM",
                            GiaVe = 0m,
                            KhoangCach = 450,
                            MoTa = "Tuyến đường du lịch nổi tiếng, phong cảnh đẹp",
                            NgayTao = new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TenTuyen = "TP.HCM - Nha Trang",
                            ThoiGianDuKien = new TimeSpan(0, 8, 30, 0, 0),
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            TuyenDuongId = 2,
                            DiemDen = "Đà Lạt",
                            DiemDi = "TP.HCM",
                            GiaVe = 0m,
                            KhoangCach = 300,
                            MoTa = "Tuyến đường lên thành phố ngàn hoa",
                            NgayTao = new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TenTuyen = "TP.HCM - Đà Lạt",
                            ThoiGianDuKien = new TimeSpan(0, 6, 0, 0, 0),
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            TuyenDuongId = 3,
                            DiemDen = "Vũng Tàu",
                            DiemDi = "TP.HCM",
                            GiaVe = 0m,
                            KhoangCach = 125,
                            MoTa = "Tuyến đường ngắn đến biển Vũng Tàu",
                            NgayTao = new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TenTuyen = "TP.HCM - Vũng Tàu",
                            ThoiGianDuKien = new TimeSpan(0, 2, 30, 0, 0),
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            TuyenDuongId = 4,
                            DiemDen = "Cần Thơ",
                            DiemDi = "TP.HCM",
                            GiaVe = 0m,
                            KhoangCach = 170,
                            MoTa = "Tuyến đường đến miền Tây sông nước",
                            NgayTao = new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TenTuyen = "TP.HCM - Cần Thơ",
                            ThoiGianDuKien = new TimeSpan(0, 3, 30, 0, 0),
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            TuyenDuongId = 5,
                            DiemDen = "Hạ Long",
                            DiemDi = "Hà Nội",
                            GiaVe = 0m,
                            KhoangCach = 165,
                            MoTa = "Tuyến đường đến vịnh Hạ Long",
                            NgayTao = new DateTime(2024, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TenTuyen = "Hà Nội - Hạ Long",
                            ThoiGianDuKien = new TimeSpan(0, 3, 0, 0, 0),
                            TrangThaiHoatDong = true
                        });
                });

            modelBuilder.Entity("DatVeXe.Models.Ve", b =>
                {
                    b.Property<int>("VeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VeId"));

                    b.Property<int?>("ChoNgoiId")
                        .HasColumnType("int");

                    b.Property<int>("ChuyenXeId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal>("GiaVe")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("LyDoHuy")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MaVe")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("NgayDat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("NgayHuy")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NguoiDungId")
                        .HasColumnType("int");

                    b.Property<string>("SoDienThoai")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TenKhach")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("VeTrangThai")
                        .HasColumnType("int");

                    b.HasKey("VeId");

                    b.HasIndex("ChoNgoiId");

                    b.HasIndex("ChuyenXeId");

                    b.HasIndex("NguoiDungId");

                    b.ToTable("Ves");
                });

            modelBuilder.Entity("DatVeXe.Models.Xe", b =>
                {
                    b.Property<int>("XeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("XeId"));

                    b.Property<string>("BienSoXe")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LoaiXe")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MauSac")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MoTa")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("NamSanXuat")
                        .HasColumnType("int");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("datetime2");

                    b.Property<string>("NhaSanXuat")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NhaXe")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SoGhe")
                        .HasColumnType("int");

                    b.Property<bool>("TrangThaiHoatDong")
                        .HasColumnType("bit");

                    b.HasKey("XeId");

                    b.ToTable("Xes");

                    b.HasData(
                        new
                        {
                            XeId = 1,
                            BienSoXe = "51A-12345",
                            LoaiXe = "Giường nằm 40 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Phương Trang",
                            SoGhe = 40,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 2,
                            BienSoXe = "51B-67890",
                            LoaiXe = "Limousine 20 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Mai Linh",
                            SoGhe = 20,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 3,
                            BienSoXe = "51C-11111",
                            LoaiXe = "Ghế ngồi 45 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Thành Bưởi",
                            SoGhe = 45,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 4,
                            BienSoXe = "51D-22222",
                            LoaiXe = "Giường nằm 36 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Phương Trang",
                            SoGhe = 36,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 5,
                            BienSoXe = "51E-33333",
                            LoaiXe = "Limousine 16 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Hoàng Long",
                            SoGhe = 16,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 6,
                            BienSoXe = "30A-44444",
                            LoaiXe = "Ghế ngồi 50 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Mai Linh",
                            SoGhe = 50,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 7,
                            BienSoXe = "30B-55555",
                            LoaiXe = "Giường nằm 32 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Thành Bưởi",
                            SoGhe = 32,
                            TrangThaiHoatDong = true
                        },
                        new
                        {
                            XeId = 8,
                            BienSoXe = "30C-66666",
                            LoaiXe = "Limousine 24 chỗ",
                            NgayTao = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            NhaXe = "Hoàng Long",
                            SoGhe = 24,
                            TrangThaiHoatDong = true
                        });
                });

            modelBuilder.Entity("DatVeXe.Models.ChoNgoi", b =>
                {
                    b.HasOne("DatVeXe.Models.Xe", "Xe")
                        .WithMany("ChoNgois")
                        .HasForeignKey("XeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Xe");
                });

            modelBuilder.Entity("DatVeXe.Models.ChuyenXe", b =>
                {
                    b.HasOne("DatVeXe.Models.TaiXe", "TaiXe")
                        .WithMany("ChuyenXes")
                        .HasForeignKey("TaiXeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("DatVeXe.Models.TuyenDuong", "TuyenDuong")
                        .WithMany("ChuyenXes")
                        .HasForeignKey("TuyenDuongId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("DatVeXe.Models.Xe", "Xe")
                        .WithMany("ChuyenXes")
                        .HasForeignKey("XeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TaiXe");

                    b.Navigation("TuyenDuong");

                    b.Navigation("Xe");
                });

            modelBuilder.Entity("DatVeXe.Models.DanhBaHanhKhach", b =>
                {
                    b.HasOne("DatVeXe.Models.NguoiDung", "NguoiDung")
                        .WithMany()
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("DatVeXe.Models.DanhGiaChuyenDi", b =>
                {
                    b.HasOne("DatVeXe.Models.NguoiDung", "NguoiDung")
                        .WithMany()
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DatVeXe.Models.Ve", "Ve")
                        .WithMany()
                        .HasForeignKey("VeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NguoiDung");

                    b.Navigation("Ve");
                });

            modelBuilder.Entity("DatVeXe.Models.SeatReservation", b =>
                {
                    b.HasOne("DatVeXe.Models.ChoNgoi", "ChoNgoi")
                        .WithMany()
                        .HasForeignKey("ChoNgoiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DatVeXe.Models.ChuyenXe", "ChuyenXe")
                        .WithMany()
                        .HasForeignKey("ChuyenXeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChoNgoi");

                    b.Navigation("ChuyenXe");
                });

            modelBuilder.Entity("DatVeXe.Models.ThanhToan", b =>
                {
                    b.HasOne("DatVeXe.Models.Ve", "Ve")
                        .WithMany()
                        .HasForeignKey("VeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DatVeXe.Models.Ve", null)
                        .WithMany("ThanhToans")
                        .HasForeignKey("VeId1");

                    b.Navigation("Ve");
                });

            modelBuilder.Entity("DatVeXe.Models.Ve", b =>
                {
                    b.HasOne("DatVeXe.Models.ChoNgoi", "ChoNgoi")
                        .WithMany("Ves")
                        .HasForeignKey("ChoNgoiId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("DatVeXe.Models.ChuyenXe", "ChuyenXe")
                        .WithMany("Ves")
                        .HasForeignKey("ChuyenXeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DatVeXe.Models.NguoiDung", "NguoiDung")
                        .WithMany("Ves")
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ChoNgoi");

                    b.Navigation("ChuyenXe");

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("DatVeXe.Models.ChoNgoi", b =>
                {
                    b.Navigation("Ves");
                });

            modelBuilder.Entity("DatVeXe.Models.ChuyenXe", b =>
                {
                    b.Navigation("Ves");
                });

            modelBuilder.Entity("DatVeXe.Models.NguoiDung", b =>
                {
                    b.Navigation("Ves");
                });

            modelBuilder.Entity("DatVeXe.Models.TaiXe", b =>
                {
                    b.Navigation("ChuyenXes");
                });

            modelBuilder.Entity("DatVeXe.Models.TuyenDuong", b =>
                {
                    b.Navigation("ChuyenXes");
                });

            modelBuilder.Entity("DatVeXe.Models.Ve", b =>
                {
                    b.Navigation("ThanhToans");
                });

            modelBuilder.Entity("DatVeXe.Models.Xe", b =>
                {
                    b.Navigation("ChoNgois");

                    b.Navigation("ChuyenXes");
                });
#pragma warning restore 612, 618
        }
    }
}
