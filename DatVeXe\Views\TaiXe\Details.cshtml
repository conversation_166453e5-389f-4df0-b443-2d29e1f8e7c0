@model DatVeXe.Models.TaiXe

@{
    ViewData["Title"] = "Chi tiết Tài xế";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user mr-2"></i>
                        Chi tiết Tài xế: @Model.HoTen
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.TaiXeId" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Thông tin cá nhân -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> Thông tin cá nhân</h5>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Họ tên:</strong></td>
                                    <td>@Html.DisplayFor(model => model.HoTen)</td>
                                </tr>
                                <tr>
                                    <td><strong>Số điện thoại:</strong></td>
                                    <td>
                                        <a href="tel:@Model.SoDienThoai">@Html.DisplayFor(model => model.SoDienThoai)</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Địa chỉ:</strong></td>
                                    <td>@Html.DisplayFor(model => model.DiaChi)</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày sinh:</strong></td>
                                    <td>
                                        @if (Model.NgaySinh.HasValue)
                                        {
                                            @Model.NgaySinh.Value.ToString("dd/MM/yyyy")
                                            <small class="text-muted">
                                                (@((DateTime.Now.Year - Model.NgaySinh.Value.Year)) tuổi)
                                            </small>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có thông tin</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Giới tính:</strong></td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(Model.GioiTinh))
                                        {
                                            <span class="badge badge-@(Model.GioiTinh == "Nam" ? "primary" : "pink")">
                                                @Model.GioiTinh
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có thông tin</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>CMND/CCCD:</strong></td>
                                    <td>@Html.DisplayFor(model => model.CMND)</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Thông tin bằng lái -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-id-card"></i> Thông tin bằng lái xe</h5>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Số bằng lái:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SoBangLai)</td>
                                </tr>
                                <tr>
                                    <td><strong>Loại bằng lái:</strong></td>
                                    <td>
                                        @switch (Model.LoaiBangLai)
                                        {
                                            case LoaiBangLai.B1:
                                                <span class="badge badge-primary">B1 - Xe ô tô dưới 9 chỗ</span>
                                                break;
                                            case LoaiBangLai.B2:
                                                <span class="badge badge-primary">B2 - Xe ô tô dưới 9 chỗ và xe tải dưới 3.5 tấn</span>
                                                break;
                                            case LoaiBangLai.C:
                                                <span class="badge badge-warning">C - Xe tải và xe khách dưới 30 chỗ</span>
                                                break;
                                            case LoaiBangLai.D:
                                                <span class="badge badge-success">D - Xe khách trên 30 chỗ</span>
                                                break;
                                            case LoaiBangLai.E:
                                                <span class="badge badge-danger">E - Xe container, xe đầu kéo</span>
                                                break;
                                            case LoaiBangLai.F:
                                                <span class="badge badge-dark">F - Xe chuyên dụng</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày cấp:</strong></td>
                                    <td>
                                        @if (Model.NgayCapBangLai.HasValue)
                                        {
                                            @Model.NgayCapBangLai.Value.ToString("dd/MM/yyyy")
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có thông tin</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày hết hạn:</strong></td>
                                    <td>
                                        @if (Model.NgayHetHanBangLai.HasValue)
                                        {
                                            @Model.NgayHetHanBangLai.Value.ToString("dd/MM/yyyy")
                                            @if (Model.NgayHetHanBangLai.Value < DateTime.Now)
                                            {
                                                <span class="badge badge-danger ml-2">Đã hết hạn</span>
                                            }
                                            else if (Model.NgayHetHanBangLai.Value < DateTime.Now.AddMonths(3))
                                            {
                                                <span class="badge badge-warning ml-2">Sắp hết hạn</span>
                                            }
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có thông tin</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Kinh nghiệm:</strong></td>
                                    <td>
                                        @if (Model.KinhNghiem.HasValue)
                                        {
                                            <span>@Model.KinhNghiem năm</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa có thông tin</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <!-- Thông tin công việc -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-briefcase"></i> Thông tin công việc</h5>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td width="40%"><strong>Ngày vào làm:</strong></td>
                                    <td>
                                        @Model.NgayVaoLam.ToString("dd/MM/yyyy")
                                        <small class="text-muted">
                                            (@((DateTime.Now - Model.NgayVaoLam).Days) ngày làm việc)
                                        </small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td>
                                        @switch (Model.TrangThai)
                                        {
                                            case TrangThaiTaiXe.HoatDong:
                                                <span class="badge badge-success">Hoạt động</span>
                                                break;
                                            case TrangThaiTaiXe.NghiPhep:
                                                <span class="badge badge-info">Nghỉ phép</span>
                                                break;
                                            case TrangThaiTaiXe.TamNghi:
                                                <span class="badge badge-warning">Tạm nghỉ</span>
                                                break;
                                            case TrangThaiTaiXe.DaNghiViec:
                                                <span class="badge badge-danger">Đã nghỉ việc</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày tạo hồ sơ:</strong></td>
                                    <td>@Model.NgayTao.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                @if (Model.NgayCapNhat.HasValue)
                                {
                                    <tr>
                                        <td><strong>Lần cập nhật cuối:</strong></td>
                                        <td>@Model.NgayCapNhat.Value.ToString("dd/MM/yyyy HH:mm")</td>
                                    </tr>
                                }
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-sticky-note"></i> Ghi chú</h5>
                            
                            @if (!string.IsNullOrEmpty(Model.GhiChu))
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    @Model.GhiChu
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">Không có ghi chú nào.</p>
                            }
                        </div>
                    </div>

                    <hr>

                    <!-- Lịch sử chuyến xe -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3"><i class="fas fa-history"></i> Lịch sử chuyến xe</h5>

                            @if (Model.ChuyenXes != null && Model.ChuyenXes.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Mã chuyến</th>
                                                <th>Tuyến đường</th>
                                                <th>Ngày khởi hành</th>
                                                <th>Giờ khởi hành</th>
                                                <th>Trạng thái</th>
                                                <th>Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var chuyenXe in Model.ChuyenXes.OrderByDescending(c => c.NgayKhoiHanh).Take(10))
                                            {
                                                <tr>
                                                    <td>
                                                        <strong><EMAIL>("D4")</strong>
                                                    </td>
                                                    <td>
                                                        @if (chuyenXe.TuyenDuong != null)
                                                        {
                                                            <span>@chuyenXe.TuyenDuong.DiemDi → @chuyenXe.TuyenDuong.DiemDen</span>
                                                        }
                                                        else if (!string.IsNullOrEmpty(chuyenXe.DiemDi) && !string.IsNullOrEmpty(chuyenXe.DiemDen))
                                                        {
                                                            <span>@chuyenXe.DiemDi → @chuyenXe.DiemDen</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">Chưa có thông tin</span>
                                                        }
                                                    </td>
                                                    <td>@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</td>
                                                    <td>@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</td>
                                                    <td>
                                                        @if (chuyenXe.TrangThai)
                                                        {
                                                            @switch (chuyenXe.TrangThaiChuyenXe)
                                                            {
                                                                case TrangThaiChuyenXe.HoatDong:
                                                                    <span class="badge badge-success">Hoạt động</span>
                                                                    break;
                                                                case TrangThaiChuyenXe.TamDung:
                                                                    <span class="badge badge-warning">Tạm dừng</span>
                                                                    break;
                                                                case TrangThaiChuyenXe.DaHuy:
                                                                    <span class="badge badge-danger">Đã hủy</span>
                                                                    break;
                                                                case TrangThaiChuyenXe.HoanThanh:
                                                                    <span class="badge badge-success">Hoàn thành</span>
                                                                    break;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            <span class="badge badge-danger">Đã hủy</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <a asp-controller="ChuyenXe" asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId"
                                                           class="btn btn-info btn-sm" title="Xem chi tiết">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                                @if (Model.ChuyenXes.Count() > 10)
                                {
                                    <div class="text-center">
                                        <small class="text-muted">
                                            Hiển thị 10 chuyến xe gần nhất. Tổng cộng: @Model.ChuyenXes.Count() chuyến xe.
                                        </small>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-bus fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">Chưa có chuyến xe nào</h6>
                                    <p class="text-muted">Tài xế này chưa được phân công chuyến xe nào.</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
