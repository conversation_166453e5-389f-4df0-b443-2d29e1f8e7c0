@model DatVeXe.Models.Ve
@using DatVeXe.Helpers
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewData["Title"] = "Chi tiết đặt vé - " + Model.MaVe;
}

@section Styles {
    <style>
        .detail-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        }

        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }

        .info-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            color: #212529;
            font-size: 1.1rem;
        }

        .status-timeline {
            position: relative;
            padding-left: 30px;
        }

        .status-timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #28a745;
        }

        .action-buttons {
            gap: 10px;
        }

        .action-buttons .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
    </style>
}

<div class="container-fluid">
    @Html.AntiForgeryToken()

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a asp-area="Admin" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-home"></i> Admin
                </a>
            </li>
            <li class="breadcrumb-item">
                <a asp-area="Admin" asp-controller="Booking" asp-action="List">
                    Quản lý đặt vé
                </a>
            </li>
            <li class="breadcrumb-item active">Chi tiết vé @Model.MaVe</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-gradient mb-1">
                <i class="fas fa-ticket-alt me-2"></i>
                Chi tiết đặt vé
            </h2>
            <p class="text-muted mb-0">Thông tin chi tiết về vé @Model.MaVe</p>
        </div>
        <div class="action-buttons d-flex">
            <a asp-action="List" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Quay lại
            </a>
            <a asp-action="PrintTicket" asp-route-id="@Model.VeId" class="btn btn-admin btn-admin-primary" target="_blank">
                <i class="fas fa-print me-2"></i>
                In vé
            </a>
            @if (Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy)
            {
                <div class="dropdown">
                    <button class="btn btn-admin btn-admin-warning dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-edit me-2"></i>
                        Cập nhật trạng thái
                    </button>
                    <ul class="dropdown-menu">
                        @foreach (var status in Enum.GetValues<DatVeXe.Models.TrangThaiVe>())
                        {
                            @if (status != Model.VeTrangThai)
                            {
                                <li>
                                    <a class="dropdown-item" href="#" onclick="updateTicketStatus('@status')">
                                        <i class="fas fa-circle text-@(ViewHelper.GetStatusBadgeClass(status)) me-2"></i>
                                        @ViewHelper.GetStatusText(status)
                                    </a>
                                </li>
                            }
                        }
                    </ul>
                </div>
            }
        </div>

        <!-- Trip Information -->
        @if (Model.ChuyenXe != null)
        {
            <div class="detail-card">
                <div class="detail-header">
                    <h4 class="mb-0">
                        <i class="fas fa-bus me-2"></i>
                        Thông tin chuyến xe
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Tuyến đường</div>
                                <div class="info-value">
                                    <strong>@Model.ChuyenXe.TuyenDuong?.DiemDi → @Model.ChuyenXe.TuyenDuong?.DiemDen</strong>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Ngày khởi hành</div>
                                <div class="info-value">
                                    <i class="fas fa-calendar-alt me-2"></i>@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Giờ khởi hành</div>
                                <div class="info-value">
                                    <i class="fas fa-clock me-2"></i>@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            @if (Model.ChuyenXe.Xe != null)
                            {
                                <div class="info-item">
                                    <div class="info-label">Biển số xe</div>
                                    <div class="info-value">
                                        <i class="fas fa-bus me-2"></i>@Model.ChuyenXe.Xe.BienSoXe
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Loại xe</div>
                                    <div class="info-value">@Model.ChuyenXe.Xe.LoaiXe</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Số ghế</div>
                                    <div class="info-value">@Model.ChuyenXe.Xe.SoGhe ghế</div>
                                </div>
                            }

                            @if (Model.ChoNgoi != null)
                            {
                                <div class="info-item">
                                    <div class="info-label">Ghế đã chọn</div>
                                    <div class="info-value">
                                        <span class="status-badge bg-info text-white">
                                            <i class="fas fa-chair me-1"></i>Ghế @Model.ChoNgoi.SoGhe
                                        </span>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Payment & Status Information -->
    <div class="col-lg-4">
        <!-- Payment Status -->
        <div class="detail-card">
            <div class="detail-header">
                <h4 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Thông tin thanh toán
                </h4>
            </div>
            <div class="card-body">
                @if (Model.ThanhToans != null && Model.ThanhToans.Any())
                {
                    @foreach (var thanhToan in Model.ThanhToans.OrderByDescending(t => t.NgayThanhToan))
                    {
                        <div class="info-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="info-label">Phương thức</div>
                                    <div class="info-value">@thanhToan.PhuongThucThanhToan</div>
                                </div>
                                <span class="status-badge bg-@(ViewHelper.GetPaymentStatusBadgeClass(thanhToan.TrangThai))">
                                    @ViewHelper.GetPaymentStatusText(thanhToan.TrangThai)
                                </span>
                            </div>

                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    @(thanhToan.NgayThanhToan != default(DateTime) ? thanhToan.NgayThanhToan.ToString("dd/MM/yyyy HH:mm") : "")
                                </small>
                            </div>

                            <div class="mt-1">
                                <strong class="text-success">@thanhToan.SoTien.ToString("N0") VNĐ</strong>
                            </div>

                            @if (!string.IsNullOrEmpty(thanhToan.MaGiaoDich))
                            {
                                <div class="mt-1">
                                    <small class="text-muted">Mã GD: @thanhToan.MaGiaoDich</small>
                                </div>
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Chưa có thông tin thanh toán</p>
                    </div>
                }
            </div>
        </div>

        <!-- Status Timeline -->
        <div class="detail-card">
            <div class="detail-header">
                <h4 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Lịch sử trạng thái
                </h4>
            </div>
            <div class="card-body">
                <div class="status-timeline">
                    <div class="timeline-item">
                        <div class="info-label">Đã đặt vé</div>
                        <div class="info-value">@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>

                    @if (Model.ThanhToans?.Any(t => t.TrangThai == DatVeXe.Models.TrangThaiThanhToan.ThanhCong) == true)
                    {
                        var thanhToanThanhCong = Model.ThanhToans.FirstOrDefault(t => t.TrangThai == DatVeXe.Models.TrangThaiThanhToan.ThanhCong);
                        <div class="timeline-item">
                            <div class="info-label">Đã thanh toán</div>
                            <div class="info-value">@(thanhToanThanhCong != null && thanhToanThanhCong.NgayThanhToan != default(DateTime) ? thanhToanThanhCong.NgayThanhToan.ToString("dd/MM/yyyy HH:mm") : "")</div>
                        </div>
                    }

                    @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaSuDung)
                    {
                        <div class="timeline-item">
                            <div class="info-label">Đã sử dụng</div>
                            <div class="info-value">Khách đã lên xe</div>
                        </div>
                    }

                    @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHuy && Model.NgayHuy.HasValue)
                    {
                        <div class="timeline-item">
                            <div class="info-label">Đã hủy</div>
                            <div class="info-value">
                                @(Model.NgayHuy.HasValue ? Model.NgayHuy.Value.ToString("dd/MM/yyyy HH:mm") : "")
                                @if (!string.IsNullOrEmpty(Model.LyDoHuy))
                                {
                                    <br><small class="text-muted">Lý do: @Model.LyDoHuy</small>
                                }
                            </div>
                        </div>
                    }

                    @if (Model.VeTrangThai == DatVeXe.Models.TrangThaiVe.DaHoanThanh)
                    {
                        <div class="timeline-item">
                            <div class="info-label">Hoàn thành</div>
                            <div class="info-value">Chuyến đi đã kết thúc</div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="detail-card">
            <div class="detail-header">
                <h4 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Thao tác nhanh
                </h4>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="PrintTicket" asp-route-id="@Model.VeId"
                       class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        In vé
                    </a>

                    @if (Model.NguoiDung != null && !string.IsNullOrEmpty(Model.NguoiDung.Email))
                    {
                        <button class="btn btn-outline-info" onclick="sendEmailNotification()">
                            <i class="fas fa-envelope me-2"></i>
                            Gửi email thông báo
                        </button>
                    }

                    @if (!string.IsNullOrEmpty(Model.SoDienThoai) || !string.IsNullOrEmpty(Model.NguoiDung?.SoDienThoai))
                    {
                        <button class="btn btn-outline-success" onclick="sendSMSNotification()">
                            <i class="fas fa-sms me-2"></i>
                            Gửi SMS thông báo
                        </button>
                    }

                    @if (Model.VeTrangThai != DatVeXe.Models.TrangThaiVe.DaHuy)
                    {
                        <button class="btn btn-outline-danger" onclick="cancelTicket()">
                            <i class="fas fa-ban me-2"></i>
                            Hủy vé
                        </button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
</div>



@section Scripts {
    <script>
        function updateTicketStatus(newStatus) {
            const statusText = getStatusText(newStatus);

            if (confirm(`Bạn có chắc chắn muốn cập nhật trạng thái vé thành "${statusText}"?`)) {
                $.post('@Url.Action("UpdateStatus")', {
                    veId: @Model.VeId,
                    trangThai: newStatus,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                })
                .done(function(data) {
                    if (data.success) {
                        // Update status badge
                        const badge = $('#currentStatus');
                        badge.removeClass().addClass('status-badge bg-' + getStatusBadgeClass(newStatus));
                        badge.text(data.newStatus);

                        showToast(data.message, 'success');

                        // Reload page after 2 seconds
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .fail(function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Có lỗi xảy ra khi cập nhật trạng thái vé';
                    showToast(errorMsg, 'error');
                });
            }
        }

        function sendEmailNotification() {
            showToast('Chức năng gửi email đang được phát triển', 'info');
        }

        function sendSMSNotification() {
            showToast('Chức năng gửi SMS đang được phát triển', 'info');
        }

        function cancelTicket() {
            const reason = prompt('Vui lòng nhập lý do hủy vé:');
            if (reason && reason.trim()) {
                updateTicketStatus('DaHuy');
            }
        }

        function getStatusText(status) {
            const statusMap = {
                'DaDat': 'Đã đặt',
                'DaThanhToan': 'Đã thanh toán',
                'DaSuDung': 'Đã sử dụng',
                'DaHuy': 'Đã hủy',
                'DaHoanThanh': 'Đã hoàn thành',
                'DaHoanTien': 'Đã hoàn tiền'
            };
            return statusMap[status] || 'Không xác định';
        }

        function getStatusBadgeClass(status) {
            const classMap = {
                'DaDat': 'warning',
                'DaThanhToan': 'info',
                'DaSuDung': 'primary',
                'DaHuy': 'danger',
                'DaHoanThanh': 'success',
                'DaHoanTien': 'secondary'
            };
            return classMap[status] || 'light';
        }

        function showToast(message, type) {
            const toastClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-secondary';

            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-bell';

            const toast = $(`
                <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="${icon} me-2"></i>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
            }

            $('#toast-container').append(toast);
            toast.toast('show');

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}

    <div class="row">
        <!-- Ticket Information -->
        <div class="col-lg-8">
            <div class="detail-card">
                <div class="detail-header">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Thông tin vé
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Mã vé</div>
                                <div class="info-value">
                                    <strong class="text-primary">@Model.MaVe</strong>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Tên khách hàng</div>
                                <div class="info-value">
                                    @{
                                        var tenKhach = !string.IsNullOrEmpty(Model.TenKhach) ? Model.TenKhach : Model.NguoiDung?.HoTen;
                                    }
                                    @tenKhach
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Số điện thoại</div>
                                <div class="info-value">
                                    @{
                                        var soDienThoai = !string.IsNullOrEmpty(Model.SoDienThoai) ? Model.SoDienThoai : Model.NguoiDung?.SoDienThoai;
                                    }
                                    <i class="fas fa-phone me-2"></i>@soDienThoai
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Email</div>
                                <div class="info-value">
                                    @{
                                        var email = Model.Email ?? Model.NguoiDung?.Email;
                                    }
                                    @if (!string.IsNullOrEmpty(email))
                                    {
                                        <i class="fas fa-envelope me-2"></i>@email
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa có</span>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Trạng thái vé</div>
                                <div class="info-value">
                                    <span class="status-badge bg-@(ViewHelper.GetStatusBadgeClass(Model.VeTrangThai))" id="currentStatus">
                                        @ViewHelper.GetStatusText(Model.VeTrangThai)
                                    </span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Giá vé</div>
                                <div class="info-value">
                                    <strong class="text-success">@Model.GiaVe.ToString("N0") VNĐ</strong>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Ngày đặt</div>
                                <div class="info-value">
                                    <i class="fas fa-calendar me-2"></i>@Model.NgayDat.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.GhiChu))
                            {
                                <div class="info-item">
                                    <div class="info-label">Ghi chú</div>
                                    <div class="info-value">@Model.GhiChu</div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
