# ✅ Hướng dẫn đăng nhập Google - ĐÃ HOÀN THÀNH

## 🎉 Tính năng đã được tích hợp thành công!

Hệ thống đặt vé xe của bạn hiện đã hỗ trợ đăng nhập bằng Google OAuth 2.0.

## C<PERSON><PERSON> thay đổi đã thực hiện

### 1. Package đã cài đặt
- `Microsoft.AspNetCore.Authentication.Google` (version 8.0.11)

### 2. Cấu hình trong appsettings.json
```json
"Authentication": {
  "Google": {
    "ClientId": "YOUR_GOOGLE_CLIENT_ID",
    "ClientSecret": "YOUR_GOOGLE_CLIENT_SECRET"
  }
}
```

### 3. Cấu hình trong Program.cs
- Thêm Authentication services với Google OAuth
- Cấu hình callback path: `/signin-google`

### 4. Cập nhật Database
- Thêm các trường mới vào bảng `NguoiDungs`:
  - `GoogleId` (nvarchar(100)): <PERSON><PERSON><PERSON> Google ID
  - `LoginProvider` (nvarchar(50)): Phân biệt loại đăng nhập ("Local", "Google")
  - `Avatar` (nvarchar(500)): URL ảnh đại diện từ Google

### 5. Cấu hình Program.cs
- Cấu hình Google Authentication service
- Callback path: `/signin-google`
- **Cấu hình `prompt=select_account`** để luôn hiển thị màn hình chọn tài khoản Google

### 6. Controller mới
- `TaiKhoanController.GoogleLogin()`: Khởi tạo OAuth flow
- `TaiKhoanController.GoogleCallback()`: Xử lý callback từ Google

### 7. Giao diện
- Thêm nút "Đăng nhập bằng Google" vào trang đăng nhập và đăng ký

## Cách cấu hình Google Cloud Console

### Bước 1: Truy cập Google Cloud Console
1. Mở: https://console.cloud.google.com/auth/clients
2. Đăng nhập bằng tài khoản Google

### Bước 2: Cấu hình OAuth consent screen
1. Chọn "External" user type
2. Điền thông tin ứng dụng:
   - App name: "Hệ thống đặt vé xe"
   - User support email: Email của bạn
   - Developer contact: Email của bạn

### Bước 3: Tạo OAuth Client ID
1. Chọn "Web application"
2. Thêm Authorized redirect URIs:
   ```
   http://localhost:5057/signin-google
   https://localhost:7057/signin-google
   ```
3. Lưu Client ID và Client Secret

### Bước 4: Cập nhật cấu hình
Thay thế `YOUR_GOOGLE_CLIENT_ID` và `YOUR_GOOGLE_CLIENT_SECRET` trong `appsettings.json`

## Cách hoạt động

### Flow đăng nhập Google:
1. User nhấp "Đăng nhập bằng Google"
2. Chuyển hướng đến Google OAuth với `prompt=select_account`
3. **Google luôn hiển thị màn hình chọn tài khoản** (không tự động đăng nhập)
4. User chọn tài khoản Google và cấp quyền
5. Google callback về `/signin-google`
6. Hệ thống xử lý thông tin user:
   - Nếu chưa có tài khoản: Tạo mới
   - Nếu đã có: Liên kết với Google ID
7. Đăng nhập thành công

### Dữ liệu lưu trữ:
- Email, tên, Google ID, ảnh đại diện
- LoginProvider = "Google"
- Mật khẩu random (không sử dụng)

## Test tính năng

### Để test:
1. Cấu hình Google OAuth credentials
2. Chạy ứng dụng: `dotnet run`
3. Truy cập trang đăng nhập
4. Nhấp "Đăng nhập bằng Google"
5. Kiểm tra flow đăng nhập

### Lưu ý:
- Cần internet để kết nối Google
- Domain localhost phải được cấu hình trong Google Console
- Client Secret cần được bảo mật

## Troubleshooting

### Lỗi thường gặp:
1. **redirect_uri_mismatch**: Kiểm tra URL callback trong Google Console
2. **invalid_client**: Kiểm tra Client ID/Secret
3. **access_denied**: User từ chối cấp quyền

### Debug:
- Kiểm tra logs trong console
- Xem Network tab trong browser
- Kiểm tra cấu hình Google Console

## Bảo mật

### Khuyến nghị:
- Không commit Client Secret vào source code
- Sử dụng User Secrets trong development
- Sử dụng Azure Key Vault trong production
- Giới hạn domain được phép trong Google Console

## Tính năng bổ sung có thể thêm

### Trong tương lai:
- Liên kết/hủy liên kết tài khoản Google
- Đăng nhập bằng Facebook, Microsoft
- Two-factor authentication
- Profile management nâng cao
