@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Cập nhật thông tin cá nhân";
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="bi bi-person-circle" style="font-size: 4rem; color: #6CABDD;"></i>
                    </div>
                    <h5 class="card-title">@Model.HoTen</h5>
                    <p class="text-muted">@Model.Email</p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-list me-2"></i>Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="@Url.Action("Dashboard", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    </a>
                    <a href="@Url.Action("LichSuDatVe", "TaiKhoan")" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock-history me-2"></i>Lịch sử đặt vé
                    </a>
                    <a href="@Url.Action("Profile", "TaiKhoan")" class="list-group-item list-group-item-action active">
                        <i class="bi bi-person-gear me-2"></i>Cập nhật thông tin
                    </a>
                    <a href="@Url.Action("Create", "Ve")" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-light">
                    <h4 class="mb-0">
                        <i class="bi bi-person-gear me-2"></i>
                        Cập nhật thông tin cá nhân
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="Profile" method="post" id="profileForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="bi bi-info-circle me-1"></i> Thông tin cơ bản
                                </h5>

                                <div class="mb-3">
                                    <label for="hoTen" class="form-label">
                                        <i class="bi bi-person me-1"></i> Họ tên
                                    </label>
                                    <input id="hoTen" name="hoTen" value="@Model.HoTen" class="form-control" placeholder="Nhập họ tên đầy đủ" required />
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope me-1"></i> Email
                                    </label>
                                    <input id="email" name="email" value="@Model.Email" class="form-control" type="email" placeholder="Nhập địa chỉ email" required />
                                </div>

                                @if (Model.LaAdmin)
                                {
                                    <div class="mb-3">
                                        <div class="alert alert-info" role="alert">
                                            <i class="bi bi-shield-check me-1"></i>
                                            <strong>Quyền:</strong> Quản trị viên
                                        </div>
                                    </div>
                                }
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="bi bi-lock me-1"></i> Đổi mật khẩu (tùy chọn)
                                </h5>

                                <div class="mb-3">
                                    <label for="matKhauCu" class="form-label">
                                        <i class="bi bi-key me-1"></i> Mật khẩu cũ
                                    </label>
                                    <div class="input-group">
                                        <input id="matKhauCu" name="matKhauCu" class="form-control" type="password" placeholder="Nhập mật khẩu cũ" />
                                        <button class="btn btn-outline-secondary" type="button" id="toggleOldPassword">
                                            <i class="bi bi-eye" id="toggleOldIcon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="matKhauMoi" class="form-label">
                                        <i class="bi bi-lock me-1"></i> Mật khẩu mới
                                    </label>
                                    <div class="input-group">
                                        <input id="matKhauMoi" name="matKhauMoi" class="form-control" type="password" placeholder="Nhập mật khẩu mới" />
                                        <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                            <i class="bi bi-eye" id="toggleNewIcon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Để trống nếu không muốn đổi mật khẩu</div>
                                </div>

                                <div class="mb-3">
                                    <label for="xacNhanMatKhau" class="form-label">
                                        <i class="bi bi-lock me-1"></i> Xác nhận mật khẩu mới
                                    </label>
                                    <div class="input-group">
                                        <input id="xacNhanMatKhau" name="xacNhanMatKhau" class="form-control" type="password" placeholder="Nhập lại mật khẩu mới" />
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="bi bi-eye" id="toggleConfirmIcon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-check-circle me-1"></i> Cập nhật thông tin
                            </button>
                            <a href="@Url.Action("Dashboard", "TaiKhoan")" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i> Quay lại Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
    }

    .user-avatar {
        margin-bottom: 1rem;
    }

    .list-group-item {
        border: none;
        border-bottom: 1px solid #eee;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .list-group-item.active {
        background-color: #6CABDD;
        border-color: #6CABDD;
    }

    .border-primary { border-color: #6CABDD !important; }
    .text-primary { color: #6CABDD !important; }
    .bg-primary { background-color: #6CABDD !important; }
    .btn-primary { background-color: #6CABDD; border-color: #6CABDD; }
    .btn-primary:hover { background-color: #5a9bc4; border-color: #5a9bc4; }
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            var form = $('#profileForm');

            // Toggle password visibility functions
            function togglePasswordVisibility(fieldId, iconId) {
                var field = $(fieldId);
                var icon = $(iconId);

                if (field.attr('type') === 'password') {
                    field.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                } else {
                    field.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                }
            }

            // Toggle password visibility events
            $('#toggleOldPassword').click(function() {
                togglePasswordVisibility('#matKhauCu', '#toggleOldIcon');
            });

            $('#toggleNewPassword').click(function() {
                togglePasswordVisibility('#matKhauMoi', '#toggleNewIcon');
            });

            $('#toggleConfirmPassword').click(function() {
                togglePasswordVisibility('#xacNhanMatKhau', '#toggleConfirmIcon');
            });

            // Form validation
            form.on('submit', function(e) {
                var hoTen = $('#hoTen').val().trim();
                var email = $('#email').val().trim();
                var matKhauCu = $('#matKhauCu').val();
                var matKhauMoi = $('#matKhauMoi').val();
                var xacNhanMatKhau = $('#xacNhanMatKhau').val();

                // Basic validation
                if (!hoTen || !email) {
                    e.preventDefault();
                    alert('Vui lòng nhập đầy đủ họ tên và email!');
                    return false;
                }

                // Email validation
                var emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    alert('Email không hợp lệ!');
                    $('#email').focus();
                    return false;
                }

                // Password validation if changing password
                if (matKhauMoi) {
                    if (!matKhauCu) {
                        e.preventDefault();
                        alert('Vui lòng nhập mật khẩu cũ!');
                        $('#matKhauCu').focus();
                        return false;
                    }

                    if (matKhauMoi.length < 6) {
                        e.preventDefault();
                        alert('Mật khẩu mới phải có ít nhất 6 ký tự!');
                        $('#matKhauMoi').focus();
                        return false;
                    }

                    if (matKhauMoi !== xacNhanMatKhau) {
                        e.preventDefault();
                        alert('Mật khẩu mới và xác nhận không khớp!');
                        $('#xacNhanMatKhau').focus();
                        return false;
                    }
                }

                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true).html('<i class="bi bi-arrow-clockwise"></i> Đang cập nhật...');
                return true;
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
}
