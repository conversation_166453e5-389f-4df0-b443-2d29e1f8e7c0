@model ContactListViewModel
@{
    ViewData["Title"] = "Danh bạ hành khách";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="bi bi-person-lines-fill me-2"></i>Danh bạ hành khách
                </h2>
                <div>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Thêm liên hệ
                    </a>
                    <a asp-controller="MyTickets" asp-action="Index" class="btn btn-outline-secondary">
                        <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                    </a>
                </div>
            </div>

            <!-- Thống kê -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-people-fill display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.TotalContacts</h3>
                            <p class="mb-0">Tổng liên hệ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-clock-history display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.DanhSachLienHe.Count(c => c.LanSuDungCuoi.HasValue)</h3>
                            <p class="mb-0">Đã sử dụng</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-star-fill display-4 mb-2"></i>
                            <h3 class="fw-bold">@(Model.DanhSachLienHe.Any() ? Model.DanhSachLienHe.Max(c => c.SoLanSuDung) : 0)</h3>
                            <p class="mb-0">Sử dụng nhiều nhất</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-calendar-plus display-4 mb-2"></i>
                            <h3 class="fw-bold">@Model.DanhSachLienHe.Count(c => c.NgayTao >= DateTime.Now.AddDays(-30))</h3>
                            <p class="mb-0">Thêm trong tháng</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tìm kiếm -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" name="searchTerm" class="form-control" 
                                       placeholder="Tìm theo tên, số điện thoại, email..." 
                                       value="@Model.SearchTerm" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="bi bi-search me-2"></i>Tìm kiếm
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Danh sách liên hệ -->
            @if (Model.DanhSachLienHe.Any())
            {
                <div class="row">
                    @foreach (var contact in Model.DanhSachLienHe)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary text-white me-3">
                                            @contact.TenHanhKhach.Substring(0, 1).ToUpper()
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-bold">@contact.TenHanhKhach</h6>
                                            @if (contact.SoLanSuDung > 0)
                                            {
                                                <small class="text-success">
                                                    <i class="bi bi-check-circle me-1"></i>Đã dùng @contact.SoLanSuDung lần
                                                </small>
                                            }
                                            else
                                            {
                                                <small class="text-muted">Chưa sử dụng</small>
                                            }
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" asp-action="Details" asp-route-id="@contact.DanhBaId">
                                                    <i class="bi bi-eye me-2"></i>Xem chi tiết
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" asp-action="Edit" asp-route-id="@contact.DanhBaId">
                                                    <i class="bi bi-pencil me-2"></i>Chỉnh sửa
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-danger" asp-action="Delete" asp-route-id="@contact.DanhBaId">
                                                    <i class="bi bi-trash me-2"></i>Xóa
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <!-- Thông tin liên hệ -->
                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="bi bi-telephone-fill text-primary me-2"></i>
                                            <span class="fw-semibold">@contact.SoDienThoai</span>
                                        </div>
                                        @if (!string.IsNullOrEmpty(contact.Email))
                                        {
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-envelope-fill text-info me-2"></i>
                                                <span class="text-truncate">@contact.Email</span>
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(contact.DiaChi))
                                        {
                                            <div class="d-flex align-items-start mb-2">
                                                <i class="bi bi-geo-alt-fill text-warning me-2 mt-1"></i>
                                                <span class="text-truncate">@contact.DiaChi</span>
                                            </div>
                                        }
                                    </div>

                                    <!-- Thông tin bổ sung -->
                                    <div class="row g-2 mb-3">
                                        @if (contact.NgaySinh.HasValue)
                                        {
                                            <div class="col-6">
                                                <small class="text-muted d-block">Ngày sinh</small>
                                                <span class="fw-semibold">@contact.NgaySinh.Value.ToString("dd/MM/yyyy")</span>
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(contact.GioiTinh))
                                        {
                                            <div class="col-6">
                                                <small class="text-muted d-block">Giới tính</small>
                                                <span class="fw-semibold">@contact.GioiTinh</span>
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(contact.CCCD))
                                        {
                                            <div class="col-12">
                                                <small class="text-muted d-block">CCCD/CMND</small>
                                                <span class="fw-semibold font-monospace">@contact.CCCD</span>
                                            </div>
                                        }
                                    </div>

                                    <!-- Thời gian -->
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            Tạo: @contact.NgayTao.ToString("dd/MM/yyyy")
                                        </small>
                                        @if (contact.LanSuDungCuoi.HasValue)
                                        {
                                            <br>
                                            <small class="text-success">
                                                <i class="bi bi-clock-history me-1"></i>
                                                Dùng cuối: @contact.LanSuDungCuoi.Value.ToString("dd/MM/yyyy")
                                            </small>
                                        }
                                    </div>

                                    @if (!string.IsNullOrEmpty(contact.GhiChu))
                                    {
                                        <div class="mb-3">
                                            <small class="text-muted d-block">Ghi chú</small>
                                            <span class="text-truncate">@contact.GhiChu</span>
                                        </div>
                                    }
                                </div>

                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a asp-action="Details" asp-route-id="@contact.DanhBaId" 
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-eye me-1"></i>Chi tiết
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@contact.DanhBaId" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-success btn-sm" 
                                                onclick="useContact(@contact.DanhBaId, '@contact.TenHanhKhach', '@contact.SoDienThoai')">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination nếu cần -->
                @if (Model.TotalContacts > 12)
                {
                    <nav aria-label="Contacts pagination">
                        <ul class="pagination justify-content-center">
                            <!-- Pagination items here -->
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-person-plus display-1 text-muted"></i>
                    </div>
                    <h4 class="text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            @:Không tìm thấy liên hệ nào
                        }
                        else
                        {
                            @:Chưa có liên hệ nào trong danh bạ
                        }
                    </h4>
                    <p class="text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            @:Thử tìm kiếm với từ khóa khác hoặc thêm liên hệ mới
                        }
                        else
                        {
                            @:Thêm liên hệ để tiện lợi khi đặt vé cho người thân, bạn bè
                        }
                    </p>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Thêm liên hệ đầu tiên
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function useContact(contactId, name, phone) {
            // This function can be used when integrating with booking form
            // For now, just show a success message
            $.post('@Url.Action("UpdateUsage")', { id: contactId }, function(result) {
                if (result.success) {
                    toastr.success(`Đã chọn liên hệ: ${name} - ${phone}`);
                    // You can redirect to booking page or populate form here
                    // window.location.href = '@Url.Action("Search", "Booking")';
                }
            });
        }

        // Auto-focus search input
        $(document).ready(function() {
            $('input[name="searchTerm"]').focus();
        });
    </script>
}

<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .card {
        border-radius: 15px;
        transition: transform 0.2s ease-in-out;
    }
    
    .card:hover {
        transform: translateY(-2px);
    }
    
    .text-truncate {
        max-width: 200px;
    }
    
    .dropdown-toggle::after {
        display: none;
    }
</style>
