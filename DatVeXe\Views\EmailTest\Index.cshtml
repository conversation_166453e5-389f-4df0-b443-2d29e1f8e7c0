@{
    ViewData["Title"] = "Test Email Service";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-envelope-check me-2"></i>
                        Test Email Service
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["EmailContent"] != null)
                    {
                        <div class="alert alert-info">
                            <h5><i class="bi bi-envelope me-2"></i>Nội dung Email Demo:</h5>
                            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; white-space: pre-wrap;">@TempData["EmailContent"]</pre>
                        </div>
                    }

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Demo Email Service:</strong> Hiện tại đang chạy ở chế độ demo.
                        Thay vì gửi email thật, hệ thống sẽ hiển thị nội dung email mẫu.
                        <br>
                        <strong>Email test:</strong> <EMAIL>
                    </div>

                    <form method="post" asp-action="SendTestEmail">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                Email nhận test
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<EMAIL>" placeholder="Nhập email để nhận test" required>
                            <div class="form-text">
                                Email sẽ nhận được email test với nội dung mẫu.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">
                                <i class="bi bi-list-check me-1"></i>
                                Loại email test
                            </label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <i class="bi bi-check-circle text-success fs-1 mb-2"></i>
                                            <h6>Email xác nhận đặt vé</h6>
                                            <button type="submit" name="type" value="confirmation" 
                                                    class="btn btn-success">
                                                <i class="bi bi-send me-1"></i>
                                                Gửi test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <i class="bi bi-x-circle text-warning fs-1 mb-2"></i>
                                            <h6>Email thông báo hủy vé</h6>
                                            <button type="submit" name="type" value="cancellation" 
                                                    class="btn btn-warning">
                                                <i class="bi bi-send me-1"></i>
                                                Gửi test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h5>Cấu hình Email Settings</h5>
                        <div class="alert alert-secondary">
                            <p><strong>Để sử dụng email service, cần cấu hình trong appsettings.json:</strong></p>
                            <pre><code>{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderName": "Hệ thống đặt vé xe",
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "EnableSsl": true
  }
}</code></pre>
                            <p class="mt-2 mb-0">
                                <strong>Lưu ý:</strong> Với Gmail, bạn cần sử dụng App Password thay vì mật khẩu thường.
                            </p>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-outline-primary">
                            <i class="bi bi-house me-1"></i>
                            Về trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }
    
    pre {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 5px;
        font-size: 0.9rem;
    }
    
    .btn {
        border-radius: 8px;
        font-weight: 600;
    }
</style>
