﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddLichSuKhuyenMaiTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LichSuKhuyenMais",
                columns: table => new
                {
                    LichSuKhuyenMaiId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NguoiDungId = table.Column<int>(type: "int", nullable: true),
                    VeId = table.Column<int>(type: "int", nullable: true),
                    KhuyenMaiId = table.Column<int>(type: "int", nullable: false),
                    MaKhuyenMai = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ThoiGianSuDung = table.Column<DateTime>(type: "datetime2", nullable: false),
                    GiaTriGiam = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LichSuKhuyenMais", x => x.LichSuKhuyenMaiId);
                    table.ForeignKey(
                        name: "FK_LichSuKhuyenMais_KhuyenMais_KhuyenMaiId",
                        column: x => x.KhuyenMaiId,
                        principalTable: "KhuyenMais",
                        principalColumn: "KhuyenMaiId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LichSuKhuyenMais_NguoiDungs_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "NguoiDungs",
                        principalColumn: "NguoiDungId");
                    table.ForeignKey(
                        name: "FK_LichSuKhuyenMais_Ves_VeId",
                        column: x => x.VeId,
                        principalTable: "Ves",
                        principalColumn: "VeId");
                });

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_KhuyenMaiId",
                table: "LichSuKhuyenMais",
                column: "KhuyenMaiId");

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_NguoiDungId",
                table: "LichSuKhuyenMais",
                column: "NguoiDungId");

            migrationBuilder.CreateIndex(
                name: "IX_LichSuKhuyenMais_VeId",
                table: "LichSuKhuyenMais",
                column: "VeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LichSuKhuyenMais");
        }
    }
}
