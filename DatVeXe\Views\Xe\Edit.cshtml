@model DatVeXe.Models.Xe

@{
    ViewData["Title"] = "Chỉnh sửa xe";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit"></i> @ViewData["Title"]
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" id="editXeForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <input type="hidden" asp-for="XeId" />
                        
                        <div class="mb-3">
                            <label asp-for="BienSo" class="form-label">
                                <i class="fas fa-id-card"></i> @Html.DisplayNameFor(model => model.BienSo)
                            </label>
                            <input asp-for="BienSo" class="form-control" placeholder="Ví dụ: 51A-12345" />
                            <span asp-validation-for="BienSo" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="LoaiXe" class="form-label">
                                <i class="fas fa-bus"></i> @Html.DisplayNameFor(model => model.LoaiXe)
                            </label>
                            <select asp-for="LoaiXe" class="form-select">
                                <option value="">-- Chọn loại xe --</option>
                                <option value="Giường nằm">Giường nằm</option>
                                <option value="Limousine">Limousine</option>
                                <option value="Ghế ngồi">Ghế ngồi</option>
                                <option value="VIP">VIP</option>
                            </select>
                            <span asp-validation-for="LoaiXe" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="SoGhe" class="form-label">
                                <i class="fas fa-chair"></i> @Html.DisplayNameFor(model => model.SoGhe)
                            </label>
                            <input asp-for="SoGhe" class="form-control" type="number" min="1" max="100" placeholder="Số ghế" />
                            <span asp-validation-for="SoGhe" class="text-danger"></span>
                            <div class="form-text">Số ghế phải từ 1 đến 100</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i> Cập nhật
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.XeId" class="btn btn-info">
                                <i class="fas fa-info-circle"></i> Chi tiết
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            var form = $('#editXeForm');
            
            // Custom validation
            $.validator.addMethod('vietnameseLicensePlate', function(value, element) {
                if (!value) return false;
                // Vietnamese license plate format: 51A-12345 or 51-A1 12345
                var pattern = /^[0-9]{2}[A-Z]{1,2}[-\s]?[0-9]{4,5}$/i;
                return pattern.test(value);
            }, 'Biển số xe không đúng định dạng (VD: 51A-12345)');

            $("#BienSo").rules("add", {
                vietnameseLicensePlate: true
            });

            // Handle form submission
            form.on('submit', function(e) {
                if (!form.valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    var firstError = $('.field-validation-error').first();
                    if (firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 200);
                    }
                    return false;
                }
                
                // Disable submit button to prevent double submission
                $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang cập nhật...');
                return true;
            });

            // Auto-format license plate
            $('#BienSo').on('input', function() {
                var value = $(this).val().toUpperCase();
                // Remove any existing dashes or spaces
                value = value.replace(/[-\s]/g, '');
                
                // Add dash after the letters if needed
                if (value.length >= 3) {
                    var letters = value.match(/^[0-9]{2}[A-Z]{1,2}/);
                    if (letters) {
                        var numbers = value.substring(letters[0].length);
                        if (numbers.length > 0) {
                            value = letters[0] + '-' + numbers;
                        }
                    }
                }
                
                $(this).val(value);
            });
        });
    </script>
}
