using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;

namespace DatVeXe.Services
{
    public class SeatReservationCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SeatReservationCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(1); // Chạy mỗi phút

        public SeatReservationCleanupService(
            IServiceProvider serviceProvider,
            ILogger<SeatReservationCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CleanupExpiredReservations();
                    await Task.Delay(_cleanupInterval, stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while cleaning up expired seat reservations");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Đợi 5 phút nếu có lỗi
                }
            }
        }

        private async Task CleanupExpiredReservations()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<DatVeXeContext>();

            try
            {
                // Tìm các reservation đã hết hạn
                var expiredReservations = await context.SeatReservations
                    .Include(r => r.ChoNgoi)
                    .Where(r => r.IsActive && r.ExpiresAt <= DateTime.Now)
                    .ToListAsync();

                if (expiredReservations.Any())
                {
                    _logger.LogInformation($"Found {expiredReservations.Count} expired seat reservations to cleanup");

                    foreach (var reservation in expiredReservations)
                    {
                        reservation.IsActive = false;
                        reservation.UpdatedAt = DateTime.Now;

                        // Log chi tiết từng reservation bị cleanup
                        _logger.LogDebug($"Cleaning up reservation: SessionId={reservation.SessionId}, " +
                                       $"SeatId={reservation.ChoNgoiId}, ExpiresAt={reservation.ExpiresAt}");
                    }

                    await context.SaveChangesAsync();
                    _logger.LogInformation($"Successfully cleaned up {expiredReservations.Count} expired seat reservations");
                }

                // Cleanup các session booking cũ (quá 24 giờ)
                await CleanupOldBookingSessions(context);

                // Cleanup các vé chưa thanh toán quá hạn (quá 30 phút)
                await CleanupUnpaidTickets(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cleanup process");
                throw;
            }
        }

        private async Task CleanupOldBookingSessions(DatVeXeContext context)
        {
            try
            {
                // Xóa các session reservation cũ hơn 24 giờ
                var oldSessions = await context.SeatReservations
                    .Where(r => !r.IsActive && r.ReservedAt <= DateTime.Now.AddHours(-24))
                    .ToListAsync();

                if (oldSessions.Any())
                {
                    context.SeatReservations.RemoveRange(oldSessions);
                    await context.SaveChangesAsync();
                    _logger.LogInformation($"Removed {oldSessions.Count} old seat reservation records");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old booking sessions");
            }
        }

        private async Task CleanupUnpaidTickets(DatVeXeContext context)
        {
            try
            {
                // Tìm các vé chưa thanh toán quá 30 phút
                var unpaidTickets = await context.Ves
                    .Include(v => v.ThanhToans)
                    .Where(v => v.VeTrangThai == TrangThaiVe.DaDat &&
                               v.NgayDat <= DateTime.Now.AddMinutes(-30) &&
                               !v.ThanhToans.Any(t => t.TrangThai == TrangThaiThanhToan.ThanhCong))
                    .ToListAsync();

                if (unpaidTickets.Any())
                {
                    foreach (var ticket in unpaidTickets)
                    {
                        ticket.VeTrangThai = TrangThaiVe.DaHuy;
                        ticket.NgayCapNhat = DateTime.Now;

                        _logger.LogInformation($"Auto-cancelled unpaid ticket: {ticket.MaVe}");
                    }

                    await context.SaveChangesAsync();
                    _logger.LogInformation($"Auto-cancelled {unpaidTickets.Count} unpaid tickets");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up unpaid tickets");
            }
        }
    }
}
