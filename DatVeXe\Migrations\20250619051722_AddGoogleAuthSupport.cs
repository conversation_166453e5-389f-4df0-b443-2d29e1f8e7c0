﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class AddGoogleAuthSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Ngay<PERSON><PERSON><PERSON><PERSON>",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NgayCap<PERSON>hat",
                table: "TuyenDuongs");

            migrationBuilder.DropColumn(
                name: "Ngay<PERSON>ap<PERSON><PERSON>",
                table: "TaiXes");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "KhuyenMais");

            migrationBuilder.DropColumn(
                name: "<PERSON>ay<PERSON>apNhat",
                table: "ChuyenXes");

            migrationBuilder.AddColumn<string>(
                name: "Avatar",
                table: "NguoiDungs",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GoogleId",
                table: "NguoiDungs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LoginProvider",
                table: "NguoiDungs",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 1,
                columns: new[] { "Avatar", "GoogleId", "LoginProvider" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 2,
                columns: new[] { "Avatar", "GoogleId", "LoginProvider" },
                values: new object[] { null, null, null });

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 3,
                columns: new[] { "Avatar", "GoogleId", "LoginProvider" },
                values: new object[] { null, null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Avatar",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "GoogleId",
                table: "NguoiDungs");

            migrationBuilder.DropColumn(
                name: "LoginProvider",
                table: "NguoiDungs");

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "Xes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "TuyenDuongs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "TaiXes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "NguoiDungs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "KhuyenMais",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "ChuyenXes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 1,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 2,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 3,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 4,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "KhuyenMais",
                keyColumn: "KhuyenMaiId",
                keyValue: 5,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 1,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 2,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "NguoiDungs",
                keyColumn: "NguoiDungId",
                keyValue: 3,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TaiXes",
                keyColumn: "TaiXeId",
                keyValue: 1,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TaiXes",
                keyColumn: "TaiXeId",
                keyValue: 2,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TaiXes",
                keyColumn: "TaiXeId",
                keyValue: 3,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 1,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 2,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 3,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 4,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "TuyenDuongs",
                keyColumn: "TuyenDuongId",
                keyValue: 5,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 1,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 2,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 3,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 4,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 5,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 6,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 7,
                column: "NgayCapNhat",
                value: null);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 8,
                column: "NgayCapNhat",
                value: null);
        }
    }
}
