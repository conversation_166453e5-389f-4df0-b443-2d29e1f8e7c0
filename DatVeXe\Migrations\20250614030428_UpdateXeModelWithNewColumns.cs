﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DatVeXe.Migrations
{
    /// <inheritdoc />
    public partial class UpdateXeModelWithNewColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON><PERSON>",
                table: "Xes",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HangXe",
                table: "Xes",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MauXe",
                table: "Xes",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NamSanXuat",
                table: "Xes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "TrangThaiHoatDong",
                table: "Xes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "TrangThai",
                table: "ChoNgois",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 1,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 2,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 3,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 4,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 5,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 6,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 7,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });

            migrationBuilder.UpdateData(
                table: "Xes",
                keyColumn: "XeId",
                keyValue: 8,
                columns: new[] { "GhiChu", "HangXe", "MauXe", "NamSanXuat", "TrangThaiHoatDong" },
                values: new object[] { null, null, null, null, true });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GhiChu",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "HangXe",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "MauXe",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "NamSanXuat",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "TrangThaiHoatDong",
                table: "Xes");

            migrationBuilder.DropColumn(
                name: "TrangThai",
                table: "ChoNgois");
        }
    }
}
