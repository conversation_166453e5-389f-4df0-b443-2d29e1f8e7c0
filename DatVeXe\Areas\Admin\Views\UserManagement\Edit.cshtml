@model DatVeXe.Models.NguoiDung

@{
    ViewData["Title"] = "Chỉnh sửa người dùng";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 style="color: #2c3e50; font-weight: 600;">
            <i class="fas fa-user-edit text-primary me-2"></i>
            Chỉnh sửa người dùng: @Model.HoTen
        </h3>
        <div class="d-flex gap-2">
            <a href="@Url.Action("Details", new { id = Model.NguoiDungId })" class="btn btn-info">
                <i class="fas fa-eye me-1"></i> Xem chi tiết
            </a>
            <a href="@Url.Action("Index")" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Info Summary -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-user-circle me-2"></i>
                        Thông tin hiện tại
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="fas fa-user-circle" style="font-size: 4rem; color: #6CABDD;"></i>
                    </div>
                    <h5 class="mb-2">@Model.HoTen</h5>
                    <p class="text-muted mb-3">@Model.Email</p>
                    
                    <div class="mb-3">
                        @if (Model.LaAdmin)
                        {
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-crown me-1"></i>Quản trị viên
                            </span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">
                                <i class="fas fa-user me-1"></i>Người dùng
                            </span>
                        }
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <span class="badge bg-@(Model.TrangThaiHoatDong ? "success" : "danger")">
                                    @(Model.TrangThaiHoatDong ? "Hoạt động" : "Không hoạt động")
                                </span>
                            </div>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-@(Model.TaiKhoanBiKhoa ? "danger" : "success")">
                                @(Model.TaiKhoanBiKhoa ? "Bị khoá" : "Bình thường")
                            </span>
                        </div>
                    </div>

                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Đăng ký: @Model.NgayDangKy.ToString("dd/MM/yyyy")
                    </small>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Chỉnh sửa thông tin
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="NguoiDungId" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <!-- Personal Information Tab -->
                        <div class="tab-content">
                            <div class="tab-pane fade show active">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-address-card me-2"></i>
                                    Thông tin cá nhân
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="HoTen" class="form-label fw-bold">
                                                <i class="fas fa-user text-muted me-1"></i>
                                                Họ tên <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="HoTen" class="form-control" />
                                            <span asp-validation-for="HoTen" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Email" class="form-label fw-bold">
                                                <i class="fas fa-envelope text-muted me-1"></i>
                                                Email <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="Email" class="form-control" type="email" />
                                            <span asp-validation-for="Email" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="SoDienThoai" class="form-label fw-bold">
                                                <i class="fas fa-phone text-muted me-1"></i>
                                                Số điện thoại
                                            </label>
                                            <input asp-for="SoDienThoai" class="form-control" />
                                            <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="GioiTinh" class="form-label fw-bold">
                                                <i class="fas fa-venus-mars text-muted me-1"></i>
                                                Giới tính
                                            </label>
                                            <select asp-for="GioiTinh" class="form-select">
                                                <option value="">-- Chọn giới tính --</option>
                                                <option value="Nam">Nam</option>
                                                <option value="Nữ">Nữ</option>
                                                <option value="Khác">Khác</option>
                                            </select>
                                            <span asp-validation-for="GioiTinh" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="NgaySinh" class="form-label fw-bold">
                                                <i class="fas fa-birthday-cake text-muted me-1"></i>
                                                Ngày sinh
                                            </label>
                                            <input asp-for="NgaySinh" class="form-control" type="date" />
                                            <span asp-validation-for="NgaySinh" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label for="newPassword" class="form-label fw-bold">
                                                <i class="fas fa-key text-muted me-1"></i>
                                                Mật khẩu mới
                                            </label>
                                            <div class="input-group">
                                                <input name="newPassword" id="newPassword" class="form-control" type="password" placeholder="Để trống nếu không đổi mật khẩu" />
                                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                    <i class="fas fa-eye" id="togglePasswordIcon"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Để trống nếu không muốn thay đổi mật khẩu</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label asp-for="DiaChi" class="form-label fw-bold">
                                                <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                                Địa chỉ
                                            </label>
                                            <textarea asp-for="DiaChi" class="form-control" rows="3"></textarea>
                                            <span asp-validation-for="DiaChi" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Permissions and Status -->
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>
                                    Quyền hạn và trạng thái
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Quyền hạn</label>
                                            <div class="form-check">
                                                <input asp-for="LaAdmin" class="form-check-input" type="checkbox" />
                                                <label asp-for="LaAdmin" class="form-check-label">
                                                    <i class="fas fa-user-shield text-warning me-1"></i>
                                                    Quyền quản trị viên
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Trạng thái hoạt động</label>
                                            <div class="form-check">
                                                <input asp-for="TrangThaiHoatDong" class="form-check-input" type="checkbox" />
                                                <label asp-for="TrangThaiHoatDong" class="form-check-label">
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    Tài khoản hoạt động
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Trạng thái tài khoản</label>
                                            <div class="form-check">
                                                <input asp-for="TaiKhoanBiKhoa" class="form-check-input" type="checkbox" />
                                                <label asp-for="TaiKhoanBiKhoa" class="form-check-label">
                                                    <i class="fas fa-lock text-danger me-1"></i>
                                                    Khoá tài khoản
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3" id="lockReasonGroup" style="@(Model.TaiKhoanBiKhoa ? "" : "display: none;")">
                                            <label asp-for="LyDoKhoa" class="form-label fw-bold">
                                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                                Lý do khoá tài khoản
                                            </label>
                                            <textarea asp-for="LyDoKhoa" class="form-control" rows="2" placeholder="Nhập lý do khoá tài khoản..."></textarea>
                                            <span asp-validation-for="LyDoKhoa" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Submit Buttons -->
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="@Url.Action("Details", new { id = Model.NguoiDungId })" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i> Xem chi tiết
                                    </a>
                                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> Hủy
                                    </a>
                                    <button type="reset" class="btn btn-outline-warning">
                                        <i class="fas fa-undo me-1"></i> Đặt lại
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> Cập nhật
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
<script src="~/js/user-management.js"></script>

<style>
    .form-label.fw-bold {
        color: #2c3e50;
    }
    
    .card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #6CABDD;
        box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.25);
    }
    
    .btn {
        border-radius: 6px;
    }
    
    .form-check-input:checked {
        background-color: #6CABDD;
        border-color: #6CABDD;
    }
    
    .user-avatar {
        margin-bottom: 1rem;
    }
    
    .input-group .btn {
        border-radius: 0 6px 6px 0;
    }
</style>
