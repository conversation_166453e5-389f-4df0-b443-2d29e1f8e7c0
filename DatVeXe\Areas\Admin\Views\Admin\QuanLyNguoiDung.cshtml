@{
    ViewData["Title"] = "Chuyển hướng...";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-sync-alt fa-3x text-primary fa-spin"></i>
                    </div>
                    <h4 class="mb-3">Đang chuyển hướng...</h4>
                    <p class="text-muted">Bạn đang được chuyển đến trang quản lý người dùng mới với nhiều tính năng nâng cao hơn.</p>
                    <div class="mt-4">
                        <a href="@Url.Action("Index", "UserManagement")" class="btn btn-primary">
                            <i class="fas fa-users me-1"></i> Đến trang quản lý người dùng
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto redirect after 2 seconds
    setTimeout(function() {
        window.location.href = '@Url.Action("Index", "UserManagement")';
    }, 2000);
</script>
