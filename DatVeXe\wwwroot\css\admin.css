/* Admin Panel Custom Styles */

/* Global Admin Styles */

/* Form Labels */
.form-label,
.form-check-label,
.form-label.fw-bold,
.form-check-label.fw-bold,
.admin-card .form-label,
.admin-card .form-check-label {
    color: black !important;
}

/* Override any other label styles */
label {
    color: black !important;
}
.admin-body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Sidebar Styles */
.admin-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    margin: 2px 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.admin-sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: #ffc107;
    transform: translateX(5px);
}

.admin-sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: #28a745;
}

.admin-sidebar .nav-link i {
    width: 20px;
    margin-right: 10px;
}

/* Header Styles */
.admin-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
}

.admin-header .navbar-brand {
    font-weight: 700;
    color: #2c3e50;
}

/* Card Styles */
.admin-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px;
}

.admin-card .card-body {
    padding: 25px;
}

/* Stats Cards */
.stats-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    border-left: 5px solid;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-card i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 3rem;
    opacity: 0.3;
    color: white;
}

/* Table Styles */
.admin-table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background: white;
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 20px 15px;
}

.admin-table .table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.admin-table .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.admin-table .table tbody td {
    padding: 15px;
    vertical-align: middle;
    border: none;
}

/* Button Styles */
.btn-admin {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
}

.btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-admin-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-admin-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-admin-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.btn-admin-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

/* Badge Styles */
.admin-badge {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Styles */
.admin-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.admin-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.admin-form .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

/* Search and Filter Styles */
.search-filter-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.search-filter-card .card-body {
    padding: 25px;
}

/* Pagination Styles */
.admin-pagination .page-link {
    border: none;
    color: #667eea;
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-pagination .page-link:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

.admin-pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Toast Notifications */
.toast-container {
    z-index: 9999;
}

.toast {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Loading Spinner */
.admin-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .admin-sidebar.show {
        left: 0;
    }
    
    .admin-content {
        margin-left: 0;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .admin-table {
        font-size: 0.9rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .admin-card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .admin-table {
        background-color: #2d3748;
    }
    
    .admin-table .table tbody tr:hover {
        background-color: #4a5568;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
}

/* Booking Management Styles */
.icon-box {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.icon-box:hover {
    transform: scale(1.1);
}

/* Table row highlighting for statuses */
.table tr.status-cancelled {
    background-color: rgba(220, 53, 69, 0.05);
}

.table tr.status-completed {
    background-color: rgba(40, 167, 69, 0.05);
}

.table tr.status-upcoming {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Ticket card styles */
.ticket-card {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    transition: all 0.3s;
    margin-bottom: 20px;
}

.ticket-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.ticket-card .card-header {
    padding: 15px 20px;
    border-bottom: none;
}

.ticket-card .card-body {
    padding: 20px;
}

.ticket-card .ticket-status {
    position: absolute;
    top: 15px;
    right: 15px;
}

/* Seat map styles */
.seat-map {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.seat {
    aspect-ratio: 1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
}

.seat:hover {
    transform: scale(1.1);
}

.seat-available {
    background-color: #e9ecef;
    color: #495057;
}

.seat-booked {
    background-color: #dc3545;
    color: white;
}

.seat-selected {
    background-color: #28a745;
    color: white;
}

/* Filtering and search box */
.filter-box {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

/* Custom styling for booking info panels */
.booking-info-panel {
    border-left: 4px solid #667eea;
    background-color: #f8f9fa;
    border-radius: 0 8px 8px 0;
    padding: 15px;
    margin-bottom: 15px;
}

.booking-info-panel.success {
    border-left-color: #28a745;
}

.booking-info-panel.warning {
    border-left-color: #ffc107;
}

.booking-info-panel.danger {
    border-left-color: #dc3545;
}

/* Timeline styles for booking history */
.booking-timeline {
    position: relative;
    padding-left: 30px;
}

.booking-timeline:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 0;
    width: 2px;
    height: 100%;
    background-color: #dee2e6;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-badge {
    position: absolute;
    left: -30px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #667eea;
    border: 3px solid white;
    box-shadow: 0 0 0 1px #dee2e6;
    z-index: 1;
}

.timeline-content {
    padding: 15px;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
