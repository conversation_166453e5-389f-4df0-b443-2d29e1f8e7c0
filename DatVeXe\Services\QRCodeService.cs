using System.Text.Json;
using DatVeXe.Models;

namespace DatVeXe.Services
{
    public interface IQRCodeService
    {
        string GenerateTicketQRData(Ve ve);
        string GenerateTicketQRData(string ticketCode, string passengerName, string phone, 
            int tripId, string seatNumber, DateTime departureTime, string route, decimal amount);
        TicketQRData? ParseTicketQRData(string qrData);
        bool ValidateTicketQR(string qrData, out TicketQRData? ticketData, out string errorMessage);
    }

    public class QRCodeService : IQRCodeService
    {
        private readonly ILogger<QRCodeService> _logger;

        public QRCodeService(ILogger<QRCodeService> logger)
        {
            _logger = logger;
        }

        public string GenerateTicketQRData(Ve ve)
        {
            // Tạo format thân thiện với người dùng cho QR Scanner apps
            var trangThaiText = ve.VeTrangThai switch
            {
                TrangThaiVe.DaDat => "Đã đặt",
                TrangThaiVe.DaThanhToan => "Đã thanh toán",
                TrangThaiVe.DaSuDung => "Đã sử dụng",
                TrangThaiVe.DaHoanThanh => "Đã hoàn thành",
                TrangThaiVe.DaHuy => "Đã hủy",
                TrangThaiVe.DaHoanTien => "Đã hoàn tiền",
                _ => "Không xác định"
            };

            var qrText = $@"🎫 VÉ XE KHÁCH
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Mã vé: {ve.MaVe}
👤 Hành khách: {ve.TenKhach}
📞 Điện thoại: {ve.SoDienThoai}
🚌 Tuyến: {(ve.ChuyenXe != null ? $"{ve.ChuyenXe.DiemDiDisplay} → {ve.ChuyenXe.DiemDenDisplay}" : "")}
📅 Ngày đi: {ve.ChuyenXe?.NgayKhoiHanh:dd/MM/yyyy HH:mm}
💺 Ghế số: {ve.ChoNgoi?.SoGhe ?? "Chưa chọn"}
💰 Giá vé: {ve.GiaVe:N0} VNĐ
📊 Trạng thái: {trangThaiText}
📆 Ngày đặt: {ve.NgayDat:dd/MM/yyyy HH:mm}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Vui lòng xuất trình vé này khi lên xe";

            return qrText;
        }

        public string GenerateTicketQRData(string ticketCode, string passengerName, string phone,
            int tripId, string seatNumber, DateTime departureTime, string route, decimal amount)
        {
            var qrText = $@"🎫 VÉ XE KHÁCH
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Mã vé: {ticketCode}
👤 Hành khách: {passengerName}
📞 Điện thoại: {phone}
🚌 Tuyến: {route}
📅 Ngày đi: {departureTime:dd/MM/yyyy HH:mm}
💺 Ghế số: {seatNumber}
💰 Giá vé: {amount:N0} VNĐ
📊 Trạng thái: Đã đặt
📆 Ngày đặt: {DateTime.Now:dd/MM/yyyy HH:mm}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Vui lòng xuất trình vé này khi lên xe";

            return qrText;
        }

        public TicketQRData? ParseTicketQRData(string qrData)
        {
            try
            {
                // Thử parse JSON format trước (format cũ)
                if (qrData.TrimStart().StartsWith("{"))
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    };

                    return JsonSerializer.Deserialize<TicketQRData>(qrData, options);
                }

                // Parse format text mới
                if (qrData.Contains("🎫 VÉ XE KHÁCH"))
                {
                    return ParseTextFormatQRData(qrData);
                }

                // Parse format cũ dạng TICKET:|PASSENGER: (fallback)
                if (qrData.Contains("TICKET:") && qrData.Contains("PASSENGER:"))
                {
                    return ParseLegacyFormatQRData(qrData);
                }

                _logger.LogWarning("Unknown QR data format: {QRData}", qrData.Substring(0, Math.Min(100, qrData.Length)));
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing QR data: {QRData}", qrData.Substring(0, Math.Min(100, qrData.Length)));
                return null;
            }
        }

        private TicketQRData ParseTextFormatQRData(string qrData)
        {
            var ticketData = new TicketQRData();
            var lines = qrData.Split('\n');

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("📋 Mã vé:"))
                    ticketData.TicketCode = trimmedLine.Substring("📋 Mã vé:".Length).Trim();
                else if (trimmedLine.StartsWith("👤 Hành khách:"))
                    ticketData.PassengerName = trimmedLine.Substring("👤 Hành khách:".Length).Trim();
                else if (trimmedLine.StartsWith("📞 Điện thoại:"))
                    ticketData.Phone = trimmedLine.Substring("📞 Điện thoại:".Length).Trim();
                else if (trimmedLine.StartsWith("🚌 Tuyến:"))
                    ticketData.Route = trimmedLine.Substring("🚌 Tuyến:".Length).Trim();
                else if (trimmedLine.StartsWith("💺 Ghế số:"))
                    ticketData.SeatNumber = trimmedLine.Substring("💺 Ghế số:".Length).Trim();
                else if (trimmedLine.StartsWith("💰 Giá vé:"))
                {
                    var amountStr = trimmedLine.Substring("💰 Giá vé:".Length).Replace("VNĐ", "").Replace(",", "").Trim();
                    if (decimal.TryParse(amountStr, out decimal amount))
                        ticketData.Amount = amount;
                }
                else if (trimmedLine.StartsWith("📅 Ngày đi:"))
                {
                    var dateStr = trimmedLine.Substring("📅 Ngày đi:".Length).Trim();
                    if (DateTime.TryParseExact(dateStr, "dd/MM/yyyy HH:mm", null, System.Globalization.DateTimeStyles.None, out DateTime date))
                        ticketData.DepartureTime = date;
                }
                else if (trimmedLine.StartsWith("📊 Trạng thái:"))
                    ticketData.Status = trimmedLine.Substring("📊 Trạng thái:".Length).Trim();
            }

            return ticketData;
        }

        private TicketQRData ParseLegacyFormatQRData(string qrData)
        {
            var ticketData = new TicketQRData();
            var parts = qrData.Split('|');

            foreach (var part in parts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length == 2)
                {
                    var key = keyValue[0].Trim();
                    var value = keyValue[1].Trim();

                    switch (key)
                    {
                        case "TICKET":
                            ticketData.TicketCode = value;
                            break;
                        case "PASSENGER":
                            ticketData.PassengerName = value;
                            break;
                        case "PHONE":
                            ticketData.Phone = value;
                            break;
                        case "TRIP":
                            ticketData.Route = value;
                            break;
                        case "SEAT":
                            ticketData.SeatNumber = value;
                            break;
                        case "DATE":
                            if (DateTime.TryParseExact(value, "yyyy-MM-dd HH:mm", null, System.Globalization.DateTimeStyles.None, out DateTime date))
                                ticketData.DepartureTime = date;
                            break;
                    }
                }
            }

            return ticketData;
        }

        public bool ValidateTicketQR(string qrData, out TicketQRData? ticketData, out string errorMessage)
        {
            ticketData = null;
            errorMessage = string.Empty;

            try
            {
                ticketData = ParseTicketQRData(qrData);
                if (ticketData == null)
                {
                    errorMessage = "Không thể đọc thông tin QR code";
                    return false;
                }

                // Validate required fields
                if (string.IsNullOrEmpty(ticketData.TicketCode))
                {
                    errorMessage = "Mã vé không hợp lệ";
                    return false;
                }

                if (string.IsNullOrEmpty(ticketData.PassengerName))
                {
                    errorMessage = "Tên hành khách không hợp lệ";
                    return false;
                }

                if (ticketData.TripId <= 0)
                {
                    errorMessage = "Thông tin chuyến xe không hợp lệ";
                    return false;
                }

                // Validate departure time
                if (ticketData.DepartureTime == DateTime.MinValue)
                {
                    errorMessage = "Thời gian khởi hành không hợp lệ";
                    return false;
                }

                // Check if ticket is expired (for demo, allow tickets up to 1 day after departure)
                if (ticketData.DepartureTime.AddDays(1) < DateTime.Now)
                {
                    errorMessage = "Vé đã hết hạn";
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating QR data");
                errorMessage = "Lỗi xử lý QR code";
                return false;
            }
        }
    }

    public class TicketQRData
    {
        public string TicketCode { get; set; } = string.Empty;
        public string PassengerName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public int TripId { get; set; }
        public string SeatNumber { get; set; } = string.Empty;
        public DateTime DepartureTime { get; set; }
        public string Route { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime IssueTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
    }
}
