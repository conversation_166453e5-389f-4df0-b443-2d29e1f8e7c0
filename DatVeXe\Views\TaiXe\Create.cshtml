@model DatVeXe.Models.TaiXe

@{
    ViewData["Title"] = "Thêm Tài xế mới";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus mr-2"></i>
                        Thêm Tài xế mới
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <!-- Thông tin cá nhân -->
                            <div class="col-md-6">
                                <h5 class="mb-3"><i class="fas fa-user"></i> Thông tin cá nhân</h5>
                                
                                <div class="form-group">
                                    <label asp-for="HoTen" class="control-label"></label>
                                    <input asp-for="HoTen" class="form-control" placeholder="Nhập họ tên đầy đủ" />
                                    <span asp-validation-for="HoTen" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="SoDienThoai" class="control-label"></label>
                                    <input asp-for="SoDienThoai" class="form-control" placeholder="Ví dụ: 0901234567" />
                                    <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="DiaChi" class="control-label"></label>
                                    <textarea asp-for="DiaChi" class="form-control" rows="3" placeholder="Nhập địa chỉ thường trú"></textarea>
                                    <span asp-validation-for="DiaChi" class="text-danger"></span>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="NgaySinh" class="control-label"></label>
                                            <input asp-for="NgaySinh" type="date" class="form-control" />
                                            <span asp-validation-for="NgaySinh" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="GioiTinh" class="control-label"></label>
                                            <select asp-for="GioiTinh" class="form-control">
                                                <option value="">-- Chọn giới tính --</option>
                                                <option value="Nam">Nam</option>
                                                <option value="Nữ">Nữ</option>
                                            </select>
                                            <span asp-validation-for="GioiTinh" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label asp-for="CMND" class="control-label"></label>
                                    <input asp-for="CMND" class="form-control" placeholder="Nhập số CMND/CCCD" />
                                    <span asp-validation-for="CMND" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Thông tin bằng lái -->
                            <div class="col-md-6">
                                <h5 class="mb-3"><i class="fas fa-id-card"></i> Thông tin bằng lái xe</h5>
                                
                                <div class="form-group">
                                    <label asp-for="SoBangLai" class="control-label"></label>
                                    <input asp-for="SoBangLai" class="form-control" placeholder="Nhập số bằng lái xe" />
                                    <span asp-validation-for="SoBangLai" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="LoaiBangLai" class="control-label"></label>
                                    <select asp-for="LoaiBangLai" class="form-control">
                                        <option value="">-- Chọn loại bằng lái --</option>
                                        <option value="1">B1 - Xe ô tô dưới 9 chỗ</option>
                                        <option value="2">B2 - Xe ô tô dưới 9 chỗ và xe tải dưới 3.5 tấn</option>
                                        <option value="3">C - Xe tải và xe khách dưới 30 chỗ</option>
                                        <option value="4">D - Xe khách trên 30 chỗ</option>
                                        <option value="5">E - Xe container, xe đầu kéo</option>
                                        <option value="6">F - Xe chuyên dụng</option>
                                    </select>
                                    <span asp-validation-for="LoaiBangLai" class="text-danger"></span>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="NgayCapBangLai" class="control-label"></label>
                                            <input asp-for="NgayCapBangLai" type="date" class="form-control" />
                                            <span asp-validation-for="NgayCapBangLai" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="NgayHetHanBangLai" class="control-label"></label>
                                            <input asp-for="NgayHetHanBangLai" type="date" class="form-control" />
                                            <span asp-validation-for="NgayHetHanBangLai" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label asp-for="KinhNghiem" class="control-label"></label>
                                    <input asp-for="KinhNghiem" type="number" min="0" max="50" class="form-control" placeholder="Số năm kinh nghiệm lái xe" />
                                    <span asp-validation-for="KinhNghiem" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Thông tin công việc -->
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3"><i class="fas fa-briefcase"></i> Thông tin công việc</h5>
                                
                                <div class="form-group">
                                    <label asp-for="NgayVaoLam" class="control-label"></label>
                                    <input asp-for="NgayVaoLam" type="date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
                                    <span asp-validation-for="NgayVaoLam" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <label asp-for="TrangThai" class="control-label"></label>
                                    <select asp-for="TrangThai" class="form-control">
                                        <option value="1" selected>Hoạt động</option>
                                        <option value="2">Nghỉ phép</option>
                                        <option value="3">Tạm nghỉ</option>
                                        <option value="4">Đã nghỉ việc</option>
                                    </select>
                                    <span asp-validation-for="TrangThai" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3"><i class="fas fa-sticky-note"></i> Ghi chú</h5>
                                
                                <div class="form-group">
                                    <label asp-for="GhiChu" class="control-label"></label>
                                    <textarea asp-for="GhiChu" class="form-control" rows="5" placeholder="Nhập ghi chú về tài xế (nếu có)"></textarea>
                                    <span asp-validation-for="GhiChu" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Lưu thông tin
                                </button>
                                <a asp-action="Index" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Hủy bỏ
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Các trường có dấu * là bắt buộc
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Validation cho số điện thoại
            $('#SoDienThoai').on('input', function() {
                var phone = $(this).val();
                var phoneRegex = /^(0[0-9]{9})$/;
                
                if (phone && !phoneRegex.test(phone)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">Số điện thoại phải có 10 số và bắt đầu bằng số 0</div>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Validation cho CMND
            $('#CMND').on('input', function() {
                var cmnd = $(this).val();
                var cmndRegex = /^[0-9]{9,12}$/;
                
                if (cmnd && !cmndRegex.test(cmnd)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<div class="invalid-feedback">CMND/CCCD phải từ 9-12 chữ số</div>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Kiểm tra ngày hết hạn bằng lái phải sau ngày cấp
            $('#NgayCapBangLai, #NgayHetHanBangLai').on('change', function() {
                var ngayCap = new Date($('#NgayCapBangLai').val());
                var ngayHetHan = new Date($('#NgayHetHanBangLai').val());
                
                if (ngayCap && ngayHetHan && ngayHetHan <= ngayCap) {
                    $('#NgayHetHanBangLai').addClass('is-invalid');
                    if (!$('#NgayHetHanBangLai').next('.invalid-feedback').length) {
                        $('#NgayHetHanBangLai').after('<div class="invalid-feedback">Ngày hết hạn phải sau ngày cấp bằng lái</div>');
                    }
                } else {
                    $('#NgayHetHanBangLai').removeClass('is-invalid');
                    $('#NgayHetHanBangLai').next('.invalid-feedback').remove();
                }
            });
        });
    </script>
}
