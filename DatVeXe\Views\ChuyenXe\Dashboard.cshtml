@{
    ViewData["Title"] = "Thống kê chuyến xe";
}

<div class="container py-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
        <h2 class="fw-bold text-primary mb-0 text-uppercase">Thống kê chuyến xe</h2>
        <div class="d-flex gap-2">
            <a asp-action="Index" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> Danh sách chuyến xe
            </a>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Thêm chuyến xe
            </a>
        </div>
    </div>

    <!-- Thống kê tổng quan -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-bus fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.TongChuyenXe</h3>
                    <p class="card-text">Tổng chuyến xe</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.ChuyenXeHomNay</h3>
                    <p class="card-text">Chuyến xe hôm nay</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.ChuyenXeThangNay</h3>
                    <p class="card-text">Chuyến xe tháng này</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <h3 class="card-title">@ViewBag.ChuyenXeChuaKhoiHanh</h3>
                    <p class="card-text">Chưa khởi hành</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê theo tuyến đường và loại xe -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-route"></i> Top 5 tuyến đường phổ biến
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.TopTuyenDuong != null && ((IEnumerable<dynamic>)ViewBag.TopTuyenDuong).Any())
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var tuyen in (IEnumerable<dynamic>)ViewBag.TopTuyenDuong)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <strong>@tuyen.TuyenDuong</strong>
                                        <br>
                                        <small class="text-muted">@tuyen.TongVe vé đã bán - @tuyen.DoanhThu.ToString("N0") VNĐ</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">@tuyen.SoChuyenXe</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted text-center">Chưa có dữ liệu</p>
                    }
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bus"></i> Thống kê theo loại xe
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.ThongKeLoaiXe != null && ((IEnumerable<dynamic>)ViewBag.ThongKeLoaiXe).Any())
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var loaiXe in (IEnumerable<dynamic>)ViewBag.ThongKeLoaiXe)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <strong>@loaiXe.LoaiXe</strong>
                                        <br>
                                        <small class="text-muted">@loaiXe.TongVe vé - @loaiXe.DoanhThu.ToString("N0") VNĐ</small>
                                    </div>
                                    <span class="badge bg-success rounded-pill">@loaiXe.SoChuyenXe</span>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted text-center">Chưa có dữ liệu</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê nhanh -->
    <div class="card">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">
                <i class="fas fa-chart-line"></i> Thống kê nhanh tháng này
            </h6>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="border-end">
                        <h4 class="text-primary">@ViewBag.ChuyenXeThangNay</h4>
                        <p class="text-muted">Chuyến xe</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border-end">
                        <h4 class="text-success">
                            @{
                                var tongVeThangNay = ViewBag.TopTuyenDuong != null ?
                                    ((IEnumerable<dynamic>)ViewBag.TopTuyenDuong).Sum(t => t.TongVe) : 0;
                            }
                            @tongVeThangNay
                        </h4>
                        <p class="text-muted">Vé đã bán</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border-end">
                        <h4 class="text-warning">
                            @{
                                var doanhThuThangNay = ViewBag.TopTuyenDuong != null ?
                                    ((IEnumerable<dynamic>)ViewBag.TopTuyenDuong).Sum(t => t.DoanhThu) : 0;
                            }
                            @doanhThuThangNay.ToString("N0")
                        </h4>
                        <p class="text-muted">Doanh thu (VNĐ)</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <h4 class="text-info">
                        @{
                            var tyLeLayDay = tongVeThangNay > 0 && ViewBag.ChuyenXeThangNay > 0 ?
                                (double)tongVeThangNay / (ViewBag.ChuyenXeThangNay * 40) * 100 : 0;
                        }
                        @tyLeLayDay.ToString("F1")%
                    </h4>
                    <p class="text-muted">Tỷ lệ lấp đầy</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .progress {
            background-color: #e9ecef;
        }
        .list-group-item {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }
        .list-group-item:last-child {
            border-bottom: none;
        }
    </style>
}
