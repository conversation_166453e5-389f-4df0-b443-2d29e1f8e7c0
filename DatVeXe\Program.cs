using DatVeXe.Models;
using DatVeXe.Services;
using DatVeXe.Middleware;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Đăng ký DbContext sử dụng SQL Server LocalDB
builder.Services.AddDbContext<DatVeXeContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Cấu hình Email Settings
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));

// Cấu hình MoMo Settings
builder.Services.Configure<MoMoConfig>(builder.Configuration.GetSection("MoMo"));

// Đăng ký Email Service
builder.Services.AddScoped<IEmailService, EmailService>();

// Đăng ký SMS Service
builder.Services.AddScoped<ISMSService, SMSService>();

// Đăng ký Payment Service và VNPay Helper
builder.Services.AddScoped<VNPayHelper>();
builder.Services.AddHttpClient<IMoMoHelper, MoMoHelper>();
builder.Services.AddScoped<IMoMoHelper, MoMoHelper>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddHttpContextAccessor();

// Đăng ký Trip Search Service
builder.Services.AddScoped<ITripSearchService, TripSearchService>();

// Đăng ký QR Code Service
builder.Services.AddScoped<IQRCodeService, QRCodeService>();

// Đăng ký Ticket Cancellation Service
builder.Services.AddScoped<ITicketCancellationService, TicketCancellationService>();

// Đăng ký Background Service để dọn dẹp reservation hết hạn
builder.Services.AddHostedService<SeatReservationCleanupService>();

// Thêm hỗ trợ Session
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30); // Thời gian timeout của session
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Thêm Authentication services
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = "Cookies";
})
.AddCookie("Cookies")
.AddGoogle(options =>
{
    options.ClientId = builder.Configuration["Authentication:Google:ClientId"];
    options.ClientSecret = builder.Configuration["Authentication:Google:ClientSecret"];
    options.CallbackPath = "/signin-google";
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// Add global exception handling middleware
app.UseGlobalExceptionHandling();

// Comment out HTTPS redirection for development testing
// app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Sử dụng Authentication và Authorization
app.UseAuthentication();
app.UseAuthorization();

// Sử dụng Session
app.UseSession();

// Sử dụng middleware kiểm tra trạng thái người dùng
app.UseUserStatusCheck();

// Cấu hình routing cho Admin Area
app.MapControllerRoute(
    name: "admin",
    pattern: "{area:exists}/{controller=Admin}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
