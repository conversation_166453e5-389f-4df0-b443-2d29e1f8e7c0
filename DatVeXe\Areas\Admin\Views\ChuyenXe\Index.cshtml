@model IEnumerable<DatVeXe.Models.ChuyenXe>
@{
    ViewData["Title"] = "Quản lý chuyến xe";
}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-calendar-alt" style="color: #34495e;"></i>
        Quản lý chuyến xe
    </h3>
    <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Create" class="btn" style="background-color: #34495e; border-color: #34495e; color: white;">
        <i class="fas fa-plus"></i>
        Thêm chuyến xe
    </a>
</div>
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>ID</th>
                        <th>Xe</th>
                        <th><PERSON><PERSON><PERSON><PERSON> đường</th>
                        <th>Ng<PERSON>y khởi hành</th>
                        <th>Thời gian đi</th>
                        <th>Giá vé</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var chuyenXe in Model)
                    {
                        <tr>
                            <td>@chuyenXe.ChuyenXeId</td>
                            <td>@chuyenXe.Xe?.BienSo</td>
                            <td>@chuyenXe.TuyenDuong?.TenTuyen</td>
                            <td>@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</td>
                            <td>@chuyenXe.ThoiGianDi.ToString(@"hh\:mm")</td>
                            <td>@chuyenXe.Gia.ToString("N0") VNĐ</td>
                            <td>
                                @if (chuyenXe.NgayKhoiHanh > DateTime.Now)
                                {
                                    <span class="badge" style="background-color: #3498db; color: white;">Sắp khởi hành</span>
                                }
                                else if (chuyenXe.NgayKhoiHanh.Date == DateTime.Now.Date)
                                {
                                    <span class="badge" style="background-color: #f39c12; color: white;">Hôm nay</span>
                                }
                                else
                                {
                                    <span class="badge" style="background-color: #95a5a6; color: white;">Đã hoàn thành</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm" style="border: 1px solid #3498db; color: #3498db;" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Edit" asp-route-id="@chuyenXe.ChuyenXeId" class="btn btn-sm" style="border: 1px solid #f39c12; color: #f39c12;" title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form asp-area="Admin" asp-controller="ChuyenXe" asp-action="Delete" asp-route-id="@chuyenXe.ChuyenXeId" method="post" style="display:inline;" onsubmit="return confirm('Bạn có chắc chắn muốn xóa chuyến xe này?');">
                                        <button type="submit" class="btn btn-sm" style="border: 1px solid #e74c3c; color: #e74c3c;" title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="mt-3">
    <small class="text-muted">
        Tổng cộng: @Model.Count() chuyến xe
    </small>
</div>
