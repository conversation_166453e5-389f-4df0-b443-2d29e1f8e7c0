@{
    ViewData["Title"] = "Đăng nhập";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Đặt Vé Xe</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
            height: 100vh;
            overflow-x: hidden;
            background-color: #f5f5f5;
        }

        .login-page {
            display: flex;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .login-image {
            flex: 1;
            background: url('/images/mancity.webp') center/cover no-repeat;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .login-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(10, 38, 64, 0.9) 0%, rgba(10, 38, 64, 0.7) 50%, rgba(108, 171, 221, 0.6) 100%);
            z-index: 1;
        }

        .login-image-content {
            position: relative;
            z-index: 2;
            max-width: 80%;
        }

        .login-logo-container {
            width: 120px;
            height: 120px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            border: 3px solid #6cabdd;
        }

        .login-logo-icon {
            font-size: 60px;
            color: #6cabdd;
        }

        .login-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            color: #6cabdd;
        }

        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0 auto;
            line-height: 1.6;
        }

        .login-form-section {
            flex: 1;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 3rem 2rem;
        }

        .login-form-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 2rem;
        }

        .login-tab {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            color: #6c757d;
            cursor: pointer;
            position: relative;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .login-tab.active {
            color: #6cabdd;
            font-weight: 700;
            border-bottom: 3px solid #6cabdd;
        }

        .login-form-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0c2340;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #0c2340;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #6cabdd;
            box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.15);
            outline: none;
        }

        .password-input-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 0.25rem;
            transition: color 0.2s;
        }

        .password-toggle:hover {
            color: #0c2340;
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: #6cabdd;
            text-decoration: none;
            font-size: 0.9rem;
            margin-top: 0.75rem;
            font-weight: 500;
            transition: color 0.2s;
        }

        .forgot-password:hover {
            color: #0c2340;
            text-decoration: underline;
        }

        .input-with-icon {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
            z-index: 1;
        }

        .input-with-icon .form-control,
        .password-input-container .form-control {
            padding-left: 40px;
        }

        .login-button {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #6cabdd;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1.5rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .login-button:hover {
            background-color: #0c2340;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .register-section {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e0e0e0;
        }

        .register-text {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 1rem;
        }

        .register-link {
            display: inline-block;
            padding: 0.75rem 2rem;
            background-color: #0c2340;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .register-link:hover {
            background-color: #6cabdd;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Google Login Button */
        .google-login-section {
            text-align: center;
            margin: 1.5rem 0;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }

        .divider-text {
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .google-login-button {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #fff;
            color: #333;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .google-login-button:hover {
            background-color: #f8f9fa;
            border-color: #6cabdd;
            color: #333;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Google Login Button */
        .google-login-section {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }

        .divider-text {
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .google-login-button {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #fff;
            color: #333;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .google-login-button:hover {
            background-color: #f8f9fa;
            border-color: #6cabdd;
            color: #333;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .google-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgPHBhdGggZD0iTTE3LjY0IDkuMjA0NTQ1NDVjMC0uNjM5NzI3MjctLjA1NzI3MjczLTEuMjUyMjcyNzMtLjE2MzYzNjM2LTEuODQwOTA5MDlIOXY0LjQ4NjM2MzY0aDQuODQ0MDkwOTFjLS4yMDQ1NDU0NSAxLjEyNS0uODI5NTQ1NDUgMi4wNzk1NDU0NS0xLjc2MTM2MzY0IDIuNzE1OTA5MDl2Mi4yNTgxODE4MmgzLjA0MDkwOTA5YzEuNjY4MTgxODItMS4yNTIyNzI3MyAyLjYzNDA5MDkxLTMuMDk1NDU0NTUgMi42MzQwOTA5MS01LjI3MjcyNzI3eiIgZmlsbD0iIzQyODVGNCIvPgogICAgPHBhdGggZD0iTTkgMThjMi40MzE4MTgxOCAwIDQuNDY4MTgxODItLjc5NTQ1NDU1IDUuOTU2MzYzNjQtMi4xNTIyNzI3M2wtMy4wNDA5MDkwOS0yLjI1ODE4MTgyYy0uODM0MDkwOTEuNTU5MDkwOTEtMS45MDIyNzI3My44ODYzNjM2NC0yLjkxNTQ1NDU1Ljg4NjM2MzY0LTIuMjQwOTA5MDkgMC00LjE0MzE4MTgyLTEuNTEzNjM2MzYtNC44MjI3MjcyNy0zLjU0NTQ1NDU1SDBWMTMuNzVjMS40NDMxODE4MiAyLjg2MzYzNjM2IDQuNDA0NTQ1NDUgNC44NjM2MzYzNiA5IDQuODYzNjM2MzZ6IiBmaWxsPSIjMzRBODUzIi8+CiAgICA8cGF0aCBkPSJNMy45NTQ1NDU0NSAxMC42NzA0NTQ1NWMtLjE3MDQ1NDU1LS41NTkwOTA5MS0uMjcyNzI3MjctMS4xNTkwOTA5MS0uMjcyNzI3MjctMS43NzA0NTQ1NXMuMTAyMjcyNzMtMS4yMTEzNjM2NC4yNzI3MjcyNy0xLjc3MDQ1NDU1VjQuMzQwOTA5MDlIMGMtLjYwNjgxODE4IDEuMjA0NTQ1NDUtLjk1NDU0NTQ1IDIuNTY4MTgxODItLjk1NDU0NTQ1IDQuMDU5MDkwOTFzLjM0NzcyNzI3IDIuODU0NTQ1NDUuOTU0NTQ1NDUgNC4wNTkwOTA5MWwzLjk1NDU0NTQ1LTMuMDc5NTQ1NDV6IiBmaWxsPSIjRkJCQzA1Ii8+CiAgICA8cGF0aCBkPSJNOSAzLjU3OTU0NTQ1YzEuMzIxMzYzNjQgMCAyLjUwOTA5MDkxLjQ1NDU0NTQ1IDMuNDQwOTA5MDkgMS4zNDc3MjcyN2wyLjU4MTgxODItMi41ODE4MTgyQzEzLjQ2MzYzNjM2LjgwNDU0NTQ1NSAxMS40MjcyNzI3IDAgOSAwIDQuNDA0NTQ1NDUgMCAxLjQ0MzE4MTgyIDEuOTk1NDU0NTUgMCA0Ljg1OTA5MDkxbDMuOTU0NTQ1NDUgMy4wNzk1NDU0NWMuNjc5NTQ1NDUtMi4wMzE4MTgxOCAyLjU4MTgxODItMy41NDU0NTQ1NSA0LjgyMjcyNzI3LTMuNTQ1NDU0NTV6IiBmaWxsPSIjRUE0MzM1Ii8+CiAgPC9nPgo8L3N2Zz4K');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        /* Fix cho checkbox và label */
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .form-check-input {
            margin-right: 0.5rem;
            margin-top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid #6cabdd;
            border-radius: 3px;
            background-color: white;
        }

        .form-check-input:checked {
            background-color: #6cabdd;
            border-color: #6cabdd;
        }

        .form-check-input:focus {
            border-color: #6cabdd;
            box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.25);
        }

        .form-check-label {
            color: #0c2340;
            font-weight: 500;
            font-size: 0.95rem;
            cursor: pointer;
            user-select: none;
        }

        .form-check-label:hover {
            color: #6cabdd;
        }

        @@media (max-width: 768px) {
            .login-page {
                flex-direction: column;
            }

            .login-image {
                min-height: 200px;
            }

            .login-form-container {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-page">
        <div class="login-image">
            <div class="login-image-content">
                <div class="login-logo-container">
                    <i class="bi bi-bus-front login-logo-icon"></i>
                </div>
                <h1 class="login-title">Đặt Vé Xe</h1>
                <p class="login-subtitle">
                    Hệ thống đặt vé xe trực tuyến hiện đại, tiện lợi và an toàn. Đặt vé nhanh chóng, thanh toán dễ dàng và trải nghiệm hành trình tuyệt vời.
                </p>
            </div>
        </div>

        <div class="login-form-section">
            <div class="login-form-container">
                <div class="login-tabs">
                    <a href="/TaiKhoan/DangNhap" class="login-tab active">Đăng nhập</a>
                    <a href="/TaiKhoan/DangKy" class="login-tab">Đăng ký</a>
                </div>

                <h2 class="login-form-title">Đăng nhập tài khoản</h2>

                @if (TempData["ThongBao"] != null)
                {
                    <div class="alert alert-danger mb-4">
                        @TempData["ThongBao"]
                    </div>
                }

                <form asp-action="DangNhap" method="post">
                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-with-icon">
                            <i class="bi bi-envelope input-icon"></i>
                            <input type="email" id="email" name="email" class="form-control" placeholder="Nhập địa chỉ email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="matKhau" class="form-label">Mật khẩu</label>
                        <div class="password-input-container">
                            <i class="bi bi-lock input-icon"></i>
                            <input type="password" id="matKhau" name="matKhau" class="form-control" placeholder="Nhập mật khẩu" required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <a href="#" class="forgot-password">Quên mật khẩu?</a>
                    </div>

                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                        <label class="form-check-label" for="rememberMe">Ghi nhớ đăng nhập</label>
                    </div>

                    <button type="submit" class="login-button">Đăng nhập</button>
                </form>

                <div class="google-login-section">
                    <div class="divider">
                        <span class="divider-text">Hoặc</span>
                    </div>
                    <a href="/TaiKhoan/GoogleLogin" class="google-login-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Đăng nhập bằng Google
                    </a>
                </div>

                <div class="register-section">
                    <p class="register-text">Chưa có tài khoản?</p>
                    <a href="/TaiKhoan/DangKy" class="register-link">Đăng ký ngay</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('matKhau');
            const icon = document.querySelector('.password-toggle i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('matKhau');

            form.addEventListener('submit', function(event) {
                let isValid = true;

                // Validate email
                if (!emailInput.value.trim()) {
                    isValid = false;
                    showError(emailInput, 'Vui lòng nhập địa chỉ email');
                } else if (!isValidEmail(emailInput.value)) {
                    isValid = false;
                    showError(emailInput, 'Địa chỉ email không hợp lệ');
                } else {
                    clearError(emailInput);
                }

                // Validate password
                if (!passwordInput.value.trim()) {
                    isValid = false;
                    showError(passwordInput, 'Vui lòng nhập mật khẩu');
                } else {
                    clearError(passwordInput);
                }

                if (!isValid) {
                    event.preventDefault();
                }
            });

            // Helper functions
            function showError(input, message) {
                const formGroup = input.closest('.form-group');
                let errorDiv = formGroup.querySelector('.invalid-feedback');

                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    formGroup.appendChild(errorDiv);
                }

                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                input.classList.add('is-invalid');
            }

            function clearError(input) {
                const formGroup = input.closest('.form-group');
                const errorDiv = formGroup.querySelector('.invalid-feedback');

                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }

                input.classList.remove('is-invalid');
            }

            function isValidEmail(email) {
                // Simple email validation without regex
                if (!email) return false;

                // Check for at symbol
                var atSymbol = String.fromCharCode(64);
                var atIndex = email.indexOf(atSymbol);
                if (atIndex < 1) return false;

                // Check for domain with at least one dot
                var domain = email.substring(atIndex + 1);
                var dotIndex = domain.indexOf('.');
                if (dotIndex < 1 || dotIndex >= domain.length - 1) return false;

                // Check for spaces
                if (email.indexOf(' ') >= 0) return false;

                return true;
            }
        });
    </script>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
</body>
</html>
