@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Sửa chuyến xe";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit"></i> @ViewData["Title"]
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" id="editForm" novalidate>
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" style="display:none;"></div>
                        <input type="hidden" asp-for="ChuyenXeId" />

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DiemDi" class="form-label required"></label>
                                    <input asp-for="DiemDi" class="form-control" placeholder="Nhập điểm đi" />
                                    <span asp-validation-for="DiemDi" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DiemDen" class="form-label required"></label>
                                    <input asp-for="DiemDen" class="form-control" placeholder="Nhập điểm đến" />
                                    <span asp-validation-for="DiemDen" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="NgayKhoiHanh" class="form-label required"></label>
                                    <input asp-for="NgayKhoiHanh" class="form-control" type="datetime-local" />
                                    <span asp-validation-for="NgayKhoiHanh" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="XeId" class="form-label required">Chọn xe</label>
                                    <select asp-for="XeId" asp-items="ViewBag.XeList" class="form-select">
                                        <option value="">-- Chọn xe --</option>
                                    </select>
                                    <span asp-validation-for="XeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="TaiXeId" class="form-label">Chọn tài xế</label>
                                    <select asp-for="TaiXeId" asp-items="ViewBag.TaiXeList" class="form-select">
                                        <option value="">-- Chọn tài xế --</option>
                                    </select>
                                    <span asp-validation-for="TaiXeId" class="text-danger"></span>
                                    <small class="form-text text-muted">Có thể để trống và gán tài xế sau</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 d-flex gap-2">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Lưu thay đổi
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .required::after {
            content: " *";
            color: red;
        }
        .validation-summary-errors {
            display: block !important;
        }
        .validation-summary-errors ul {
            list-style: none;
            padding-left: 0;
            margin-bottom: 0;
        }
        .field-validation-error {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Set min date for NgayKhoiHanh to today
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            var hh = String(today.getHours()).padStart(2, '0');
            var mi = String(today.getMinutes()).padStart(2, '0');
            today = yyyy + '-' + mm + '-' + dd + 'T' + hh + ':' + mi;
            $('#NgayKhoiHanh').attr('min', today);

            // Hiển thị validation summary nếu có lỗi
            if ($('.validation-summary-errors').length > 0 || $('.field-validation-error').length > 0) {
                $('.validation-summary-errors').closest('.alert').show();
            }

            // Xử lý form submit
            $('#editForm').on('submit', function(e) {
                if (!$(this).valid()) {
                    e.preventDefault();
                    $('.validation-summary-errors').closest('.alert').show();
                    // Scroll to first error
                    var firstError = $('.field-validation-error').first();
                    if (firstError.length > 0) {
                        $('html, body').animate({
                            scrollTop: firstError.offset().top - 100
                        }, 200);
                    }
                }
            });
        });
    </script>
} 