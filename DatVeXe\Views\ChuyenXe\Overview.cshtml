@{
    ViewData["Title"] = "Tổng quan trang ChuyenXe";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bus"></i> Tổng quan trang ChuyenXe
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="text-success">✅ Các chức năng dành cho người dùng:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Tìm kiếm chuyến xe</strong> - Hiển thị danh sách chuyến xe có sẵn
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong><PERSON><PERSON> lọc nâng cao</strong> - <PERSON><PERSON><PERSON> theo điểm đi/đến, ng<PERSON>y, giá, loại xe, trạng thái
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Sắp xếp dữ liệu</strong> - Theo ngày, giá, điểm đi/đến, loại xe
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Chi tiết chuyến xe</strong> - Xem thông tin chi tiết, giá vé, tình trạng ghế
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Đặt vé trực tiếp</strong> - Nút "Đặt vé" liên kết với trang chọn chỗ ngồi
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Hiển thị trạng thái</strong> - Ghế trống, đã khởi hành, giá vé
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check text-success"></i>
                                    <strong>Giao diện thân thiện</strong> - Responsive, dễ sử dụng trên mobile
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-info"></i>
                                    <strong>Quản lý Admin</strong> - Chuyển hướng đến trang Admin riêng biệt
                                </li>
                            </ul>

                            <h5 class="text-info mt-4">🎯 Tối ưu cho người dùng:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-user text-info"></i>
                                    <strong>Chỉ hiển thị chức năng cần thiết</strong> - Loại bỏ các tính năng quản lý
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-search text-info"></i>
                                    <strong>Tập trung vào tìm kiếm</strong> - Giao diện đơn giản, dễ tìm chuyến xe
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-ticket-alt text-info"></i>
                                    <strong>Đặt vé nhanh chóng</strong> - Nút đặt vé rõ ràng, dễ nhấn
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-mobile-alt text-info"></i>
                                    <strong>Mobile-friendly</strong> - Tối ưu cho điện thoại di động
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-shield-alt text-info"></i>
                                    <strong>Bảo mật</strong> - Phân quyền rõ ràng, chuyển hướng Admin
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-link"></i> Liên kết nhanh
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a asp-action="Index" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Tìm kiếm chuyến xe
                                        </a>
                                        <a asp-controller="Home" asp-action="Index" class="btn btn-success">
                                            <i class="fas fa-home"></i> Về trang chủ
                                        </a>
                                        <a asp-controller="Booking" asp-action="Search" class="btn btn-info">
                                            <i class="fas fa-ticket-alt"></i> Đặt vé nhanh
                                        </a>
                                        <a href="/Admin" class="btn btn-warning">
                                            <i class="fas fa-cog"></i> Trang Admin
                                        </a>
                                        <a asp-controller="SeedData" asp-action="Index" class="btn btn-outline-secondary">
                                            <i class="fas fa-database"></i> Dữ liệu mẫu
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="card bg-light mt-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i> Thông tin kỹ thuật
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Controller:</strong> ChuyenXeController<br>
                                        <strong>Model:</strong> ChuyenXeSearchViewModel<br>
                                        <strong>Views:</strong> Index, Details, Overview<br>
                                        <strong>Features:</strong> Search, Filter, Sort, View Details, Book Tickets<br>
                                        <strong>Database:</strong> Entity Framework Core<br>
                                        <strong>UI Framework:</strong> Bootstrap 5<br>
                                        <strong>Target:</strong> End Users (Customers)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Features -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-search"></i> Demo tìm kiếm
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Thử các tính năng tìm kiếm:</p>
                    <div class="d-grid gap-2">
                        <a href="/ChuyenXe?DiemDi=TP.HCM" class="btn btn-outline-primary btn-sm">
                            Tìm chuyến từ TP.HCM
                        </a>
                        <a href="/ChuyenXe?DiemDen=Nha Trang" class="btn btn-outline-primary btn-sm">
                            Tìm chuyến đến Nha Trang
                        </a>
                        <a href="/ChuyenXe?LoaiXe=Limousine" class="btn btn-outline-primary btn-sm">
                            Tìm xe Limousine
                        </a>
                        <a href="/ChuyenXe?TrangThai=chua_khoi_hanh" class="btn btn-outline-primary btn-sm">
                            Chuyến chưa khởi hành
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-sort"></i> Demo sắp xếp
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Thử các tính năng sắp xếp:</p>
                    <div class="d-grid gap-2">
                        <a href="/ChuyenXe?SortBy=Gia&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Giá tăng dần
                        </a>
                        <a href="/ChuyenXe?SortBy=Gia&SortOrder=desc" class="btn btn-outline-success btn-sm">
                            Giá giảm dần
                        </a>
                        <a href="/ChuyenXe?SortBy=NgayKhoiHanh&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Ngày gần nhất
                        </a>
                        <a href="/ChuyenXe?SortBy=DiemDi&SortOrder=asc" class="btn btn-outline-success btn-sm">
                            Điểm đi A-Z
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .list-group-item {
            border: none;
            padding: 0.5rem 0;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn {
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
    </style>
}
