@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewData["Title"] = "Thống kê đặt vé";
}

@section Styles {
    <style>
        .stats-overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 25px;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stats-label {
            font-size: 0.9rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
        }

        .top-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }

        .top-item:hover {
            background-color: #f8f9fa;
        }

        .top-item:last-child {
            border-bottom: none;
        }

        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .metric-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
}

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a asp-area="Admin" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-home"></i> Admin
                </a>
            </li>
            <li class="breadcrumb-item">
                <a asp-area="Admin" asp-controller="Booking" asp-action="List">
                    Quản lý đặt vé
                </a>
            </li>
            <li class="breadcrumb-item active">Thống kê</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-gradient mb-1">
                <i class="fas fa-chart-bar me-2"></i>
                Thống kê đặt vé
            </h2>
            <p class="text-muted mb-0">Báo cáo và phân tích dữ liệu đặt vé</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-admin btn-admin-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt me-2"></i>
                Làm mới
            </button>
            <button class="btn btn-admin btn-admin-success" onclick="exportReport()">
                <i class="fas fa-download me-2"></i>
                Xuất báo cáo
            </button>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="stats-overview">
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-icon text-primary">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stats-number text-dark">@ViewBag.TongVe</div>
                    <div class="stats-label text-muted">Tổng số vé</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-icon text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number text-dark">@ViewBag.VeDaThanhToan</div>
                    <div class="stats-label text-muted">Vé đã thanh toán</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-icon text-danger">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stats-number text-dark">@ViewBag.VeDaHuy</div>
                    <div class="stats-label text-muted">Vé đã hủy</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-icon text-info">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stats-number text-dark">@ViewBag.VeThangNay</div>
                    <div class="stats-label text-muted">Vé tháng này</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-lg-8">
            <div class="stats-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Doanh thu theo thời gian
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Distribution -->
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart me-2"></i>
                        Phân bố trạng thái vé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
