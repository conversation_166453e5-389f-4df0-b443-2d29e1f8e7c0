/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-2xh0csjq8l] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-2xh0csjq8l] {
  color: #0077cc;
}

.btn-primary[b-2xh0csjq8l] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-2xh0csjq8l], .nav-pills .show > .nav-link[b-2xh0csjq8l] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-2xh0csjq8l] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-2xh0csjq8l] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-2xh0csjq8l] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-2xh0csjq8l] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-2xh0csjq8l] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
