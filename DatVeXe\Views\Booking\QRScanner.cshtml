@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Quét mã QR vé";
}

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h1 class="mt-4 mb-0"><i class="bi bi-qr-code-scan me-2"></i>Quét mã QR vé</h1>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                <li class="breadcrumb-item"><a href="@Url.Action("AdminBookingList", "Booking")">Quản lý đặt vé</a></li>
                <li class="breadcrumb-item active">Quét QR</li>
            </ol>
        </div>
        <div>
            <a href="@Url.Action("AdminBookingList", "Booking")" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- QR Scanner -->
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-camera me-1"></i>Quét mã QR</h5>
                </div>
                <div class="card-body">
                    <div class="qr-scanner-container">
                        <div id="qr-reader" style="width: 100%; min-height: 300px; border-radius: 8px; overflow: hidden;"></div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                Đưa mã QR vào khung hình để quét
                            </small>
                            <div class="mt-2">
                                <small class="text-info">
                                    💡 <strong>Mẹo:</strong> Nếu không quét được, thử button "Alt Scan" hoặc nhập mã vé thủ công
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 d-flex flex-wrap gap-2 justify-content-center">
                        <button id="start-scan" class="btn btn-success">
                            <i class="bi bi-play-fill me-1"></i>Bắt đầu quét
                        </button>
                        <button id="stop-scan" class="btn btn-danger" disabled>
                            <i class="bi bi-stop-fill me-1"></i>Dừng quét
                        </button>
                        <button id="switch-camera" class="btn btn-info" disabled>
                            <i class="bi bi-camera-reels me-1"></i>Đổi camera
                        </button>
                        <button id="test-qr" class="btn btn-secondary" onclick="testQRCode()">
                            <i class="bi bi-bug me-1"></i>Test QR
                        </button>
                        <button id="alt-scan" class="btn btn-warning" onclick="startAlternativeScanning()">
                            <i class="bi bi-qr-code-scan me-1"></i>Alt Scan
                        </button>
                    </div>
                    
                    <!-- Manual input -->
                    <hr class="my-4">
                    <div class="manual-input-section">
                        <h6 class="mb-3">
                            <i class="bi bi-keyboard me-2"></i>Hoặc nhập mã vé thủ công:
                        </h6>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-ticket-perforated"></i>
                            </span>
                            <input type="text" id="manual-code" class="form-control"
                                   placeholder="Nhập mã vé (VD: VE001234)..."
                                   autocomplete="off">
                            <button class="btn btn-primary" onclick="checkManualCode()">
                                <i class="bi bi-search me-1"></i>Kiểm tra
                            </button>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <i class="bi bi-lightbulb me-1"></i>
                            Nhấn Enter hoặc click "Kiểm tra" để xác minh mã vé
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Info -->
        <div class="col-xl-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-ticket-detailed me-1"></i>Thông tin vé</h5>
                </div>
                <div class="card-body">
                    <div id="ticket-info" class="text-center text-muted">
                        <i class="bi bi-qr-code-scan" style="font-size: 4rem; opacity: 0.3;"></i>
                        <p class="mt-3">Quét mã QR để xem thông tin vé</p>
                    </div>
                    
                    <div id="ticket-details" style="display: none;">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="alert alert-info" id="ticket-status"></div>
                            </div>

                            <!-- Thông tin cơ bản -->
                            <div class="col-12">
                                <div class="ticket-info-section">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-person-circle me-2"></i>Thông tin hành khách
                                    </h6>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📋 Mã vé:</label>
                                <p id="ticket-code" class="mb-2 text-dark fs-5 fw-semibold"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">👤 Tên khách:</label>
                                <p id="customer-name" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📞 Số điện thoại:</label>
                                <p id="customer-phone" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📧 Email:</label>
                                <p id="customer-email" class="mb-2 text-dark fs-6"></p>
                            </div>
                                </div>
                            </div>

                            <!-- Thông tin chuyến xe -->
                            <div class="col-12">
                                <div class="ticket-info-section">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-bus-front me-2"></i>Thông tin chuyến xe
                                    </h6>
                            <div class="col-12">
                                <label class="form-label fw-bold text-dark">🚌 Tuyến đường:</label>
                                <p id="route-info" class="mb-2 text-dark fs-6 fw-semibold"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📅 Ngày khởi hành:</label>
                                <p id="departure-date" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">🚐 Biển số xe:</label>
                                <p id="vehicle-number" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">💺 Số ghế:</label>
                                <p id="seat-number" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📊 Trạng thái:</label>
                                <div class="status-badge-container">
                                    <span id="ticket-state-badge" class="badge fs-6"></span>
                                </div>
                            </div>
                                </div>
                            </div>

                            <!-- Thông tin thanh toán -->
                            <div class="col-12">
                                <div class="ticket-info-section">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-credit-card me-2"></i>Thông tin thanh toán
                                    </h6>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">💰 Giá vé:</label>
                                <p id="ticket-price" class="mb-2 text-success fs-5 fw-bold"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold text-dark">📆 Ngày đặt:</label>
                                <p id="booking-date" class="mb-2 text-dark fs-6"></p>
                            </div>
                            <div class="col-12" id="promo-info" style="display: none;">
                                <label class="form-label fw-bold text-dark">🏷️ Khuyến mãi:</label>
                                <p id="promo-details" class="mb-2 text-success fs-6"></p>
                            </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4" id="action-buttons" style="display: none;">
                            <div class="mb-3">
                                <label for="note-input" class="form-label">Ghi chú (tùy chọn):</label>
                                <textarea id="note-input" class="form-control" rows="2" placeholder="Nhập ghi chú..."></textarea>
                            </div>
                            <button class="btn btn-success w-100" onclick="markTicketUsed()">
                                <i class="bi bi-check-circle me-1"></i>Đánh dấu đã sử dụng
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Scans -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-1"></i>Lịch sử quét gần đây</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>Mã vé</th>
                                    <th>Tên khách</th>
                                    <th>Trạng thái</th>
                                    <th>Kết quả</th>
                                </tr>
                            </thead>
                            <tbody id="scan-history">
                                <tr>
                                    <td colspan="5" class="text-center text-muted">Chưa có lịch sử quét</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Cải thiện contrast và styling cho thông tin vé */
        .ticket-info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }

        .ticket-info-section h6 {
            color: #007bff !important;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .form-label {
            color: #212529 !important;
            font-weight: 600 !important;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .ticket-info-section p {
            color: #212529 !important;
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            margin-bottom: 10px;
            min-height: 38px;
            display: flex;
            align-items: center;
        }

        .ticket-info-section .text-success {
            color: #198754 !important;
            font-weight: 700;
        }

        .status-badge-container {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
        }

        .promo-highlight {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745 !important;
        }

        .qr-scanner-container {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }

        .manual-input-section {
            background: #fff3cd;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #ffc107;
        }

        /* Responsive improvements */
        @@media (max-width: 768px) {
            .ticket-info-section {
                padding: 10px;
            }

            .ticket-info-section p {
                padding: 6px 10px;
                font-size: 0.9rem;
            }
        }
    </style>
}

@section Scripts {
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <script>
        let html5QrcodeScanner;
        let currentQrData = '';
        let scanHistory = [];

        // Initialize QR scanner
        function initQRScanner() {
            // Không cần khởi tạo trước, sẽ tạo khi bắt đầu quét
            console.log('QR Scanner initialized');
        }

        // Start scanning
        function startScanning() {
            try {
                console.log('Starting QR scanner...');

                // Clear any existing scanner first
                if (html5QrcodeScanner) {
                    console.log('Clearing existing scanner...');
                    html5QrcodeScanner.clear().catch(err => console.log('Clear error:', err));
                }

                // Tạo scanner mới với config tối ưu cho nhiều loại QR
                const config = {
                    fps: 10,
                    qrbox: function(viewfinderWidth, viewfinderHeight) {
                        // Tạo qrbox linh hoạt theo kích thước viewfinder
                        let minEdgePercentage = 0.7; // 70% của viewfinder
                        let minEdgeSize = Math.min(viewfinderWidth, viewfinderHeight);
                        let qrboxSize = Math.floor(minEdgeSize * minEdgePercentage);
                        return {
                            width: qrboxSize,
                            height: qrboxSize
                        };
                    },
                    aspectRatio: 1.777778, // 16:9 aspect ratio
                    rememberLastUsedCamera: true,
                    showTorchButtonIfSupported: true,
                    // Cải thiện khả năng đọc QR
                    experimentalFeatures: {
                        useBarCodeDetectorIfSupported: true
                    },
                    // Tăng độ nhạy
                    videoConstraints: {
                        facingMode: "environment" // Camera sau
                    }
                };

                // Chỉ thêm supportedScanTypes nếu có sẵn
                if (typeof Html5QrcodeScanType !== 'undefined') {
                    config.supportedScanTypes = [Html5QrcodeScanType.SCAN_TYPE_CAMERA];
                }

                console.log('🔧 Scanner config:', config);

                html5QrcodeScanner = new Html5QrcodeScanner(
                    "qr-reader",
                    config,
                    /* verbose= */ true
                );

                console.log('Rendering scanner...');
                html5QrcodeScanner.render(onScanSuccess, onScanFailure);

                document.getElementById('start-scan').disabled = true;
                document.getElementById('stop-scan').disabled = false;
                document.getElementById('switch-camera').disabled = false;

                showStatus('Đang khởi động camera...', 'info');
                console.log('QR Scanner render called');
            } catch (error) {
                console.error('Error starting QR scanner:', error);
                showStatus('Lỗi khởi động camera: ' + error.message, 'error');

                // Reset button states on error
                document.getElementById('start-scan').disabled = false;
                document.getElementById('stop-scan').disabled = true;
                document.getElementById('switch-camera').disabled = true;
            }
        }

        // Stop scanning
        function stopScanning() {
            try {
                if (html5QrcodeScanner) {
                    html5QrcodeScanner.clear().then(() => {
                        console.log('QR Scanner stopped');
                        showStatus('Đã dừng quét', 'info');
                    }).catch(err => {
                        console.error('Error stopping scanner:', err);
                    });
                }

                document.getElementById('start-scan').disabled = false;
                document.getElementById('stop-scan').disabled = true;
                document.getElementById('switch-camera').disabled = true;
            } catch (error) {
                console.error('Error stopping QR scanner:', error);
            }
        }

        // QR scan success callback
        function onScanSuccess(decodedText, decodedResult) {
            console.log('🎯 QR Code detected successfully!');
            console.log('📄 Decoded text length:', decodedText.length);
            console.log('📄 Decoded text preview:', decodedText.substring(0, 100) + (decodedText.length > 100 ? '...' : ''));
            console.log('📊 Decoded result:', decodedResult);

            // Log QR code characteristics
            if (decodedText.includes('🎫')) {
                console.log('✅ Detected ticket QR format (with emoji)');
            } else if (decodedText.includes('TICKET:')) {
                console.log('✅ Detected legacy ticket QR format');
            } else if (decodedText.startsWith('{')) {
                console.log('✅ Detected JSON QR format');
            } else {
                console.log('ℹ️ Detected other QR format');
            }

            // Dừng quét ngay khi tìm thấy QR code
            console.log('⏹️ Stopping scanner...');
            stopScanning();

            currentQrData = decodedText;
            showStatus('✅ Đã quét được mã QR, đang kiểm tra...', 'success');

            // Verify QR code
            console.log('🔍 Verifying QR code...');
            verifyQRCode(decodedText);
            addToScanHistory(decodedText, 'Đang kiểm tra...');
        }

        // QR scan failure callback
        function onScanFailure(error) {
            // Log occasional errors for debugging
            if (Math.random() < 0.01) { // Log 1% of errors to avoid spam
                console.log('⚠️ QR scan error (sample):', error);
            }
        }

        // Verify QR code
        function verifyQRCode(qrData) {
            console.log('Verifying QR data:', qrData);

            fetch(`@Url.Action("VerifyQRCode", "Booking")?qrData=${encodeURIComponent(qrData)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Verification response:', data);
                    if (data.success) {
                        displayTicketInfo(data.ticketInfo, true);
                        updateScanHistory(qrData, 'Thành công');
                        showStatus('Vé hợp lệ!', 'success');
                    } else {
                        // Hiển thị thông tin vé nếu có, ngay cả khi có lỗi
                        if (data.ticketInfo) {
                            displayTicketInfo(data.ticketInfo, false);
                            updateScanHistory(qrData, 'Có vấn đề: ' + data.message);
                        } else {
                            displayError(data.message);
                            updateScanHistory(qrData, 'Thất bại: ' + data.message);
                        }
                        showStatus(data.message, 'warning');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    displayError('Có lỗi xảy ra khi kiểm tra mã QR');
                    updateScanHistory(qrData, 'Lỗi hệ thống');
                    showStatus('Lỗi hệ thống', 'error');
                });
        }

        // Display ticket information
        function displayTicketInfo(ticketInfo, canMarkUsed = false) {
            console.log('Displaying ticket info:', ticketInfo);

            document.getElementById('ticket-info').style.display = 'none';
            document.getElementById('ticket-details').style.display = 'block';

            // Thông tin cơ bản
            document.getElementById('ticket-code').textContent = ticketInfo.maVe || '-';
            document.getElementById('customer-name').textContent = ticketInfo.tenKhach || '-';
            document.getElementById('customer-phone').textContent = ticketInfo.soDienThoai || '-';
            document.getElementById('customer-email').textContent = ticketInfo.email || 'Chưa có';

            // Thông tin chuyến xe
            document.getElementById('route-info').textContent = ticketInfo.tuyenDuong || '-';
            document.getElementById('departure-date').textContent = ticketInfo.ngayKhoiHanh || '-';
            document.getElementById('vehicle-number').textContent = ticketInfo.bienSoXe || 'Chưa xác định';
            document.getElementById('seat-number').textContent = ticketInfo.soGhe || 'Chưa chọn';

            // Thông tin thanh toán - Format số tiền đúng
            const giaVe = ticketInfo.giaVe || '0';
            document.getElementById('ticket-price').textContent = giaVe + ' VNĐ';
            document.getElementById('booking-date').textContent = ticketInfo.ngayDat || '-';

            // Trạng thái vé với màu sắc
            const stateBadge = document.getElementById('ticket-state-badge');
            const trangThai = ticketInfo.trangThai || 'Không xác định';
            console.log('Ticket status:', trangThai);
            stateBadge.textContent = trangThai;

            // Styling cho trạng thái
            stateBadge.className = 'badge fs-6 ' + getStatusBadgeClass(trangThai);

            // Thông tin khuyến mãi (nếu có)
            if (ticketInfo.maKhuyenMai || (ticketInfo.giamGia && ticketInfo.giamGia > 0)) {
                const promoInfo = document.getElementById('promo-info');
                const promoDetails = document.getElementById('promo-details');
                promoInfo.style.display = 'block';

                // Thêm highlight cho section có khuyến mãi
                const promoSection = promoInfo.closest('.ticket-info-section');
                if (promoSection) {
                    promoSection.classList.add('promo-highlight');
                }

                let promoText = '';
                if (ticketInfo.tenKhuyenMai) {
                    promoText += `${ticketInfo.tenKhuyenMai}`;
                } else if (ticketInfo.maKhuyenMai) {
                    promoText += `Mã: ${ticketInfo.maKhuyenMai}`;
                }

                if (ticketInfo.giamGia && ticketInfo.giamGia > 0) {
                    if (promoText) promoText += ' - ';
                    promoText += `Giảm: ${parseInt(ticketInfo.giamGia).toLocaleString('vi-VN')} VNĐ`;
                }

                promoDetails.textContent = promoText || 'Có khuyến mãi';
            } else {
                document.getElementById('promo-info').style.display = 'none';
                // Remove highlight nếu không có khuyến mãi
                document.querySelectorAll('.promo-highlight').forEach(el => {
                    el.classList.remove('promo-highlight');
                });
            }

            // Status message
            const statusElement = document.getElementById('ticket-status');
            if (canMarkUsed) {
                statusElement.textContent = 'Vé hợp lệ - Có thể sử dụng';
                statusElement.className = 'alert alert-success';
            } else {
                statusElement.textContent = getStatusMessage(trangThai);
                statusElement.className = 'alert ' + getStatusAlertClass(trangThai);
            }

            // Action buttons - Chỉ hiển thị cho vé có thể đánh dấu sử dụng
            const actionButtons = document.getElementById('action-buttons');
            console.log('Can mark used:', canMarkUsed, 'Status:', trangThai);

            // Chỉ cho phép đánh dấu sử dụng với vé đã thanh toán
            if (canMarkUsed && trangThai === 'Đã thanh toán') {
                actionButtons.style.display = 'block';
            } else {
                actionButtons.style.display = 'none';
            }
        }

        // Get status badge class - Mapping đúng với TrangThaiVe enum
        function getStatusBadgeClass(trangThai) {
            switch (trangThai) {
                case 'Đã đặt': return 'bg-warning text-dark';
                case 'Đã thanh toán': return 'bg-success text-white';
                case 'Đã sử dụng': return 'bg-info text-white';
                case 'Đã hoàn thành': return 'bg-primary text-white';
                case 'Đã hủy': return 'bg-danger text-white';
                case 'Đã hoàn tiền': return 'bg-secondary text-white';
                case 'Chưa thanh toán': return 'bg-warning text-dark';
                default: return 'bg-light text-dark';
            }
        }

        // Get status message - Mapping đúng với logic nghiệp vụ
        function getStatusMessage(trangThai) {
            switch (trangThai) {
                case 'Đã đặt': return 'Vé đã đặt - Chờ thanh toán';
                case 'Đã thanh toán': return 'Vé hợp lệ - Có thể sử dụng';
                case 'Đã sử dụng': return 'Vé đã được sử dụng - Hành khách đã lên xe';
                case 'Đã hoàn thành': return 'Chuyến đi đã hoàn thành';
                case 'Đã hủy': return 'Vé đã bị hủy - Không thể sử dụng';
                case 'Đã hoàn tiền': return 'Vé đã được hoàn tiền';
                case 'Chưa thanh toán': return 'Vé chưa được thanh toán - Cần thanh toán để sử dụng';
                default: return 'Trạng thái không xác định';
            }
        }

        // Get status alert class - Mapping đúng với Bootstrap classes
        function getStatusAlertClass(trangThai) {
            switch (trangThai) {
                case 'Đã đặt': return 'alert-warning';
                case 'Đã thanh toán': return 'alert-success';
                case 'Đã sử dụng': return 'alert-info';
                case 'Đã hoàn thành': return 'alert-primary';
                case 'Đã hủy': return 'alert-danger';
                case 'Đã hoàn tiền': return 'alert-secondary';
                case 'Chưa thanh toán': return 'alert-warning';
                default: return 'alert-light';
            }
        }

        // Display error
        function displayError(message) {
            document.getElementById('ticket-info').style.display = 'none';
            document.getElementById('ticket-details').style.display = 'block';
            document.getElementById('action-buttons').style.display = 'none';
            document.getElementById('promo-info').style.display = 'none';

            const statusElement = document.getElementById('ticket-status');
            statusElement.textContent = message;
            statusElement.className = 'alert alert-danger';

            // Clear other fields
            ['ticket-code', 'customer-name', 'customer-phone', 'customer-email', 'seat-number',
             'route-info', 'departure-date', 'vehicle-number', 'ticket-price', 'booking-date'].forEach(id => {
                document.getElementById(id).textContent = '-';
            });

            // Clear status badge
            const stateBadge = document.getElementById('ticket-state-badge');
            stateBadge.textContent = '-';
            stateBadge.className = 'badge bg-light text-dark fs-6';
        }

        // Mark ticket as used
        function markTicketUsed() {
            const note = document.getElementById('note-input').value;
            
            fetch('@Url.Action("MarkTicketUsed", "Booking")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    QrData: currentQrData,
                    GhiChu: note
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    document.getElementById('action-buttons').style.display = 'none';
                    document.getElementById('ticket-state').textContent = 'Đã sử dụng';
                    updateScanHistory(currentQrData, 'Đã đánh dấu sử dụng');
                } else {
                    showToast(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Có lỗi xảy ra khi cập nhật trạng thái vé', 'danger');
            });
        }

        // Check manual code
        function checkManualCode() {
            const code = document.getElementById('manual-code').value.trim();
            if (!code) {
                showToast('Vui lòng nhập mã vé', 'warning');
                return;
            }

            // Gửi mã vé trực tiếp (controller sẽ xử lý)
            currentQrData = code;
            verifyQRCode(code);
            addToScanHistory(code, 'Đang kiểm tra...');
        }

        // Scan history management
        function addToScanHistory(code, status) {
            const now = new Date();
            scanHistory.unshift({
                time: now.toLocaleTimeString('vi-VN'),
                code: code.includes('TICKET:') ? code.split('|')[0].replace('TICKET:', '') : code,
                customer: '',
                status: status,
                result: status
            });
            updateScanHistoryTable();
        }

        function updateScanHistory(code, result) {
            if (scanHistory.length > 0) {
                scanHistory[0].result = result;
                updateScanHistoryTable();
            }
        }

        function updateScanHistoryTable() {
            const tbody = document.getElementById('scan-history');
            if (scanHistory.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Chưa có lịch sử quét</td></tr>';
                return;
            }

            tbody.innerHTML = scanHistory.slice(0, 10).map(item => `
                <tr>
                    <td>${item.time}</td>
                    <td>${item.code}</td>
                    <td>${item.customer || '-'}</td>
                    <td>${item.status}</td>
                    <td>
                        <span class="badge ${item.result.includes('Thành công') || item.result.includes('sử dụng') ? 'bg-success' : 
                                           item.result.includes('Thất bại') ? 'bg-danger' : 'bg-warning'}">
                            ${item.result}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        // Show status message
        function showStatus(message, type) {
            showToast(message, type);
        }

        // Toast notification
        function showToast(message, type) {
            // Simple toast implementation
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        // Switch camera function
        function switchCamera() {
            if (html5QrcodeScanner) {
                stopScanning();
                setTimeout(() => {
                    startScanning();
                }, 500);
            }
        }

        // Alternative scanning with different config
        function startAlternativeScanning() {
            try {
                console.log('🔄 Starting alternative QR scanner...');

                // Clear any existing scanner first
                if (html5QrcodeScanner) {
                    console.log('Clearing existing scanner...');
                    html5QrcodeScanner.clear().catch(err => console.log('Clear error:', err));
                }

                // Alternative config - more sensitive for complex QR codes
                const altConfig = {
                    fps: 5, // Slower FPS for better accuracy
                    qrbox: { width: 300, height: 300 }, // Larger scan area
                    aspectRatio: 1.0,
                    rememberLastUsedCamera: true,
                    showTorchButtonIfSupported: true,
                    // More aggressive scanning
                    disableFlip: false,
                    videoConstraints: {
                        facingMode: "environment",
                        advanced: [{
                            focusMode: "continuous",
                            zoom: 1.0
                        }]
                    }
                };

                console.log('🔧 Alternative config:', altConfig);

                html5QrcodeScanner = new Html5QrcodeScanner(
                    "qr-reader",
                    altConfig,
                    /* verbose= */ true
                );

                console.log('Rendering alternative scanner...');
                html5QrcodeScanner.render(onScanSuccess, onScanFailure);

                document.getElementById('start-scan').disabled = true;
                document.getElementById('stop-scan').disabled = false;
                document.getElementById('switch-camera').disabled = false;

                showStatus('🔄 Alternative scanner đang khởi động...', 'info');
                console.log('Alternative QR Scanner render called');
            } catch (error) {
                console.error('Error starting alternative QR scanner:', error);
                showStatus('Lỗi khởi động alternative scanner: ' + error.message, 'error');

                // Reset button states on error
                document.getElementById('start-scan').disabled = false;
                document.getElementById('stop-scan').disabled = true;
                document.getElementById('switch-camera').disabled = true;
            }
        }

        // Test QR Code function
        function testQRCode() {
            console.log('🧪 Testing QR Code with multiple sample formats...');

            // Test với nhiều format khác nhau
            const testFormats = [
                {
                    name: 'Simple ticket code',
                    data: 'VE001234'
                },
                {
                    name: 'Legacy format',
                    data: 'TICKET:VE001234|PASSENGER:Nguyen Van A|PHONE:0123456789|TRIP:HCM-HN|DATE:2024-01-15 08:00|SEAT:A1'
                },
                {
                    name: 'JSON format',
                    data: '{"ticketCode":"VE001234","passengerName":"Nguyen Van A","phone":"0123456789","route":"HCM-HN"}'
                },
                {
                    name: 'Current emoji format',
                    data: `🎫 VÉ XE KHÁCH
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Mã vé: VE001234
👤 Hành khách: Nguyễn Văn A
📞 Điện thoại: 0123456789
🚌 Tuyến: TP.HCM → Hà Nội
📅 Ngày đi: 15/01/2024 08:00
💺 Ghế số: A12
💰 Giá vé: 115,000 VNĐ
📊 Trạng thái: Đã thanh toán
📆 Ngày đặt: 14/01/2024 10:30
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Vui lòng xuất trình vé này khi lên xe`
                }
            ];

            // Cho user chọn format để test
            const formatNames = testFormats.map((f, i) => `${i + 1}. ${f.name}`).join('\n');
            const choice = prompt(`Chọn format để test:\n${formatNames}\n\nNhập số (1-${testFormats.length}):`);

            const index = parseInt(choice) - 1;
            if (index >= 0 && index < testFormats.length) {
                const selectedFormat = testFormats[index];
                console.log(`📄 Testing with ${selectedFormat.name}:`, selectedFormat.data);

                // Simulate QR scan success
                onScanSuccess(selectedFormat.data, { text: selectedFormat.data });
            } else {
                console.log('❌ Invalid choice, using default format');
                onScanSuccess(testFormats[3].data, { text: testFormats[3].data });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, initializing QR Scanner...');
            initQRScanner();

            // Event listeners
            document.getElementById('start-scan').addEventListener('click', startScanning);
            document.getElementById('stop-scan').addEventListener('click', stopScanning);
            document.getElementById('switch-camera').addEventListener('click', switchCamera);

            // Manual input enter key
            document.getElementById('manual-code').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkManualCode();
                }
            });

            // Check camera permissions
            checkCameraPermissions();
        });

        // Check camera permissions
        function checkCameraPermissions() {
            console.log('🔍 Checking camera permissions...');

            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                console.log('📱 getUserMedia is supported');

                navigator.mediaDevices.getUserMedia({ video: true })
                    .then(function(stream) {
                        console.log('✅ Camera permission granted');
                        console.log('📹 Stream info:', stream);

                        // Stop the stream immediately
                        stream.getTracks().forEach(track => {
                            console.log('⏹️ Stopping track:', track.label);
                            track.stop();
                        });

                        showStatus('📷 Camera sẵn sàng. Click "Bắt đầu quét" để bắt đầu.', 'success');

                        // Test Html5QrcodeScanner availability
                        testQRScannerLibrary();
                    })
                    .catch(function(error) {
                        console.error('❌ Camera permission denied:', error);
                        showStatus('⚠️ Không thể truy cập camera. Vui lòng cho phép truy cập camera và tải lại trang.', 'warning');
                        document.getElementById('start-scan').disabled = true;
                    });
            } else {
                console.error('❌ getUserMedia not supported');
                showStatus('⚠️ Trình duyệt không hỗ trợ camera. Vui lòng sử dụng nhập thủ công.', 'warning');
                document.getElementById('start-scan').disabled = true;
            }
        }

        // Test QR Scanner library
        function testQRScannerLibrary() {
            console.log('🧪 Testing Html5QrcodeScanner library...');

            if (typeof Html5QrcodeScanner !== 'undefined') {
                console.log('✅ Html5QrcodeScanner is available');
                console.log('📚 Html5QrcodeScanner:', Html5QrcodeScanner);

                if (typeof Html5QrcodeScanType !== 'undefined') {
                    console.log('✅ Html5QrcodeScanType is available');
                } else {
                    console.warn('⚠️ Html5QrcodeScanType is not available');
                }
            } else {
                console.error('❌ Html5QrcodeScanner is not available');
                showStatus('❌ QR Scanner library chưa được tải. Vui lòng tải lại trang.', 'danger');
                document.getElementById('start-scan').disabled = true;
            }
        }
    </script>
}
