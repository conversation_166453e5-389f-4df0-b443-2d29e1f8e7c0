@{
    ViewData["Title"] = "Quét QR Code Vé";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-4">
                <h2 class="fw-bold text-primary">
                    <i class="bi bi-qr-code-scan me-2"></i>Quét QR Code Vé
                </h2>
                <p class="text-muted">Quét mã QR trên vé để kiểm tra thông tin và check-in</p>
            </div>

            <!-- Scanner Card -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-camera me-2"></i>Camera Scanner
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- Camera Preview -->
                    <div class="scanner-container mb-3">
                        <div id="scanner" class="scanner-preview">
                            <video id="preview" class="w-100 rounded"></video>
                            <div class="scanner-overlay">
                                <div class="scanner-frame"></div>
                            </div>
                        </div>
                        
                        <!-- Scanner Controls -->
                        <div class="scanner-controls mt-3 text-center">
                            <button id="startBtn" class="btn btn-success me-2">
                                <i class="bi bi-play-fill me-1"></i>Bắt đầu quét
                            </button>
                            <button id="stopBtn" class="btn btn-danger me-2" style="display: none;">
                                <i class="bi bi-stop-fill me-1"></i>Dừng quét
                            </button>
                            <button id="switchCameraBtn" class="btn btn-outline-primary" style="display: none;">
                                <i class="bi bi-camera-reels me-1"></i>Đổi camera
                            </button>
                        </div>

                        <!-- Manual Input -->
                        <div class="manual-input mt-4">
                            <h6 class="fw-bold">Hoặc nhập thủ công:</h6>
                            <div class="input-group">
                                <input type="text" id="manualInput" class="form-control" 
                                       placeholder="Nhập dữ liệu QR code hoặc mã vé...">
                                <button class="btn btn-outline-primary" type="button" id="validateManualBtn">
                                    <i class="bi bi-check-circle me-1"></i>Kiểm tra
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Status Messages -->
                    <div id="statusMessages"></div>
                </div>
            </div>

            <!-- Ticket Information Card -->
            <div id="ticketInfoCard" class="card shadow-sm border-0" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-ticket-perforated me-2"></i>Thông tin vé
                    </h6>
                </div>
                <div class="card-body" id="ticketInfoContent">
                    <!-- Ticket info will be populated here -->
                </div>
            </div>

            <!-- Instructions -->
            <div class="card shadow-sm border-0 mt-4">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Hướng dẫn sử dụng
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Nhấn "Bắt đầu quét" để mở camera
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Đưa mã QR vào khung hình vuông để quét
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Đảm bảo ánh sáng đủ và mã QR rõ nét
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Có thể nhập thủ công nếu không quét được
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include QR Scanner Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

<style>
    .scanner-container {
        position: relative;
        max-width: 500px;
        margin: 0 auto;
    }

    .scanner-preview {
        position: relative;
        background: #000;
        border-radius: 10px;
        overflow: hidden;
    }

    #preview {
        display: block;
        width: 100%;
        height: auto;
        min-height: 300px;
        object-fit: cover;
    }

    .scanner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
    }

    .scanner-frame {
        width: 200px;
        height: 200px;
        border: 3px solid #28a745;
        border-radius: 10px;
        position: relative;
        animation: pulse 2s infinite;
    }

    .scanner-frame::before,
    .scanner-frame::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        border: 3px solid #28a745;
    }

    .scanner-frame::before {
        top: -3px;
        left: -3px;
        border-right: none;
        border-bottom: none;
    }

    .scanner-frame::after {
        bottom: -3px;
        right: -3px;
        border-left: none;
        border-top: none;
    }

    @@keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    .ticket-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .ticket-info-item:last-child {
        border-bottom: none;
    }

    .ticket-info-label {
        font-weight: 500;
        color: #6c757d;
    }

    .ticket-info-value {
        font-weight: 600;
        color: #495057;
    }

    .status-success {
        color: #28a745;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
    }

    .status-error {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
    }

    .status-warning {
        color: #856404;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
    }
</style>

<script>
    let html5QrcodeScanner = null;
    let currentCameraId = null;
    let cameras = [];

    document.addEventListener('DOMContentLoaded', function() {
        initializeScanner();
        setupEventListeners();
    });

    function initializeScanner() {
        // Get available cameras
        Html5Qrcode.getCameras().then(devices => {
            cameras = devices;
            if (devices && devices.length) {
                currentCameraId = devices[0].id;
                document.getElementById('switchCameraBtn').style.display = devices.length > 1 ? 'inline-block' : 'none';
            }
        }).catch(err => {
            console.error('Error getting cameras:', err);
            showStatus('Không thể truy cập camera. Vui lòng sử dụng nhập thủ công.', 'error');
        });
    }

    function setupEventListeners() {
        document.getElementById('startBtn').addEventListener('click', startScanning);
        document.getElementById('stopBtn').addEventListener('click', stopScanning);
        document.getElementById('switchCameraBtn').addEventListener('click', switchCamera);
        document.getElementById('validateManualBtn').addEventListener('click', validateManualInput);
        
        // Enter key for manual input
        document.getElementById('manualInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                validateManualInput();
            }
        });
    }

    function startScanning() {
        if (!currentCameraId) {
            showStatus('Không tìm thấy camera khả dụng', 'error');
            return;
        }

        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0
        };

        html5QrcodeScanner = new Html5Qrcode("scanner");
        
        html5QrcodeScanner.start(
            currentCameraId,
            config,
            onScanSuccess,
            onScanFailure
        ).then(() => {
            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('stopBtn').style.display = 'inline-block';
            document.getElementById('switchCameraBtn').style.display = cameras.length > 1 ? 'inline-block' : 'none';
            showStatus('Đang quét... Đưa mã QR vào khung hình', 'info');
        }).catch(err => {
            console.error('Error starting scanner:', err);
            showStatus('Không thể khởi động camera: ' + err, 'error');
        });
    }

    function stopScanning() {
        if (html5QrcodeScanner) {
            html5QrcodeScanner.stop().then(() => {
                document.getElementById('startBtn').style.display = 'inline-block';
                document.getElementById('stopBtn').style.display = 'none';
                document.getElementById('switchCameraBtn').style.display = 'none';
                showStatus('Đã dừng quét', 'info');
            }).catch(err => {
                console.error('Error stopping scanner:', err);
            });
        }
    }

    function switchCamera() {
        if (cameras.length <= 1) return;
        
        const currentIndex = cameras.findIndex(camera => camera.id === currentCameraId);
        const nextIndex = (currentIndex + 1) % cameras.length;
        currentCameraId = cameras[nextIndex].id;
        
        if (html5QrcodeScanner) {
            stopScanning();
            setTimeout(startScanning, 500);
        }
    }

    function onScanSuccess(decodedText, decodedResult) {
        console.log('QR Code scanned:', decodedText);
        stopScanning();
        validateQRCode(decodedText);
    }

    function onScanFailure(error) {
        // Ignore scan failures - they happen frequently during scanning
    }

    function validateManualInput() {
        const input = document.getElementById('manualInput').value.trim();
        if (!input) {
            showStatus('Vui lòng nhập dữ liệu QR code hoặc mã vé', 'error');
            return;
        }
        validateQRCode(input);
    }

    function validateQRCode(qrData) {
        showStatus('Đang kiểm tra...', 'info');
        
        fetch('/QR/Validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ qrData: qrData })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus(data.message, 'success');
                displayTicketInfo(data.ticketInfo, data.canCheckIn);
            } else {
                showStatus(data.message, 'error');
                if (data.ticketInfo) {
                    displayTicketInfo(data.ticketInfo, false);
                }
            }
        })
        .catch(error => {
            console.error('Error validating QR code:', error);
            showStatus('Lỗi khi kiểm tra QR code', 'error');
        });
    }

    function displayTicketInfo(ticketInfo, canCheckIn) {
        const card = document.getElementById('ticketInfoCard');
        const content = document.getElementById('ticketInfoContent');
        
        let html = `
            <div class="ticket-info-item">
                <span class="ticket-info-label">Mã vé:</span>
                <span class="ticket-info-value">${ticketInfo.ticketCode}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Hành khách:</span>
                <span class="ticket-info-value">${ticketInfo.passengerName}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Số điện thoại:</span>
                <span class="ticket-info-value">${ticketInfo.phone}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Ghế:</span>
                <span class="ticket-info-value">${ticketInfo.seatNumber}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Tuyến:</span>
                <span class="ticket-info-value">${ticketInfo.route}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Khởi hành:</span>
                <span class="ticket-info-value">${new Date(ticketInfo.departureTime).toLocaleString('vi-VN')}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Giá vé:</span>
                <span class="ticket-info-value">${ticketInfo.amount.toLocaleString('vi-VN')} VNĐ</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Trạng thái:</span>
                <span class="ticket-info-value">${ticketInfo.status}</span>
            </div>
            <div class="ticket-info-item">
                <span class="ticket-info-label">Thanh toán:</span>
                <span class="ticket-info-value">${ticketInfo.paymentStatus}</span>
            </div>
        `;
        
        if (canCheckIn) {
            html += `
                <div class="mt-3 text-center">
                    <button class="btn btn-success btn-lg" onclick="checkInPassenger('${ticketInfo.ticketCode}')">
                        <i class="bi bi-check-circle me-2"></i>Check-in
                    </button>
                </div>
            `;
        }
        
        content.innerHTML = html;
        card.style.display = 'block';
    }

    function checkInPassenger(ticketCode) {
        if (!confirm('Xác nhận check-in cho hành khách này?')) {
            return;
        }
        
        fetch('/QR/CheckIn', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ticketCode: ticketCode })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus(data.message, 'success');
                // Refresh ticket info to show updated status
                setTimeout(() => {
                    validateQRCode(document.getElementById('manualInput').value || ticketCode);
                }, 1000);
            } else {
                showStatus(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error during check-in:', error);
            showStatus('Lỗi khi check-in', 'error');
        });
    }

    function showStatus(message, type) {
        const container = document.getElementById('statusMessages');
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const html = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        container.innerHTML = html;
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
</script>
