@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Chi tiết chuyến xe";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bus"></i> Thông tin chuyến xe
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3 text-primary">
                                <i class="fas fa-route"></i> Thông tin tuyến đường
                            </h6>
                            <dl class="row">
                                <dt class="col-sm-4">Điểm đi:</dt>
                                <dd class="col-sm-8">
                                    <strong class="text-primary">@Model.DiemDiDisplay</strong>
                                </dd>

                                <dt class="col-sm-4">Điểm đến:</dt>
                                <dd class="col-sm-8">
                                    <strong class="text-success">@Model.DiemDenDisplay</strong>
                                </dd>

                                <dt class="col-sm-4">Ngày khởi hành:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.NgayKhoiHanh.ToString("dddd, dd/MM/yyyy")</strong>
                                </dd>

                                <dt class="col-sm-4">Giờ khởi hành:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary fs-6">@Model.NgayKhoiHanh.ToString("HH:mm")</span>
                                </dd>

                                <dt class="col-sm-4">Giá vé:</dt>
                                <dd class="col-sm-8">
                                    <strong class="text-success fs-5">@Model.Gia.ToString("N0") VNĐ</strong>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <h6 class="card-subtitle mb-3 text-info">
                                <i class="fas fa-bus-alt"></i> Thông tin xe & Tình trạng
                            </h6>
                            <dl class="row">
                                <dt class="col-sm-4">Loại xe:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info fs-6">@Model.Xe.LoaiXe</span>
                                </dd>

                                <dt class="col-sm-4">Biển số:</dt>
                                <dd class="col-sm-8">
                                    <code>@Model.Xe.BienSo</code>
                                </dd>

                                <dt class="col-sm-4">Tổng số ghế:</dt>
                                <dd class="col-sm-8">
                                    <strong>@Model.Xe.SoGhe chỗ ngồi</strong>
                                </dd>

                                <dt class="col-sm-4">Tình trạng:</dt>
                                <dd class="col-sm-8">
                                    @{
                                        var soGheTrong = Model.Xe.SoGhe - (Model.Ves != null ? Model.Ves.Count : 0);
                                        var daDi = Model.NgayKhoiHanh <= DateTime.Now;
                                    }
                                    @if (daDi)
                                    {
                                        <span class="badge bg-secondary fs-6">Đã khởi hành</span>
                                    }
                                    else if (soGheTrong > 0)
                                    {
                                        <span class="badge bg-success fs-6">Còn @soGheTrong chỗ trống</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger fs-6">Hết chỗ</span>
                                    }
                                </dd>

                                @if (Model.TuyenDuong != null)
                                {
                                    <dt class="col-sm-4">Khoảng cách:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-road"></i> @Model.TuyenDuong.KhoangCach km
                                    </dd>

                                    <dt class="col-sm-4">Thời gian dự kiến:</dt>
                                    <dd class="col-sm-8">
                                        <i class="fas fa-clock"></i> @Model.TuyenDuong.ThoiGianDuKien.ToString(@"hh\:mm")
                                    </dd>
                                }
                            </dl>
                        </div>
                    </div>

                    <!-- Thông tin đặt vé -->
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">
                                            <i class="fas fa-ticket-alt"></i> Thông tin đặt vé
                                        </h6>
                                        <p class="mb-2">
                                            <strong>Số vé đã đặt:</strong>
                                            <span class="badge bg-info">@(Model.Ves?.Count ?? 0)/@Model.Xe.SoGhe</span>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Tỷ lệ lấp đầy:</strong>
                                            @{
                                                var tyLe = Model.Xe.SoGhe > 0 ? (double)(Model.Ves?.Count ?? 0) / Model.Xe.SoGhe * 100 : 0;
                                            }
                                            <span class="badge @(tyLe >= 80 ? "bg-danger" : tyLe >= 50 ? "bg-warning" : "bg-success")">
                                                @tyLe.ToString("F1")%
                                            </span>
                                        </p>
                                        @if (!daDi && soGheTrong > 0)
                                        {
                                            <div class="mt-3">
                                                <a asp-controller="ChoNgoi" asp-action="ChonGhe" asp-route-id="@Model.ChuyenXeId"
                                                   class="btn btn-success">
                                                    <i class="fas fa-ticket-alt"></i> Đặt vé ngay
                                                </a>
                                            </div>
                                        }
                                        else if (!daDi)
                                        {
                                            <div class="mt-3">
                                                <button class="btn btn-secondary" disabled>
                                                    <i class="fas fa-ban"></i> Hết chỗ
                                                </button>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3 class="card-title">@Model.Gia.ToString("N0")</h3>
                                        <p class="card-text">VNĐ / vé</p>
                                        @if (Model.TuyenDuong != null)
                                        {
                                            <small>~@((Model.Gia / Model.TuyenDuong.KhoangCach).ToString("N0")) VNĐ/km</small>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <div class="btn-group">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại danh sách
                            </a>
                            <a asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
                                <i class="fas fa-home"></i> Trang chủ
                            </a>
                            @if (!daDi && soGheTrong > 0)
                            {
                                <a asp-controller="ChoNgoi" asp-action="ChonGhe" asp-route-id="@Model.ChuyenXeId"
                                   class="btn btn-success">
                                    <i class="fas fa-ticket-alt"></i> Đặt vé chuyến này
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        dt {
            font-weight: 600;
        }
        .badge {
            font-weight: 500;
        }
    </style>
}