@model IEnumerable<DatVeXe.Models.ChuyenXe>
@using Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewData["Title"] = "Quản lý chuyến xe";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-calendar-alt" style="color: #34495e;"></i>
        Quản lý chuyến xe
    </h3>
    <div class="d-flex gap-2">
        <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="ThongKe" class="btn btn-info">
            <i class="fas fa-chart-bar"></i>
            Thống kê
        </a>
        <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Create" class="btn" style="background-color: #34495e; border-color: #34495e; color: white;">
            <i class="fas fa-plus"></i>
            Thêm chuyến xe
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" asp-action="Index">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label" style="color: black;">Tìm kiếm</label>
                    <input type="text" name="searchString" value="@ViewBag.CurrentFilter"
                           class="form-control" placeholder="Điểm đi, điểm đến, biển số xe..." />
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Trạng thái</label>
                    <select name="trangThai" class="form-select">
                        <option value="">Tất cả</option>
                        <option value="ChoDuyet" selected="@(ViewBag.TrangThaiFilter == "ChoDuyet")">Chờ duyệt</option>
                        <option value="DaDuyet" selected="@(ViewBag.TrangThaiFilter == "DaDuyet")">Đã duyệt</option>
                        <option value="TuChoi" selected="@(ViewBag.TrangThaiFilter == "TuChoi")">Từ chối</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Từ ngày</label>
                    <input type="date" name="tuNgay" value="@ViewBag.TuNgayFilter" class="form-control" />
                </div>
                <div class="col-md-2">
                    <label class="form-label" style="color: black;">Đến ngày</label>
                    <input type="date" name="denNgay" value="@ViewBag.DenNgayFilter" class="form-control" />
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i> Đặt lại
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Trips Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Danh sách chuyến xe (@ViewBag.TotalCount)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>ID</th>
                        <th>Xe</th>
                        <th>Tuyến đường</th>
                        <th>Ngày khởi hành</th>
                        <th>Thời gian</th>
                        <th>Giá vé</th>
                        <th>Tài xế</th>
                        <th>Trạng thái duyệt</th>
                        <th>Vé đã bán</th>
                        <th width="200">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var chuyenXe in Model)
                    {
                        <tr id="<EMAIL>">
                            <td>@chuyenXe.ChuyenXeId</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-bus text-primary me-2"></i>
                                    <div>
                                        <strong>@chuyenXe.Xe?.BienSoXe</strong>
                                        <br><small class="text-muted">@chuyenXe.Xe?.LoaiXe (@chuyenXe.Xe?.SoGhe ghế)</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>@chuyenXe.TuyenDuong?.DiemDi → @chuyenXe.TuyenDuong?.DiemDen</strong>
                                    <br><small class="text-muted">@chuyenXe.TuyenDuong?.KhoangCach km</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</strong>
                                    <br><small class="text-muted">@chuyenXe.NgayKhoiHanh.ToString("dddd", new System.Globalization.CultureInfo("vi-VN"))</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">@chuyenXe.ThoiGianDi.ToString(@"hh\:mm")</span>
                            </td>
                            <td>
                                <strong>@chuyenXe.Gia.ToString("N0") VNĐ</strong>
                            </td>
                            <td>
                                @if (chuyenXe.TaiXe != null)
                                {
                                    <div>
                                        <i class="fas fa-user text-success me-1"></i>
                                        @chuyenXe.TaiXe.HoTen
                                        <br><small class="text-muted">@chuyenXe.TaiXe.SoDienThoai</small>
                                    </div>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa phân công</span>
                                }
                            </td>
                            <td>
                                <span class="badge bg-@(chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.ChoDuyet ? "warning" :
                                                        chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.DaDuyet ? "success" : "danger")"
                                      id="<EMAIL>">
                                    <i class="fas fa-@(chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.ChoDuyet ? "clock" :
                                                      chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.DaDuyet ? "check" : "times") me-1"></i>
                                    @(chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.ChoDuyet ? "Chờ duyệt" :
                                      chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.DaDuyet ? "Đã duyệt" : "Từ chối")
                                </span>
                                @if (chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.BiTuChoi && !string.IsNullOrEmpty(chuyenXe.LyDoTuChoi))
                                {
                                    <br><small class="text-danger">@chuyenXe.LyDoTuChoi</small>
                                }
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>@(chuyenXe.Ves?.Count ?? 0)/@chuyenXe.Xe?.SoGhe</strong>
                                    <br><small class="text-muted">vé</small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId"
                                       class="btn btn-sm btn-outline-info" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if (chuyenXe.TrangThaiDuyet != DatVeXe.Models.TrangThaiDuyet.BiTuChoi)
                                    {
                                        <a asp-area="Admin" asp-controller="ChuyenXe" asp-action="Edit" asp-route-id="@chuyenXe.ChuyenXeId"
                                           class="btn btn-sm btn-outline-warning" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    }
                                    @if (chuyenXe.TrangThaiDuyet == DatVeXe.Models.TrangThaiDuyet.ChoDuyet)
                                    {
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="duyetChuyenXe(@chuyenXe.ChuyenXeId, true)" title="Duyệt">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="tuChoiChuyenXe(@chuyenXe.ChuyenXeId)" title="Từ chối">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    }
                                    @if ((chuyenXe.Ves?.Count ?? 0) == 0)
                                    {
                                        <form asp-area="Admin" asp-controller="ChuyenXe" asp-action="Delete" asp-route-id="@chuyenXe.ChuyenXeId"
                                              method="post" style="display:inline;" onsubmit="return confirm('Bạn có chắc chắn muốn xóa chuyến xe này?');">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <nav aria-label="Phân trang chuyến xe" class="mt-4">
        <ul class="pagination justify-content-center">
            @if (ViewBag.CurrentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage - 1, searchString = ViewBag.CurrentFilter, trangThai = ViewBag.TrangThaiFilter, tuNgay = ViewBag.TuNgayFilter, denNgay = ViewBag.DenNgayFilter })">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            }

            @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
            {
                <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Index", new { page = i, searchString = ViewBag.CurrentFilter, trangThai = ViewBag.TrangThaiFilter, tuNgay = ViewBag.TuNgayFilter, denNgay = ViewBag.DenNgayFilter })">
                        @i
                    </a>
                </li>
            }

            @if (ViewBag.CurrentPage < ViewBag.TotalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Index", new { page = ViewBag.CurrentPage + 1, searchString = ViewBag.CurrentFilter, trangThai = ViewBag.TrangThaiFilter, tuNgay = ViewBag.TuNgayFilter, denNgay = ViewBag.DenNgayFilter })">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">Từ chối chuyến xe</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">Lý do từ chối</label>
                        <textarea class="form-control" id="rejectReason" rows="3" placeholder="Nhập lý do từ chối chuyến xe..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-danger" id="confirmReject">Từ chối</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentTripId = null;

        function duyetChuyenXe(tripId, approve) {
            if (confirm('Bạn có chắc chắn muốn duyệt chuyến xe này?')) {
                $.post('@Url.Action("DuyetChuyenXe")', {
                    chuyenXeId: tripId,
                    duyet: approve
                })
                .done(function(data) {
                    if (data.success) {
                        // Update badge
                        const badge = $('#status-badge-' + tripId);
                        badge.removeClass('bg-warning').addClass('bg-success');
                        badge.html('<i class="fas fa-check me-1"></i>Đã duyệt');

                        // Hide approve/reject buttons
                        const row = $('#trip-row-' + tripId);
                        row.find('.btn-outline-success, .btn-outline-danger').remove();

                        showToast(data.message, 'success');
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('Có lỗi xảy ra khi duyệt chuyến xe', 'error');
                });
            }
        }

        function tuChoiChuyenXe(tripId) {
            currentTripId = tripId;
            $('#rejectModal').modal('show');
        }

        $('#confirmReject').click(function() {
            const reason = $('#rejectReason').val();

            if (!reason.trim()) {
                showToast('Vui lòng nhập lý do từ chối', 'error');
                return;
            }

            $.post('@Url.Action("DuyetChuyenXe")', {
                chuyenXeId: currentTripId,
                duyet: false,
                lyDoTuChoi: reason
            })
            .done(function(data) {
                if (data.success) {
                    // Update badge
                    const badge = $('#status-badge-' + currentTripId);
                    badge.removeClass('bg-warning').addClass('bg-danger');
                    badge.html('<i class="fas fa-times me-1"></i>Từ chối<br><small class="text-danger">' + reason + '</small>');

                    // Hide approve/reject buttons
                    const row = $('#trip-row-' + currentTripId);
                    row.find('.btn-outline-success, .btn-outline-danger').remove();

                    $('#rejectModal').modal('hide');
                    $('#rejectReason').val('');
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message, 'error');
                }
            })
            .fail(function() {
                showToast('Có lỗi xảy ra khi từ chối chuyến xe', 'error');
            });
        });

        function showToast(message, type) {
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const toast = $(`
                <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);

            if (!$('#toast-container').length) {
                $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }

            $('#toast-container').append(toast);
            toast.toast('show');

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}
