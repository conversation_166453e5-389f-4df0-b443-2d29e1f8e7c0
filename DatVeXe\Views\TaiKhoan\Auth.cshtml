@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON> nhập & <PERSON><PERSON><PERSON> ký";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Đặt Vé Xe</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
            height: 100vh;
            overflow-x: hidden;
            background-color: #f5f5f5;
        }

        .auth-container {
            min-height: 100vh;
            display: flex;
            background-color: #f8f9fa;
        }

        .auth-image-section {
            flex: 1;
            background: linear-gradient(135deg, rgba(10, 38, 64, 0.9) 0%, rgba(10, 38, 64, 0.7) 50%, rgba(108, 171, 221, 0.6) 100%), url('/images/mancity.webp') center/cover no-repeat;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            padding: 3rem 2rem;
            position: relative;
        }

        .auth-image-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(10, 38, 64, 0.85) 0%, rgba(108, 171, 221, 0.3) 100%);
            z-index: 1;
        }

        .auth-image-content {
            position: relative;
            z-index: 2;
            max-width: 80%;
        }

        .auth-content-section {
            flex: 1;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 3rem 2rem;
            min-height: 100vh;
        }

        .auth-logo-container {
            width: 120px;
            height: 120px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            border: 3px solid #6cabdd;
        }

        .auth-logo-icon {
            font-size: 60px;
            color: #6cabdd;
        }

        .auth-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            color: #6cabdd;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .auth-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0 auto 2rem;
            line-height: 1.6;
            max-width: 90%;
        }

        .auth-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .auth-feature {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1rem;
            opacity: 0.9;
        }

        .auth-feature-icon {
            font-size: 1.5rem;
            color: #6cabdd;
        }

        .auth-content-container {
            max-width: 400px;
            width: 100%;
            margin: 0 auto;
        }

        .auth-content-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .auth-content-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #0a2640;
            margin-bottom: 1rem;
        }

        .auth-content-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .auth-options {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .auth-option {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .auth-option:hover {
            border-color: #6cabdd;
            background-color: #fff;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(108, 171, 221, 0.15);
            color: inherit;
            text-decoration: none;
        }

        .auth-option-icon {
            width: 60px;
            height: 60px;
            background-color: rgba(108, 171, 221, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #6cabdd;
            font-size: 1.8rem;
            transition: all 0.3s ease;
        }

        .auth-option:hover .auth-option-icon {
            background-color: #6cabdd;
            color: white;
            transform: scale(1.1);
        }

        .auth-option-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #0a2640;
            margin-bottom: 0.5rem;
        }

        .auth-option-text {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .auth-option-btn {
            display: inline-block;
            background-color: #6cabdd;
            color: #fff;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            border: none;
            cursor: pointer;
        }

        .auth-option-btn:hover {
            background-color: #0a2640;
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .auth-option-btn.outline {
            background-color: transparent;
            border: 2px solid #6cabdd;
            color: #6cabdd;
        }

        .auth-option-btn.outline:hover {
            background-color: #6cabdd;
            color: #fff;
        }

        .auth-footer {
            text-align: center;
            padding: 2rem;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .auth-footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .auth-footer-link {
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s;
            font-size: 0.9rem;
        }

        .auth-footer-link:hover {
            color: #6cabdd;
            text-decoration: underline;
        }

        .auth-footer-text {
            color: #6c757d;
            font-size: 0.85rem;
            margin: 0;
        }

        .auth-bus-animation {
            position: absolute;
            bottom: 30px;
            left: -100px;
            font-size: 30px;
            color: rgba(255, 255, 255, 0.4);
            animation: drive 20s linear infinite;
        }

        @@keyframes drive {
            0% {
                left: -100px;
                transform: rotate(0deg);
            }
            50% {
                transform: rotate(5deg);
            }
            100% {
                left: calc(100% + 100px);
                transform: rotate(0deg);
            }
        }

        @@media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
            }

            .auth-image-section {
                min-height: 40vh;
                padding: 2rem 1rem;
            }

            .auth-content-section {
                padding: 2rem 1rem;
                min-height: 60vh;
            }

            .auth-title {
                font-size: 2rem;
            }

            .auth-content-title {
                font-size: 2rem;
            }

            .auth-features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .auth-footer-links {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- Left Side - Image Section -->
        <div class="auth-image-section">
            <div class="auth-image-content">
                <div class="auth-logo-container">
                    <i class="bi bi-bus-front auth-logo-icon"></i>
                </div>
                <h1 class="auth-title">ĐẶT VÉ XE</h1>
                <p class="auth-subtitle">
                    Hệ thống đặt vé xe khách trực tuyến hiện đại nhất Việt Nam.
                    Đặt vé nhanh chóng, thanh toán an toàn, trải nghiệm hành trình tuyệt vời.
                </p>

                <div class="auth-features">
                    <div class="auth-feature">
                        <i class="bi bi-shield-check auth-feature-icon"></i>
                        <span>Bảo mật cao</span>
                    </div>
                    <div class="auth-feature">
                        <i class="bi bi-clock auth-feature-icon"></i>
                        <span>Đặt vé 24/7</span>
                    </div>
                    <div class="auth-feature">
                        <i class="bi bi-credit-card auth-feature-icon"></i>
                        <span>Thanh toán đa dạng</span>
                    </div>
                    <div class="auth-feature">
                        <i class="bi bi-headset auth-feature-icon"></i>
                        <span>Hỗ trợ tận tình</span>
                    </div>
                </div>
            </div>

            <div class="auth-bus-animation">
                <i class="bi bi-bus-front"></i>
            </div>
        </div>

        <!-- Right Side - Content Section -->
        <div class="auth-content-section">
            <div class="auth-content-container">
                <div class="auth-content-header">
                    <h2 class="auth-content-title">Chào mừng!</h2>
                    <p class="auth-content-subtitle">Chọn một tùy chọn để tiếp tục</p>
                </div>

                <div class="auth-options">
                    <a href="/TaiKhoan/DangNhap" class="auth-option">
                        <div class="auth-option-icon">
                            <i class="bi bi-box-arrow-in-right"></i>
                        </div>
                        <h3 class="auth-option-title">Đăng nhập</h3>
                        <p class="auth-option-text">Đã có tài khoản? Đăng nhập để đặt vé và quản lý chuyến đi của bạn.</p>
                        <span class="auth-option-btn">Đăng nhập ngay</span>
                    </a>

                    <a href="/TaiKhoan/DangKy" class="auth-option">
                        <div class="auth-option-icon">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <h3 class="auth-option-title">Đăng ký</h3>
                        <p class="auth-option-text">Chưa có tài khoản? Tạo tài khoản mới để trải nghiệm dịch vụ tuyệt vời.</p>
                        <span class="auth-option-btn outline">Đăng ký ngay</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <div class="auth-footer-links">
            <a href="/" class="auth-footer-link">Trang chủ</a>
            <a href="#" class="auth-footer-link">Chính sách bảo mật</a>
            <a href="#" class="auth-footer-link">Điều khoản sử dụng</a>
            <a href="#" class="auth-footer-link">Trợ giúp & Hỗ trợ</a>
            <a href="#" class="auth-footer-link">Liên hệ</a>
        </div>
        <p class="auth-footer-text">&copy; 2025 Đặt Vé Xe. Tất cả các quyền được bảo lưu.</p>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
</body>
</html>
